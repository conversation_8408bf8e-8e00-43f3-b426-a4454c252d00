# Shield Frontend

### 🏠 [Homepage](https://shield-dash.aplazo.mx)

## Overview

Shield Frontend is an Angular-based web application built with modern technologies. This README provides instructions for setting up and running the project in different environments.

## Prerequisites

- `node >= 22.13.0`
- `angular >= 20.0.0`
- `docker >= latest` (optional, for containerized development)

## Getting Started

### 1. Environment Setup

#### Setting up the .env file

The application requires environment variables to function properly. Create a `.env` file in the project root:

```sh
# Copy the example file (if available)
cp .env.example .env

# Or create a new .env file with the following variables
```

Required environment variables:

| Variable                       | Description                                                  | Example                                                 |
| ------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------- |
| `AUTH_TOKEN`                   | Token for accessing private npm packages from Nexus registry | `NpmToken.xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`         |
| `ENV`                          | Environment target (`stg`, or `prod`)                        | `stg`                                                   |
| `NODE_ENV`                     | Node environment                                             | `development`                                           |
| `NG_APP_API_BASE_URL`          | Backend API URL                                              | `https://shield-back.aplazo.net`                        |
| `NG_APP_GOOGLE_CLIENT_ID`      | Google authentication client ID                              | `your-client-id.apps.googleusercontent.com`             |
| `NG_APP_BIFROST_URL`           | URL for Bifrost service integration                          | `https://bifrost-mpay.aplazo.net`                       |
| `NG_APP_I18N_URL`              | URL for internationalization service                         | `https://aplazo.github.io/front.in18/control-tower/dev` |
| `NG_APP_LANDING_URL`           | URL for the landing page                                     | `https://aplazo.net`                                    |
| `NG_APP_GATEWAY_URL`           | URL for API gateway service                                  | `https://core.aplazo.net`                               |
| `NG_APP_PCUSTOM_CARD_URL`      | URL for custom card service                                  | `https://pcustomcard.aplazo.net`                        |
| `NG_APP_FEATURE_FLAGS_API_KEY` | API key for feature flags service                            | `an-api-key-from-provider`                              |
| `NG_APP_FEATURE_FLAGS_ENV`     | Environment for feature flags service                        | `staging`                                               |

#### Generating AUTH_TOKEN for Nexus Registry

To access private packages from the Nexus registry, you need to generate an AUTH_TOKEN. Follow these steps:

1. Run the following command in your terminal:

   ```sh
   npm login --registry=https://nexus.aplazo.dev/repository/npm-group-aplazo/
   ```

2. Use the following generic credentials:
   - Username: `readall`
   - Password: `Aplazo@123`

   > **Note:** It is always recommended to use your own credentials if you have them.

3. After successful login, open your user's `.npmrc` file and copy the authToken:

   ```sh
   # For macOS/Linux
   cat ~/.npmrc

   # Example output:
   //nexus.aplazo.dev/repository/npm-group-aplazo/:_authToken=NpmToken.912837492837492734.92834238947
   ```

   You need to copy the value after `_authToken=` (in this example, it would be `NpmToken.912837492837492734.92834238947`).

4. Add the token to your shell configuration file:

   ```sh
   # For Bash
   echo "export AUTH_TOKEN=your-copied-token" >> ~/.bashrc
   source ~/.bashrc

   # For Zsh
   echo "export AUTH_TOKEN=your-copied-token" >> ~/.zshrc
   source ~/.zshrc
   ```

### 2. Development Options

You can run the application in three different ways:

#### Option 1: Local Development with Docker (Recommended)

This method provides a consistent development environment with hot reloading:

```sh
# Build the development container
docker-compose build shield_local_dev

# Run the development container
docker-compose up shield_local_dev
```

The application will be available at http://localhost:4200 with hot module replacement enabled.

#### Option 1a: Development with Dev Containers (VS Code)

If you're using Visual Studio Code as your primary IDE, you can leverage Dev Containers for an enhanced development experience:

1. Install the "Dev Containers" extension in VS Code
2. Open the project folder in VS Code
3. Click on the green button in the bottom-left corner or use the Command Palette (F1) and select "Dev Containers: Reopen in Container"

This approach provides a fully configured development environment with all dependencies pre-installed, consistent tooling across team members, and seamless integration with VS Code's features.

#### Option 2: Local Development with npm

If you prefer not to use Docker:

```sh
# Set the AUTH_TOKEN environment variable
export AUTH_TOKEN=your_token

# Install dependencies
npm install

# Start the development server
node --run dev:stg
```

#### Option 3: Production-like Environment (No HMR)

To test the application in a production-like environment:

```sh
# Build the container
docker-compose build shield_local

# Run the container
docker-compose up shield_local
```

The application will be available at http://localhost:4200 without hot module replacement.

### 3. Running Tests

To run the test suite:

```sh
# Build the test container
docker-compose build shield_local_test

# Run tests
docker-compose run --rm shield_local_test
```

## Project Structure

The project follows standard Angular architecture with the following key components:

- `src/` - Application source code
- `Dockerfile` - Production build configuration
- `Dockerfile.dev` - Development environment configuration
- `Dockerfile.test` - Test environment configuration
- `docker-compose.yml` - Container orchestration configuration

## Deployment

The project uses Jenkins for CI/CD with the following pipeline:

1. **Build & Test**: Runs unit tests and static code analysis
2. **Version Management**: Handles semantic versioning on the master branch
3. **Deployment**: Builds Docker images and deploys to staging and production environments

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Ensure your `AUTH_TOKEN` is valid and properly set in the `.env` file
2. **Docker Issues**: Make sure Docker is running and you have sufficient permissions
3. **Dependency Issues**: Try removing `node_modules` and reinstalling dependencies

### Getting Help

If you encounter issues not covered here, please contact the development team.

## Author

👤 **Aplazo**

- Website: https://aplazo.mx
- Github: [@aplazo](https://github.com/aplazo)
