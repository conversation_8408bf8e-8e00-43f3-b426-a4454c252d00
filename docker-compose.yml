version: '3'
services:
  tagged1:
    container_name: tagged1
    image: ${TAG_1}
    build:
      context: .
      dockerfile: Dockerfile
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env
    ports:
      - 80:80

  tagged2:
    container_name: tagged2
    image: ${TAG_2}
    build:
      context: .
      dockerfile: Dockerfile
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env
    ports:
      - 80:80

  shield_local:
    container_name: shieldLocal
    image: shield_local
    build:
      context: .
      dockerfile: Dockerfile
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env
    ports:
      - 4200:80

  shield_local_test:
    container_name: shieldLocalTest
    image: shield_local_test
    build:
      context: .
      dockerfile: Dockerfile.test
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env
    ports:
      - 4200:4200
    volumes:
      - coverage_vol:/usr/src/app/coverage

  shield_local_dev:
    container_name: shieldLocalDev
    image: shield_local_dev
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
        ENV: ${ENV}
    env_file:
      - .env
    ports:
      - 4200:4200
    volumes:
      - '/usr/src/app/node_modules'
      - '.:/usr/src/app'

volumes:
  node_modules:
  coverage_vol:
