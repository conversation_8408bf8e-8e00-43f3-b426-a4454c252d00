{"name": "@aplazo/control-tower-dashboard", "version": "1.29.1", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://aplazo.mx", "email": "<EMAIL>"}, "homepage": "https://shield-dash.aplazo.mx", "repository": {"type": "git", "url": "https://github.com/aplazo/angular.control-tower-dashboard"}, "engines": {"node": ">=22.13.0", "angular": ">=20.0.0"}, "scripts": {"ng": "ng", "dev": "ng serve --configuration development", "dev:stg": "ng serve --configuration stage --host 0.0.0.0", "dev:prod": "ng serve --configuration production --host 0.0.0.0", "build": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test --no-watch --no-progress --browsers=ChromeHeadlessNoSandbox --code-coverage", "test:watch": "ng test", "lint": "ng lint", "prepare": "husky", "release": "semantic-release"}, "private": true, "dependencies": {"@angular/animations": "^20.0.0", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/platform-browser": "^20.0.0", "@angular/platform-browser-dynamic": "^20.0.0", "@angular/router": "^20.0.0", "@angular/service-worker": "^20.0.0", "@aplazo/front-social-sso": "~3.0.0", "@aplazo/i18n": "~3.0.0", "@aplazo/merchant": "~3.0.0", "@aplazo/shared-ui": "~3.0.0", "@aplazo/ui-icons": "~3.0.0", "@auth0/angular-jwt": "~5.2.0", "@date-fns/utc": "~2.1.0", "@jsverse/transloco": "~7.4.0", "@ngneat/dialog": "~5.1.0", "@statsig/angular-bindings": "~3.16.2", "@statsig/session-replay": "~3.16.2", "@statsig/web-analytics": "~3.16.2", "air-datepicker": "~3.5.3", "date-fns": "~4.1.0", "date-fns-tz": "^3.1.3", "filesize": "^10.1.4", "nanoid": "5.0.8", "ngx-mask": "~17.0.7", "ngx-toastr": "~19.0.0", "rxjs": "~7.8.0", "tslib": "^2.6.0", "xlsx": "~0.18.5", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-eslint/builder": "^20.0.0", "@angular-eslint/eslint-plugin": "^20.0.0", "@angular-eslint/eslint-plugin-template": "^20.0.0", "@angular-eslint/schematics": "^20.0.0", "@angular-eslint/template-parser": "^20.0.0", "@angular-devkit/build-angular": "^20.0.0", "@angular/cli": "^20.0.0", "@angular/compiler-cli": "^20.0.0", "@angular/language-service": "^20.0.0", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@ngx-env/builder": "^19.0.0", "@playwright/test": "^1.42.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^22.13.1", "@typescript-eslint/eslint-plugin": "6.13.1", "@typescript-eslint/parser": "6.13.1", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^9.1.7", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-eslint": "^16.1.2", "semantic-release": "^24.2.3", "tailwindcss": "3.4.15", "typescript": "~5.8.3"}}