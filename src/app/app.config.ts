import { registerLocaleData } from '@angular/common';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import locale from '@angular/common/locales/es-MX';
import {
  ApplicationConfig,
  importProvidersFrom,
  isDevMode,
} from '@angular/core';
import { provideNoopAnimations } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';
import { provideServiceWorker } from '@angular/service-worker';
import { provideGoogleSSO } from '@aplazo/front-social-sso/google';
import { provideI18N } from '@aplazo/i18n';
import {
  provideBrowserUtils,
  provideConnectionStatus,
  provideCustomErrorHandler,
  provideLoader,
  provideNotifier,
  provideRedirecter,
  provideTemporal,
  provideUseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { provideScriptRenderer } from '@aplazo/shared-ui/script-renderer';
import { STATSIG_INIT_CONFIG } from '@statsig/angular-bindings';
import { StatsigSessionReplayPlugin } from '@statsig/session-replay';
import { StatsigAutoCapturePlugin } from '@statsig/web-analytics';
import { provideEnvironmentNgxMask } from 'ngx-mask';
import { ToastNoAnimationModule } from 'ngx-toastr';
import packageJson from '../../package.json';
import { routes } from './app.routes';
import { provideCampaignsRepositories } from './features/campaigns/infra/config/providers';
import {
  provideEnvironmentVariables,
  shieldEnvs,
} from './features/core/infra/config/environments';
import { tokenInterceptor } from './features/core/infra/interceptors/token.interceptor';
import { provideServiceWorkerUpdater } from './features/core/infra/services/service-worker-updater.service';
import { provideUserStore } from './features/login/infra/config/providers';
import { provideMenuStore } from './features/shared/infra/config/providers';

registerLocaleData(locale, 'es-MX');

const settings = {
  sdkKey: shieldEnvs.featureFlagsApiKey,
  user: {
    appVersion: packageJson.version,
    userAgent: navigator.userAgent,
    locale: 'es-MX',
    custom: {
      appName: 'shield',
    },
  },
  options: {
    environment: {
      tier: shieldEnvs.featureFlagsEnv,
    },
    plugins: [new StatsigAutoCapturePlugin(), new StatsigSessionReplayPlugin()],
  },
};

export const appConfig: ApplicationConfig = {
  providers: [
    provideNoopAnimations(),
    provideRouter(routes),
    provideHttpClient(withInterceptors([tokenInterceptor])),
    {
      provide: STATSIG_INIT_CONFIG,
      useValue: settings,
    },
    provideScriptRenderer(),
    provideGoogleSSO({
      clientId: shieldEnvs.googleClientId,
    }),
    provideLoader(),
    provideNotifier(),
    provideRedirecter(),
    provideUserStore(),
    provideMenuStore(),
    importProvidersFrom(
      ToastNoAnimationModule.forRoot({
        closeButton: true,
        timeOut: 5000,
      })
    ),
    provideI18N({
      remoteUrl: shieldEnvs.i18nUrl,
      fallbackLocalUrl: '/assets/i18n',
    }),
    provideEnvironmentVariables(),
    provideUseCaseErrorHandler(),
    provideEnvironmentNgxMask(),
    provideTemporal(),
    provideCampaignsRepositories(),
    provideServiceWorker('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerWhenStable:30000',
    }),
    provideConnectionStatus({
      notifierText: {
        title: 'Sin conexión a internet',
        message:
          'Algunas o todas las funcionalidades pueden verse afectadas. Verifique la conexión y vuelva a intentar',
      },
      position: 'toast-bottom-left',
    }),
    provideBrowserUtils(),
    provideCustomErrorHandler('chunkFile', 'offlineToastr'),
    provideServiceWorkerUpdater({
      pollInterval: 5 * 1000,
      dialogTitle: 'Una nueva versión está disponible',
      dialogMessage: '¿Quieres actualizar ahora?',
    }),
  ],
};
