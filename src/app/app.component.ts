import { AsyncPipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { LoaderService } from '@aplazo/merchant/shared';
import { AplazoPillLoaderComponent } from '@aplazo/shared-ui/loader';

@Component({
  selector: 'app-root',
  imports: [AsyncPipe, RouterOutlet, AplazoPillLoaderComponent],
  template: `<router-outlet></router-outlet>

    <aplz-ui-pill-loader
      [loading]="(isLoading$ | async) === true"></aplz-ui-pill-loader> `,
})
export class AppComponent {
  readonly #loaderService = inject(LoaderService);

  isLoading$ = this.#loaderService.isLoading$;
}
