import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import { invalidApiBaseUrlError } from '../../../shared/domain/messages-text';
import { PaymentReportRepositoryDto } from '../../application/dtos/report-request.dto';
import { PaymentReportRepository } from '../../domain/repositories/payment-report.repository';

@Injectable({ providedIn: 'root' })
export class PaymentsSendReportWithHttpRepository
  implements
    PaymentReportRepository<PaymentReportRepositoryDto, Observable<void>>
{
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);

  getReport(req: PaymentReportRepositoryDto): Observable<void> {
    if (!this.#environment.bifrostUrl) {
      throw new RuntimeMerchantError(
        invalidApiBaseUrlError,
        'ReportWithHttpRepository::invalidApiUrl'
      );
    }

    return this.#http.get<void>(
      `${this.#environment.bifrostUrl}/api/v1/merchant-payment/finance/send-mail`,
      {
        params: {
          emails: req.emails,
          type: req.type,
          paymentDate: req.date,
        },
      }
    );
  }
}
