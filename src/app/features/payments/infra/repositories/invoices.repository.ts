import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import {
  PaymentInvoicesRepositoryRequest,
  PaymentInvoiceSummaryResponse,
} from '../../domain/entities/invoices';
import { PaymentInvoicesRepository } from '../../domain/repositories/invoices.repository';

@Injectable({
  providedIn: 'root',
})
export class PaymentInvoicesWithHttp implements PaymentInvoicesRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #baseUrl = this.#environment.apiBaseUrl;

  getSummary(
    dateRange: PaymentInvoicesRepositoryRequest
  ): Observable<PaymentInvoiceSummaryResponse> {
    let params = new HttpParams();
    params = params.set('start', dateRange.start);
    params = params.set('end', dateRange.end);

    return this.#http.get<PaymentInvoiceSummaryResponse>(
      `${this.#baseUrl}/api/v1/invoice/range/status`,
      { params }
    );
  }
}
