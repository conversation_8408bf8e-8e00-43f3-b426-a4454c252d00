import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  RawDateYearFirstWithHyphen,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import { invalidApiBaseUrlError } from '../../../shared/domain/messages-text';
import { PaymentSummaryResponse } from '../../domain/entities/summary-response';
import { PaymentSummaryRepository } from '../../domain/repositories/payment-summary.repository';

@Injectable({
  providedIn: 'root',
})
export class PaymentsSummaryWithHttpRepository
  implements
    PaymentSummaryRepository<
      RawDateYearFirstWithHyphen,
      Observable<PaymentSummaryResponse>
    >
{
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);

  getByDate(
    date: RawDateYearFirstWithHyphen
  ): Observable<PaymentSummaryResponse> {
    if (!this.#environment.apiBaseUrl) {
      throw new RuntimeMerchantError(
        invalidApiBaseUrlError,
        'SummaryWithHttpRepository::invalidApiUrl'
      );
    }

    return this.#http.get<PaymentSummaryResponse>(
      `${this.#environment.apiBaseUrl}/api/v1/finance/merchant-payment/data-summary/payment-date/${date}`
    );
  }
}
