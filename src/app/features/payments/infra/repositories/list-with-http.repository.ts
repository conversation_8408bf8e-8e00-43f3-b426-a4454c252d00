import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import { PaymentListRepositoryDto } from '../../application/dtos/list-request.dto';
import {
  PaymentInvoicesRepositoryRequest,
  PaymentInvoicesStatusResponse,
} from '../../domain/entities/invoices';
import { PaymentListResponse } from '../../domain/entities/list-response';
import { PaymentListRepository } from '../../domain/repositories/payment-list.repository';

@Injectable({
  providedIn: 'root',
})
export class PaymentsListWithHttpRepository implements PaymentListRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);

  getList(req: PaymentListRepositoryDto): Observable<PaymentListResponse> {
    let params = new HttpParams()
      .set('pageNum', String(req.pageNum))
      .set('pageSize', String(req.pageSize));

    if (req.frequency !== null && req.frequency !== undefined) {
      params = params.set('frequency', req.frequency.toLocaleUpperCase());
    }

    if (req.paymentStatus !== null && req.paymentStatus !== undefined) {
      params = params.set('status', req.paymentStatus.toLocaleUpperCase());
    }

    if (req.invoiceStatus !== null && req.invoiceStatus !== undefined) {
      params = params.set(
        'invoiceStatus',
        req.invoiceStatus.toLocaleUpperCase()
      );
    }

    if (req.paymentId !== null && req.paymentId !== undefined) {
      params = params.set('paymentId', String(req.paymentId));
    }

    if (req.merchantId !== null && req.merchantId !== undefined) {
      params = params.set('merchantId', String(req.merchantId));
    }

    if (req.name !== null && req.name !== undefined) {
      params = params.set('name', String(req.name));
    }

    return this.#http.get<PaymentListResponse>(
      `${this.#environment.apiBaseUrl}/api/v1/finance/merchant-payment/data-detail/payment-date/${req.date}`,
      { params }
    );
  }

  getInvoiceStatusList(
    daterange: PaymentInvoicesRepositoryRequest
  ): Observable<PaymentInvoicesStatusResponse[]> {
    let params = new HttpParams()
      .set('start', daterange.start)
      .set('end', daterange.end);

    if (daterange.frequency) {
      params = params.set('frequency', daterange.frequency.toLocaleUpperCase());
    }

    if (
      daterange.paymentStatus !== null &&
      daterange.paymentStatus !== undefined
    ) {
      params = params.set(
        'status',
        daterange.paymentStatus.toLocaleUpperCase()
      );
    }

    if (
      daterange.invoiceStatus !== null &&
      daterange.invoiceStatus !== undefined
    ) {
      params = params.set(
        'invoiceStatus',
        daterange.invoiceStatus.toLocaleUpperCase()
      );
    }

    if (daterange.paymentId !== null && daterange.paymentId !== undefined) {
      params = params.set('id', String(daterange.paymentId));
    }

    if (daterange.merchantId !== null && daterange.merchantId !== undefined) {
      params = params.set('merchantId', String(daterange.merchantId));
    }

    if (daterange.name !== null && daterange.name !== undefined) {
      params = params.set('name', String(daterange.name));
    }

    return this.#http.get<PaymentInvoicesStatusResponse[]>(
      `${this.#environment.apiBaseUrl}/api/v1/invoice/range`,
      { params }
    );
  }
}
