import { HttpClient, HttpEvent, HttpRequest } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import { ReceiptHistoryDto } from '../../application/dtos/upload-receipts.dto';
import { ReceiptsRepository } from '../../domain/repositories/receipts.repository';

@Injectable({ providedIn: 'root' })
export class ReceiptsWithHttpRepository implements ReceiptsRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #apiUrl = this.#environment.apiBaseUrl;

  uploadFile(identifier: string, data: FormData): Observable<HttpEvent<any>> {
    return this.#http.post(
      `${this.#apiUrl}/api/v1/finance/merchant-payment/receipt/upload`,
      data,
      {
        observe: 'events',
        responseType: 'json',
        reportProgress: true,
        params: { identifier },
      }
    );
  }

  retrieveEvents(): Observable<HttpEvent<any>> {
    const request = new HttpRequest(
      'GET',
      `${this.#apiUrl}/api/v1/shield/sse/v1/merchant-receipt-notify`,
      {
        observe: 'events',
        responseType: 'text',
        reportProgress: true,
      }
    );

    return this.#http.request(request);
  }

  postReceiptHistory(req: ReceiptHistoryDto): Observable<any> {
    return this.#http.post(
      `${this.#apiUrl}/api/v1/finance/merchant-payment/receipt-history`,
      req,
      {
        responseType: 'json',
        reportProgress: true,
      }
    );
  }
}
