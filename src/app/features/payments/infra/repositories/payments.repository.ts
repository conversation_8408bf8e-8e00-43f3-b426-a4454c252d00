import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import {
  InvoiceGenerationByIDSResponse,
  InvoiceGenerationRepositoryRequest,
  InvoiceGenerationResponse,
} from '../../domain/entities/payment';
import { PaymentsRepository } from '../../domain/repositories/payments.repository';

@Injectable()
export class PaymentsWithHttp implements PaymentsRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #baseUrl = this.#environment.apiBaseUrl;

  generateInvoices(
    range: InvoiceGenerationRepositoryRequest
  ): Observable<InvoiceGenerationResponse> {
    return this.#http.post<InvoiceGenerationResponse>(
      `${this.#baseUrl}/api/v1/invoice/generate/range`,
      range
    );
  }

  generateInvoicesByIds(
    ids: number[]
  ): Observable<InvoiceGenerationByIDSResponse[]> {
    return this.#http.post<InvoiceGenerationByIDSResponse[]>(
      `${this.#baseUrl}/api/v1/invoice/payment/multiple/status`,
      ids
    );
  }
}
