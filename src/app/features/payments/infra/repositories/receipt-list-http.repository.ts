import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import { invalidApiBaseUrlError } from '../../../shared/domain/messages-text';
import { ReceiptHistoryRequest } from '../../application/dtos/upload-receipts.dto';
import { UploadReceiptsResponse } from '../../../../../app/features/payments/domain/entities/list-response';
import { ReceiptListRepository } from '../../domain/repositories/receipt-list.repository';

@Injectable({
  providedIn: 'root',
})
export class ReceiptListWithHttpRepository
  implements
    ReceiptListRepository<
      ReceiptHistoryRequest,
      Observable<UploadReceiptsResponse>
    >
{
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);

  getList(req: ReceiptHistoryRequest): Observable<UploadReceiptsResponse> {
    if (!this.#environment.apiBaseUrl) {
      throw new RuntimeMerchantError(
        invalidApiBaseUrlError,
        'ListWithHttpRepository::invalidApiUrl'
      );
    }

    return this.#http.get<UploadReceiptsResponse>(
      `${this.#environment.apiBaseUrl}/api/v1/finance/merchant-payment/receipt-history/creation-date-after`,
      {
        params: {
          pageNum: String(req.pageNum),
          pageSize: String(req.pageSize),
        },
      }
    );
  }
}
