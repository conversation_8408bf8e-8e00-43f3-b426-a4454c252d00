import { Component, inject } from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { DialogRef } from '@ngneat/dialog';

@Component({
  selector: 'app-confirm-report',
  imports: [AplazoButtonComponent, AplazoCardComponent],
  template: `
    <aplz-ui-card size="full">
      @if (data) {
        <h2 class="text-center text-lg font-medium text-pretty">
          {{ data.title }}
        </h2>

        <p class="mt-6 text-center">
          {{ data.description }}
        </p>

        <div
          class="flex items-center justify-center flex-wrap mt-4 gap-4 w-full">
          @for (btn of data.buttons; track btn; let odd = $odd) {
            <button
              aplzButton
              [aplzColor]="odd ? 'aplazo' : 'accent'"
              aplzAppearance="solid"
              size="md"
              (click)="close(btn.id)">
              {{ btn.label }}
            </button>
          }
        </div>
      } @else {
        <h2 class="text-center text-lg font-medium text-pretty">
          No se pudo cargar el texto del modal.
        </h2>
      }
    </aplz-ui-card>
  `,
})
export class ConfirmReportComponent {
  readonly #dialogRef: DialogRef<{
    title: string;
    description: string;
    buttons: { label: string; id: string }[];
  }> = inject(DialogRef);

  readonly data = this.#dialogRef.data;

  close(type: string) {
    this.#dialogRef.close(type);
  }
}
