import { Component, computed, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { OnlyNumbersDirective } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { DialogRef } from '@ngneat/dialog';

@Component({
  selector: 'app-invoices-by-id',
  template: `
    <aplz-ui-card size="full">
      <h2 class="text-center text-lg font-medium text-pretty">
        Ingrese ID y presione enter
      </h2>
      <h2 class="text-center text-base font-light text-pretty">
        cuando termine de agregar "IDS" de clic en guardar
      </h2>
      <div class="flex mt-10 items-center justify-center">
        <label
          for="ids"
          class="flex flex-wrap gap-2 w-60 border rounded py-4 px-2 focus-within:ring-2 focus-visible:ring-2 focus-within:ring-aplazo-aplazo focus-visible:ring-aplazo-aplazo">
          @for (item of ids(); track item) {
            <span
              class="flex-grow-0 flex-shrink-0 bg-dark-background text-dark-primary border border-current text-sm font-medium me-2 px-2.5 py-0.5 rounded">
              {{ item }}
            </span>
          }

          <input
            class="border-none outline-none rounded bg-transparent flex-grow flex-shrink-0"
            type="text"
            name="ids"
            id="ids"
            [(ngModel)]="newId"
            aplazoOnlyNumbers
            (keyup.enter)="addOne()"
            (keyup.control.enter)="create()" />
        </label>
      </div>

      <div class="flex justify-end mt-12 gap-4">
        <button
          aplzButton
          aplzAppearance="stroked"
          aplzColor="light"
          size="md"
          (click)="cancel()">
          Cancelar
        </button>

        <button
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          (click)="create()">
          Guardar
        </button>
      </div>
    </aplz-ui-card>
  `,
  imports: [
    FormsModule,
    AplazoCardComponent,
    AplazoButtonComponent,
    OnlyNumbersDirective,
  ],
})
export class InvoicesByIdsComponent {
  readonly #dialogRef = inject(DialogRef);

  readonly #ids = signal<number[]>([]);

  readonly ids = computed(() => this.#ids());

  newId: number | null = null;

  addOne() {
    if (this.newId == null || isNaN(+this.newId)) {
      return;
    }

    this.#ids.update(ids => Array.from(new Set([...ids, Number(this.newId)])));
    this.newId = null;
  }

  cancel() {
    this.#dialogRef.close();
  }

  create() {
    this.addOne();

    this.#dialogRef.close({
      confirmation: true,
      ids: this.ids(),
    });
  }
}
