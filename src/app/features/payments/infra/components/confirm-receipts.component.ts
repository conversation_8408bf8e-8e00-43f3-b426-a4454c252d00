import { Component, inject } from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoDatepickerComponent } from '@aplazo/shared-ui/datepicker';
import { DialogRef } from '@ngneat/dialog';

@Component({
  standalone: true,
  selector: 'app-confirm-receipts',
  template: ` <aplz-ui-card> </aplz-ui-card> `,
  imports: [
    AplazoCardComponent,
    AplazoButtonComponent,
    AplazoDatepickerComponent,
  ],
})
export class ConfirmReceiptsComponent {
  readonly #dialogRef = inject(DialogRef);
}
