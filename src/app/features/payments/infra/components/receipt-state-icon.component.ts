import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  ViewEncapsulation,
} from '@angular/core';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  iconArrowPath,
  iconCheckCircle,
  iconQuestionMarkCircle,
  iconUploadTray,
  iconXMark,
} from '@aplazo/ui-icons';
import { UploadReceiptState } from '../../domain/entities/receipts';

@Component({
  selector: 'app-receipt-state-icon',
  template: `
    @switch (state()) {
      @case ('loading') {
        <span class="text-special-info">
          <aplz-ui-icon
            name="arrow-path"
            size="sm"
            [spin]="true"></aplz-ui-icon>
        </span>
      }
      @case ('loaded') {
        <span class="text-special-info">
          <aplz-ui-icon name="upload-tray" size="sm"></aplz-ui-icon>
        </span>
      }
      @case ('uploadError') {
        <span class="text-special-danger">
          <aplz-ui-icon name="x-mark" size="sm"></aplz-ui-icon>
        </span>
      }
      @case ('processingError') {
        <span class="text-special-danger">
          <aplz-ui-icon name="x-mark" size="sm"></aplz-ui-icon>
        </span>
      }
      @case ('ready') {
        <span class="text-special-success">
          <aplz-ui-icon name="check-circle" size="sm"></aplz-ui-icon>
        </span>
      }
      @default {
        <aplz-ui-icon name="question-mark-circle" size="sm"></aplz-ui-icon>
      }
    }
  `,
  imports: [AplazoIconComponent],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReceiptStateIconComponent {
  readonly #iconRegister = inject(AplazoIconRegistryService);

  state = input<UploadReceiptState>('idle');

  constructor() {
    this.#iconRegister.registerIcons([
      iconXMark,
      iconArrowPath,
      iconQuestionMarkCircle,
      iconCheckCircle,
      iconUploadTray,
    ]);
  }
}
