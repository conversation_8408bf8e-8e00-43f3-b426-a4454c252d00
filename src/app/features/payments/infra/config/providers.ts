import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { GenerateInvoicesByIdsUseCase } from '../../application/usecases/generate-invoices-by-ids.usecase';
import { GenerateInvoicesUseCase } from '../../application/usecases/generate-invoices.usecase';
import { GetInvoicesSummaryUseCase } from '../../application/usecases/get-invoices-summary.usecase';
import { GetListReceiptUseCase } from '../../application/usecases/get-list-receipts.usecase';
import { GetListUseCase } from '../../application/usecases/get-list.usecase';
import { GetSummaryUseCase } from '../../application/usecases/get-summary.usecase';
import { SendReportUseCase } from '../../application/usecases/send-report.usecase';
import { UploadReceiptsUseCase } from '../../application/usecases/upload-receipts.usecase';
import { PaymentInvoicesRepository } from '../../domain/repositories/invoices.repository';
import { PaymentListRepository } from '../../domain/repositories/payment-list.repository';
import { PaymentReportRepository } from '../../domain/repositories/payment-report.repository';
import { PaymentSummaryRepository } from '../../domain/repositories/payment-summary.repository';
import { PaymentsRepository } from '../../domain/repositories/payments.repository';
import { ReceiptListRepository } from '../../domain/repositories/receipt-list.repository';
import { ReceiptsRepository } from '../../domain/repositories/receipts.repository';
import { PaymentInvoicesWithHttp } from '../repositories/invoices.repository';
import { PaymentsListWithHttpRepository } from '../repositories/list-with-http.repository';
import { PaymentsWithHttp } from '../repositories/payments.repository';
import { ReceiptListWithHttpRepository } from '../repositories/receipt-list-http.repository';
import { ReceiptsWithHttpRepository } from '../repositories/receipts-with-http.repository';
import { PaymentsSendReportWithHttpRepository } from '../repositories/send-report-with-http.repository';
import { PaymentsSummaryWithHttpRepository } from '../repositories/summary-with-http.repository';
import { HandleInvoicesByIdsGenerationService } from '../services/handle-all-invoices-by-ids-generation.service';
import { HandleAllInvoicesGenerationService } from '../services/handle-all-invoices-generation.service';

export function providePayments(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: PaymentsRepository,
      useClass: PaymentsWithHttp,
    },
    {
      provide: PaymentSummaryRepository,
      useClass: PaymentsSummaryWithHttpRepository,
    },
    {
      provide: PaymentListRepository,
      useClass: PaymentsListWithHttpRepository,
    },
    {
      provide: PaymentReportRepository,
      useClass: PaymentsSendReportWithHttpRepository,
    },
    {
      provide: PaymentInvoicesRepository,
      useClass: PaymentInvoicesWithHttp,
    },
    GenerateInvoicesUseCase,
    GenerateInvoicesByIdsUseCase,
    GetInvoicesSummaryUseCase,
    GetListUseCase,
    GetSummaryUseCase,
    SendReportUseCase,
    HandleAllInvoicesGenerationService,
    HandleInvoicesByIdsGenerationService,
  ]);
}

export function provideReceipts(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: ReceiptsRepository,
      useClass: ReceiptsWithHttpRepository,
    },
    {
      provide: ReceiptListRepository,
      useClass: ReceiptListWithHttpRepository,
    },
    UploadReceiptsUseCase,
    GetListReceiptUseCase,
  ]);
}
