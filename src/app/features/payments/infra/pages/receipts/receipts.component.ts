import { AsyncPipe } from '@angular/common';
import {
  Component,
  computed,
  inject,
  OnD<PERSON>roy,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { NotifierService, RawDateDayFirst } from '@aplazo/merchant/shared';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFileUploaderComponent } from '@aplazo/shared-ui/file-uploader';
import { AplazoFormDatepickerComponent } from '@aplazo/shared-ui/forms';
import { AplazoCommonMessageComponents } from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleProgressBarComponent } from '@aplazo/shared-ui/simple-progress';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoStepperComponents } from '@aplazo/shared-ui/stepper';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  combineLatestWith,
  EMPTY,
  filter,
  lastValueFrom,
  map,
  shareReplay,
  startWith,
  Subject,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { GetListReceiptUseCase } from '../../../application/usecases/get-list-receipts.usecase';
import { UploadReceiptsUseCase } from '../../../application/usecases/upload-receipts.usecase';
import {
  areAllStateTracked,
  UploadReceiptState,
} from '../../../domain/entities/receipts';
import { ReceiptStateIconComponent } from '../../components/receipt-state-icon.component';
import { FileSizePipe } from '../../pipes/filesize.pipe';
import { ReceiptStatusUiPipe } from '../../pipes/receipt-status-ui.pipe';
import { PaymentsCriteria } from '../../services/payment-criteria';

const config = {
  locale: es,
};

export const STEPS = {
  loadFiles: 0,
  uploading: 1,
  ready: 2,
} as const;

export type StepUI = (typeof STEPS)[keyof typeof STEPS];

@Component({
  templateUrl: './receipts.component.html',
  selector: 'app-receipts',
  imports: [
    AsyncPipe,
    AplazoDynamicPipe,
    AplazoButtonComponent,
    AplazoFormDatepickerComponent,
    ReactiveFormsModule,
    AplazoFileUploaderComponent,
    AplazoCommonMessageComponents,
    AplazoSimpleTableComponents,
    AplazoPaginationComponent,
    ReceiptStateIconComponent,
    AplazoSimpleProgressBarComponent,
    ReceiptStatusUiPipe,
    FileSizePipe,
    AplazoDynamicPipe,
    AplazoStepperComponents,
  ],
})
export class ReceiptsComponent implements OnDestroy, OnInit {
  readonly #uploadUseCase = inject(UploadReceiptsUseCase);
  readonly #criteria = inject(PaymentsCriteria);
  readonly #i18nService = inject(I18NService);
  readonly #notifier = inject(NotifierService);
  readonly #i18nScope = 'receipts';
  readonly #destroy$ = new Subject<void>();
  readonly #listUseCase = inject(GetListReceiptUseCase);
  readonly currentPage$ = this.#criteria.getPageNum$();
  readonly #itemsCounting$ = new BehaviorSubject<number>(0);

  readonly dateControl = new FormControl<Date | null>(new Date(), {
    nonNullable: false,
    validators: [Validators.required],
  });

  readonly #files = signal<
    Array<{
      file: File;
      payload: {
        state: UploadReceiptState;
        progress?: number;
        message?: string;
      };
    }>
  >([]);

  readonly files = computed(() => this.#files());

  readonly allStatesTracked = computed(() => {
    const onlyStates = this.files().map(f => f.payload.state);

    const isAllTracked = areAllStateTracked(onlyStates);

    return isAllTracked;
  });

  readonly step = computed(() => {
    const hasFiles = this.files().length > 0;
    const stepTwoState = ['loaded', 'loading'] as UploadReceiptState[];

    const someIsUploading = this.files().some(f =>
      stepTwoState.includes(f.payload.state)
    );

    if (this.allStatesTracked()) {
      return STEPS.ready;
    }

    if (hasFiles && someIsUploading) {
      return STEPS.uploading;
    }

    return STEPS.loadFiles;
  });

  today = this.#criteria.getToday();

  itemsCounting$ = this.#itemsCounting$.pipe(takeUntil(this.#destroy$));

  pagesByItemsCounting$ = this.#itemsCounting$.pipe(
    map(count => {
      return Math.ceil(count / 10);
    }),
    takeUntil(this.#destroy$)
  );

  restartReceipts = new Subject<void>();
  receipts$ = this.#criteria.getCriteria$().pipe(
    combineLatestWith(
      this.restartReceipts.asObservable().pipe(startWith(true))
    ),
    switchMap(([criteria]) =>
      this.#listUseCase.execute({
        pageNum: criteria.pageNum,
        pageSize: criteria.pageSize,
      })
    ),
    tap(response => {
      this.#itemsCounting$.next(response.totalElements);
    }),
    map(response => response.content),
    map(items => [...items].sort((a, b) => b.id - a.id)),
    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  @ViewChild(AplazoFileUploaderComponent)
  __uploader!: AplazoFileUploaderComponent;

  readonly isMultiFileSupported = toSignal(
    this.#i18nService.getTranslateObjectByKey<{ enabled: boolean }>({
      key: 'multipleFiles',
      scope: this.#i18nScope,
    }),
    {
      initialValue: false as any,
    }
  );

  readonly uploadTextUI = toSignal(
    this.#i18nService
      .getTranslateObjectByKey<{ upload: string; process: string }>({
        key: 'buttons',
        scope: this.#i18nScope,
      })
      .pipe(
        combineLatestWith(
          this.#i18nService
            .getTranslateObjectByKey<{
              dragging: string;
              orText: string;
              manualLoad: string;
            }>({
              key: 'loadFiles',
              scope: this.#i18nScope,
            })
            .pipe(takeUntil(this.#destroy$)),
          this.#i18nService
            .getTranslateObjectByKey<{ message: string }>({
              key: 'progress',
              scope: this.#i18nScope,
            })
            .pipe(takeUntil(this.#destroy$))
        ),
        map(([buttons, loadFiles, progress]) => ({
          buttons,
          loadFiles,
          progress,
        })),
        takeUntil(this.#destroy$)
      )
  );

  readonly emptyLoadedTextUI = toSignal(
    this.#i18nService
      .getTranslateObjectByKey<{
        title: string;
        description: string;
        button: { label: string };
      }>({
        key: 'emptyLoadedFiles',
        scope: this.#i18nScope,
      })
      .pipe(takeUntil(this.#destroy$))
  );

  restart(): void {
    this.__uploader.updateFiles([]);
    this.#files.set([]);
    this.restartReceipts.next();
  }

  changePage(page: number): void {
    this.#criteria.setPageNum(page);
  }

  async process(files: File[]): Promise<void> {
    if (this.step() === STEPS.uploading) {
      this.#notifier.warning({
        title: 'Tiene archivos pendientes de procesarse',
        message: 'Por favor, espere a que los archivos se procesen',
      });
      return;
    }

    this.#files.set([]);

    this.#files.set(
      files.map(file => ({
        file,
        payload: {
          state: 'idle' as UploadReceiptState,
        },
      }))
    );

    const chosenDate = await lastValueFrom(
      this.#criteria.getDate$().pipe(take(1))
    );

    const identifier = chosenDate.split('/').reverse().join('');

    const paymentDate = chosenDate.split('/').reverse().join('-');

    this.#uploadUseCase
      .execute({
        identifier,
        files,
        multiple: this.isMultiFileSupported(),
        paymentDate,
      })
      .pipe(
        tap(filesStates => {
          this.#files.set(
            filesStates.map(f => ({
              file: f.data.file,
              payload: {
                progress: f.data.progress,
                state: f.state,
                message: (f.error as any)?.message ?? f.data.message ?? '',
              },
            }))
          );
        }),

        tap(items => {
          const onlyStates: UploadReceiptState[] = [];
          let maybeHasReceipts = 0;
          let totalFiles = 0;

          items.forEach(f => {
            onlyStates.push(f.state);

            if (f.state === 'ready') {
              maybeHasReceipts += 1;
              totalFiles += f.data?.totalFilesProcessed ?? 0;
            }
          });

          if (maybeHasReceipts > 0) {
            const withReceipts = {
              title: 'Los recibos han sido creados de forma exitosa.',
              message: `Hemos generado ${totalFiles} recibos.`,
            };

            const withoutReceipts = {
              title: 'Los archivos fueron procesados de forma exitosa.',
              message: 'No se han generado recibos.',
            };

            this.#notifier.success(
              totalFiles > 0 ? withReceipts : withoutReceipts
            );
          }
        }),

        takeUntil(
          combineLatest([
            this.#destroy$,
            this.#criteria.getDate$().pipe(take(1)),
          ])
        ),

        catchError(error => {
          console.warn(error);
          return EMPTY;
        })
      )
      .subscribe();
  }

  showFileLoaderError(error: any): void {
    this.#notifier.warning({
      title: 'Lo sentimos la carga falló',
      message: error.message,
    });
  }

  ngOnInit(): void {
    this.dateControl.valueChanges
      .pipe(
        startWith(this.dateControl.value),
        filter(Boolean),
        tap(date => {
          this.#files.set([]);
          this.__uploader?.updateFiles([]);

          const dateToSet = format(
            date,
            'dd/MM/yyyy',
            config
          ) as RawDateDayFirst;

          this.#criteria.setDate(dateToSet);
        })
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
