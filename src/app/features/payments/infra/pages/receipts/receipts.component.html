<section class="px-4 md:px-8 pb-16 pt-8">
  <aplz-ui-stepper-group editable="false" [selectedIndex]="step()">
    <aplz-ui-step label="Elige una fecha y carga archivos.">
      <div class="bg-light rounded-lg shadow-md mt-4 pt-4">
        <div class="w-36 mx-auto">
          <aplz-ui-form-datepicker
            [formControl]="dateControl"
            [maxDate]="today"
            legend="Seleccione una fecha">
          </aplz-ui-form-datepicker>
        </div>

        @if (uploadTextUI()) {
          <aplz-ui-file-uploader
            #uploader
            appearence="borderless"
            [whitelistedExtensions]="['pdf']"
            [textUI]="uploadTextUI()!"
            [multiple]="isMultiFileSupported()"
            (fileErrorEvent)="showFileLoaderError($event)"
          (filesEvent)="process($event)"></aplz-ui-file-uploader>
        }
      </div>
    </aplz-ui-step>
    <aplz-ui-step label="Revisa el progreso de los archivos">
      <div class="bg-light rounded-lg shadow-md mt-4">
        <div
          class="grid justify-center items-center gap-8 w-fit mx-auto p-8 text-center">
          <section>
            <h3 class="text-xl my-6">Estamos procesando los archivos.</h3>
            <h4 class="font-light">
              Por favor, espera un momento. Una vez que el proceso termine este
              mensaje se actualizara.
            </h4>
          </section>
        </div>
      </div>
    </aplz-ui-step>
    <aplz-ui-step label="Revisa el resultado">
      <div class="bg-light rounded-lg shadow-md mt-4">
        <div
          class="grid justify-center items-center gap-8 w-fit mx-auto p-8 text-center">
          <section>
            <h3 class="text-xl my-6">
              Terminamos de cargar y procesar los archivos.
            </h3>
            <h4 class="font-light">
              Puedes revisar el estado de cada archivo en la tabla de abajo.
            </h4>
          </section>
          <button
            aplzButton
            aplzAppearance="solid"
            size="md"
            aplzColor="dark"
            (click)="restart()">
            Cargar nuevo archivo
          </button>
        </div>
      </div>
    </aplz-ui-step>
  </aplz-ui-stepper-group>

  @if (files().length) {
    <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto mt-8">
      <table aplzSimpleTable aria-label="Payment List">
        <tr aplzSimpleTableHeaderRow>
          <th
            aplzSimpleTableHeaderCell
            scope="col"
          class="text-center px-2"></th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            nombre
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Tamaño
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Última modificación
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Estado
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Progreso
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Mensaje
          </th>
        </tr>

        @for (f of files(); track f.file.name) {
          <tr aplzSimpleTableBodyRow>
            <td aplzSimpleTableBodyCell class="font-semibold text-center">
              <app-receipt-state-icon
              [state]="f.payload.state"></app-receipt-state-icon>
            </td>
            <td aplzSimpleTableBodyCell class="text-center">
              {{ f.file.name }}
            </td>
            <td aplzSimpleTableBodyCell class="text-center">
              {{ f.file.size | filesize }}
            </td>
            <td aplzSimpleTableBodyCell class="text-center">
              {{ f.file.lastModified | aplzDynamicPipe: 'date' }}
            </td>
            <td aplzSimpleTableBodyCell class="font-semibold text-center">
              {{ f.payload.state | receiptStatusUi }}
            </td>
            <td aplzSimpleTableBodyCell class="text-center">
              @if (f.payload.progress && f.payload.progress >= 0) {
                <aplz-ui-simple-progress-bar
                  [uploadProgress]="
                    f.payload.progress
                  "></aplz-ui-simple-progress-bar>
              }
            </td>
            <td aplzSimpleTableBodyCell class="text-center">
              {{ f.payload.message ?? 'NA' }}
            </td>
          </tr>
        }
      </table>
    </div>
  } @else {
    <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto mt-8">
      <table aplzSimpleTable aria-label="Payment List">
        <tr aplzSimpleTableHeaderRow>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            nombre
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Fecha de Carga
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Estado
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Archivos
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Fecha de pago
          </th>
        </tr>

        @for (item of receipts$ | async; track item) {
          <tr aplzSimpleTableBodyRow>
            <td aplzSimpleTableBodyCell class="font-semibold text-center">
              {{ item.fileName }}
            </td>
            <td aplzSimpleTableBodyCell class="font-semibold text-center">
              {{ item.createdAt | aplzDynamicPipe: 'shortDate' }}
            </td>
            <td aplzSimpleTableBodyCell class="font-semibold text-center">
              {{ item.status | receiptStatusUi }}
            </td>
            <td aplzSimpleTableBodyCell class="font-semibold text-center">
              {{ item.receiptsGenerated }}
            </td>
            <td aplzSimpleTableBodyCell class="font-semibold text-center">
              {{ item.paymentDate }}
            </td>
          </tr>
        }
      </table>
    </div>
    <div class="mt-3">
      <aplz-ui-pagination
        [totalPages]="pagesByItemsCounting$ | async"
        [currentPage]="currentPage$ | async"
        (selectedPage)="changePage($event)">
      </aplz-ui-pagination>
    </div>
  }
</section>
