import { AsyncPipe, I18nPluralPipe } from '@angular/common';
import {
  Component,
  computed,
  inject,
  OnD<PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { NotifierService, RawDateDayFirst } from '@aplazo/merchant/shared';
import { AplazoDynamicPipe, ValidDynamicPipesNames } from '@aplazo/shared-ui';
import { AutocompleteComponent } from '@aplazo/shared-ui/autocomplete';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoStatCardComponent,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { iconBankNotes, iconDownloadTray } from '@aplazo/ui-icons';
import { DialogRef, DialogService } from '@ngneat/dialog';
import { format, parse } from 'date-fns';
import { es } from 'date-fns/locale';
import {
  BehaviorSubject,
  combineLatest,
  combineLatestWith,
  distinctUntilChanged,
  EMPTY,
  filter,
  lastValueFrom,
  map,
  shareReplay,
  skip,
  startWith,
  Subject,
  switchMap,
  take,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs';
import { UserStore } from '../../../../login/application/services/user.store';
import {
  AUTOCOMPLETE_CONFIGS,
  FilterType,
  INVOICE_STATUSES,
  mapApiEntityToAutocompleteItem,
  mapConstantsToAutocompleteItems,
  PAYMENT_FREQUENCIES,
  PAYMENT_STATUSES,
  PaymentsTableHeadersUI,
  PaymentSummaryCard,
  SELECT_FILTER_CONFIGS,
  statCardKeys,
} from '../../../../shared/domain/payments';
import { GetInvoicesSummaryUseCase } from '../../../application/usecases/get-invoices-summary.usecase';
import { GetListUseCase } from '../../../application/usecases/get-list.usecase';
import { GetSummaryUseCase } from '../../../application/usecases/get-summary.usecase';
import { SendReportUseCase } from '../../../application/usecases/send-report.usecase';
import { PAYMENT_INVOICE_SUMMARY_KEYS } from '../../../domain/entities/invoices';
import { REPORT_TYPES, ReportKey } from '../../../domain/entities/reports';
import { ConfirmReportComponent } from '../../components/confirm-report.component';
import { HandleInvoicesByIdsGenerationService } from '../../services/handle-all-invoices-by-ids-generation.service';
import { HandleAllInvoicesGenerationService } from '../../services/handle-all-invoices-generation.service';
import { PaymentsCriteria } from '../../services/payment-criteria';

const config = {
  locale: es,
};

@Component({
  selector: 'app-payments',
  imports: [
    AsyncPipe,
    I18nPluralPipe,
    AplazoStatCardComponent,
    AplazoCardComponent,
    AplazoButtonComponent,
    AplazoIconComponent,
    AplazoPaginationComponent,
    AplazoSimpleTableComponents,
    AplazoCommonMessageComponents,
    AplazoDynamicPipe,
    AplazoFormDatepickerComponent,
    ReactiveFormsModule,
    AplazoFormFieldDirectives,
    AutocompleteComponent,
  ],
  templateUrl: './payments.component.html',
})
export class PaymentsComponent implements OnDestroy, OnInit {
  readonly #statsUseCase = inject(GetSummaryUseCase);
  readonly #listUseCase = inject(GetListUseCase);
  readonly #reportUseCase = inject(SendReportUseCase);
  readonly #i18Service = inject(I18NService);
  readonly #criteria = inject(PaymentsCriteria);
  readonly #dialog = inject(DialogService);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #userStore = inject(UserStore);
  readonly #notifier = inject(NotifierService);
  readonly #getPaymentInvoicesSummary = inject(GetInvoicesSummaryUseCase);
  readonly #handleAllInvoices = inject(HandleAllInvoicesGenerationService);
  readonly #handleInvoicesByIds = inject(HandleInvoicesByIdsGenerationService);
  readonly #i18nScope = 'payments';

  readonly autocompleteConfigs = AUTOCOMPLETE_CONFIGS;
  readonly selectConfigs = SELECT_FILTER_CONFIGS;

  readonly hasActiveFilters = toSignal(
    combineLatest([
      this.#criteria.getFrequency$(),
      this.#criteria.getPaymentStatus$(),
      this.#criteria.getInvoiceStatus$(),
      this.#criteria.getPaymentId$(),
      this.#criteria.getMerchantId$(),
      this.#criteria.getName$(),
    ]).pipe(
      map(
        ([
          frequency,
          paymentStatus,
          invoiceStatus,
          paymentId,
          merchantId,
          name,
        ]) =>
          frequency !== null ||
          paymentStatus !== null ||
          invoiceStatus !== null ||
          paymentId !== null ||
          merchantId !== null ||
          name !== null
      ),
      startWith(false)
    ),
    { initialValue: false }
  );

  readonly #destroy$ = new Subject<void>();
  readonly #itemsCounting$ = new BehaviorSubject<number>(0);
  readonly #update$ = new Subject<void>();
  readonly today = this.#criteria.getToday();
  readonly currentPage$ = this.#criteria.getPageNum$();

  readonly dateControl = new FormControl<Date | null>(null, {
    validators: [Validators.required],
  });
  readonly frequencyControl = new FormControl<string | null>(null);
  readonly paymentStatusControl = new FormControl<string | null>(null);
  readonly invoiceStatusControl = new FormControl<string | null>(null);

  readonly formControls: Record<string, FormControl<string | null>> = {
    frequencyControl: this.frequencyControl,
    paymentStatusControl: this.paymentStatusControl,
    invoiceStatusControl: this.invoiceStatusControl,
  };

  private autocompleteRefs = new Map<string, AutocompleteComponent>();
  readonly isBackendReady = false;

  @ViewChild('paymentIdAutocomplete')
  set paymentIdAutocomplete(ref: AutocompleteComponent) {
    if (ref) this.autocompleteRefs.set('paymentIdAutocomplete', ref);
  }

  @ViewChild('merchantIdAutocomplete')
  set merchantIdAutocomplete(ref: AutocompleteComponent) {
    if (ref) this.autocompleteRefs.set('merchantIdAutocomplete', ref);
  }

  @ViewChild('merchantNameAutocomplete')
  set merchantNameAutocomplete(ref: AutocompleteComponent) {
    if (ref) this.autocompleteRefs.set('merchantNameAutocomplete', ref);
  }

  #isRoleEnableToEdit = toSignal(
    this.#userStore.roles$.pipe(
      take(1),
      map(
        roles =>
          roles.includes('ROLE_CONTROL_TOWER_ADMIN') ||
          roles.includes('ROLE_CONTROL_TOWER_MERCHANT_OPS')
      )
    )
  );

  isRoleEnableToEdit = computed(() => this.#isRoleEnableToEdit());

  readonly #canGenerateInvoices = toSignal(
    this.#userStore.roles$.pipe(
      take(1),
      map(
        roles =>
          roles.includes('ROLE_CONTROL_TOWER_ADMIN') ||
          roles.includes('ROLE_CONTROL_TOWER_FINANCE_ADMIN')
      )
    )
  );

  readonly canGenerateInvoices = computed(() => this.#canGenerateInvoices());

  readonly paymentFrequencies = PAYMENT_FREQUENCIES;
  readonly paymentStatuses = PAYMENT_STATUSES;
  readonly invoiceStatuses = INVOICE_STATUSES;

  paymentFrequencyItems = mapConstantsToAutocompleteItems(
    this.paymentFrequencies
  );
  paymentStatusItems = mapConstantsToAutocompleteItems(this.paymentStatuses);
  invoiceStatusItems = mapConstantsToAutocompleteItems(this.invoiceStatuses);

  #paymentIds: Array<{ id: number; name: string }> = [];
  #merchantIds: Array<{ id: number; name: string }> = [];
  #merchantNames: Array<{ id: number; name: string }> = [];

  paymentIds: any[] = [];
  merchantIds: any[] = [];
  merchantNames: any[] = [];

  statsUIText$ = this.#i18Service.getTranslateObjectByKey<PaymentSummaryCard>({
    key: 'stats',
    scope: this.#i18nScope,
  });

  emptyPaymentsText$ = this.#i18Service.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptyTable',
    scope: this.#i18nScope,
  });

  counterLabel$ = this.#i18Service
    .getTranslateObjectByKey<Record<string, string>>({
      key: 'counterLabel',
      scope: this.#i18nScope,
    })
    .pipe(
      map(text => ({
        '=0': text.empty,
        '=1': '# ' + text.singular,
        other: '# ' + text.plural,
      }))
    );

  tableHeaders$ =
    this.#i18Service.getTranslateObjectByKey<PaymentsTableHeadersUI>({
      key: 'tableHeaders',
      scope: this.#i18nScope,
    });

  itemsCounting$ = this.#itemsCounting$.pipe(takeUntil(this.#destroy$));

  pagesByItemsCounting$ = this.#itemsCounting$.pipe(
    map(count => Math.ceil(count / 10)),
    takeUntil(this.#destroy$)
  );

  readonly #criteriaChanges$ = this.#criteria.getCriteria$().pipe(
    distinctUntilChanged<any>(
      (prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)
    ),
    shareReplay({ bufferSize: 1, refCount: true })
  );

  stats$ = this.#criteria.getCriteria$().pipe(
    combineLatestWith(this.#update$.pipe(startWith(null))),
    switchMap(([criteria]) => this.#statsUseCase.execute(criteria.date)),
    combineLatestWith(this.statsUIText$),
    map(([stats, i18nText]) => {
      return statCardKeys.map(key => {
        const pipeName: ValidDynamicPipesNames = 'currency';

        return {
          isDarkMode: false,
          statCardKey: key,
          statCardTitle: i18nText[key].statCardTitle,
          value: String(stats[key]),
          helpTooltip: i18nText[key].helpTooltip,
          pipeName,
        };
      });
    }),
    takeUntil(this.#destroy$)
  );

  payments$ = this.#criteriaChanges$.pipe(
    switchMap(criteria =>
      this.#listUseCase
        .execute({
          date: criteria.date,
          pageNum: criteria.pageNum,
          pageSize: criteria.pageSize,
          frequency: criteria.frequency,
          paymentStatus: criteria.paymentStatus,
          invoiceStatus: criteria.invoiceStatus,
          paymentId: criteria.paymentId,
          merchantId: criteria.merchantId,
          name: criteria.name,
        })
        .pipe(
          tap(response => {
            this.#itemsCounting$.next(response.totalElements);
          }),
          map(response => response.content),
          map(items => [...items].sort((a, b) => b.id - a.id))
        )
    ),
    shareReplay({ bufferSize: 1, refCount: true }),
    takeUntil(this.#destroy$)
  );

  invoicesSummary$ = this.#criteria.getCriteria$().pipe(
    combineLatestWith(this.#update$.pipe(startWith(null))),
    switchMap(([criteria]) =>
      this.#getPaymentInvoicesSummary.execute({
        startDate: criteria.date,
        endDate: criteria.date,
      })
    ),
    map(summary => {
      return PAYMENT_INVOICE_SUMMARY_KEYS.map(key => {
        return {
          label: summary[key].label,
          amount: summary[key].amount,
          helpTooltip: summary[key].tooltip ?? undefined,
        };
      });
    })
  );

  hasPayments$ = this.payments$.pipe(
    map(payments => payments.length > 0),
    takeUntil(this.#destroy$)
  );

  readonly vm$ = combineLatest({
    hasPayments: this.hasPayments$,
    stats: this.stats$,
    invoicesSummary: this.invoicesSummary$,
    payments: this.payments$,
    statsUIText: this.statsUIText$,
    tableHeaders: this.tableHeaders$,
    counterLabel: this.counterLabel$,
    emptyPaymentsText: this.emptyPaymentsText$,
    pagesByItemsCounting: this.pagesByItemsCounting$,
    currentPage: this.currentPage$,
    itemsCounting: this.itemsCounting$,
    emptyMessageUIText: this.emptyPaymentsText$,
  }).pipe(takeUntil(this.#destroy$));

  constructor() {
    this.#iconRegister.registerIcons([iconDownloadTray, iconBankNotes]);
  }

  changePage(page: number): void {
    this.#criteria.setPageNum(page);
  }

  sendReport(): void {
    const dialog: DialogRef<any, string> = this.#dialog.open(
      ConfirmReportComponent,
      {
        data: {
          description: 'Seleccione el tipo de reporte: ',
          title: 'Enviaremos un correo electrónico con el reporte.',
          buttons: [
            { label: 'Pagos', id: 'payment' },
            { label: 'Validaciones', id: 'validations' },
          ],
        },
      }
    );

    dialog.afterClosed$
      .pipe(
        withLatestFrom(this.#criteria.getCriteria$(), this.#userStore.email$),
        switchMap(([result, criteria, email]) => {
          if (!result || !REPORT_TYPES[result as ReportKey]) {
            return EMPTY;
          }

          return this.#reportUseCase.execute({
            date: criteria.date,
            type: result,
            email,
          });
        }),
        take(1)
      )
      .subscribe();
  }

  async generateInvoice(): Promise<void> {
    if (this.dateControl.invalid || !this.dateControl.value) {
      this.#notifier.warning({
        title: 'Se requiere una fecha para generar las facturas',
      });
      return;
    }

    const confirmReport = this.#dialog.open(ConfirmReportComponent, {
      data: {
        title: 'Generar facturas',
        description: 'Elija generar por ID o todas las facturas',
        buttons: [
          { label: 'No. Pagos', id: 'single' },
          { label: 'Todas las facturas', id: 'all' },
        ],
      },
    });

    const confirmReportResult = await lastValueFrom(
      confirmReport.afterClosed$.pipe(take(1))
    );

    if (!confirmReportResult) {
      return;
    }

    if (confirmReportResult === 'single') {
      const { withExecution } = await this.#handleInvoicesByIds.execute();
      if (withExecution) {
        this.#update$.next();
      }
      return;
    }

    const date = this.dateControl.value;
    const { withExecution } = await this.#handleAllInvoices.execute(date);
    if (withExecution) {
      this.#update$.next();
    }
  }

  onSelectFilterChange(event: Event, filterType: FilterType): void {
    const target = event.target as HTMLSelectElement;
    const value = target.value === 'null' ? null : target.value;
    const config = this.selectConfigs[filterType];

    const control = this.formControls[config.controlName];
    if (control) {
      control.setValue(value, { emitEvent: false });
    }

    this.#criteria[config.criteriaMethod](value);

    if (config.triggerUpdate) {
      this.#update$.next();
    }
  }

  onAutocompleteItemSelected(event: any, configId: string): void {
    const configKey = Object.keys(this.autocompleteConfigs).find(
      key =>
        this.autocompleteConfigs[key as keyof typeof this.autocompleteConfigs]
          .viewChildRef === configId
    ) as keyof typeof this.autocompleteConfigs | undefined;

    if (!configKey || !event) return;

    const config = this.autocompleteConfigs[configKey];
    const value = config.valueExtractor(event);

    if (value !== null && value !== undefined) {
      this.#criteria[config.criteriaSetterMethod](value);
    }
  }

  onAutocompleteItemRemoved(configId: string): void {
    const configKey = Object.keys(this.autocompleteConfigs).find(
      key =>
        this.autocompleteConfigs[key as keyof typeof this.autocompleteConfigs]
          .viewChildRef === configId
    ) as keyof typeof this.autocompleteConfigs | undefined;

    if (!configKey) return;

    const config = this.autocompleteConfigs[configKey];
    this.#criteria[config.criteriaSetterMethod](null);
  }

  resetFilters(): void {
    Object.values(this.formControls).forEach(control => {
      control.reset();
    });

    this.autocompleteRefs.forEach(ref => {
      if (ref) ref.clearSelection();
    });

    this.#criteria.resetFilters();
    this.#update$.next();
  }

  private updateAutocompleteData(payments: any[]): void {
    this.#paymentIds = payments.map(payment => ({
      id: payment.id,
      name: payment.id.toString(),
    }));

    this.#merchantIds = payments.map(payment => ({
      id: payment.merchantId,
      name: payment.merchantId.toString(),
    }));

    this.#merchantNames = payments.map(payment => ({
      id: payment.merchantId,
      name: payment.name,
    }));

    this.paymentIds = this.#paymentIds.map(entity =>
      mapApiEntityToAutocompleteItem(entity, 'name')
    );

    this.merchantIds = this.#merchantIds.map(entity =>
      mapApiEntityToAutocompleteItem(entity, 'name')
    );

    this.merchantNames = this.#merchantNames.map(entity =>
      mapApiEntityToAutocompleteItem(entity, 'name')
    );
  }

  ngOnInit(): void {
    this.#criteria
      .getDate$()
      .pipe(
        take(1),
        filter(date => !!date),
        map(d => ({ startDate: d })),
        tap(date => {
          const data = parse(date.startDate, 'dd/MM/yyyy', new Date());
          this.dateControl.setValue(data, { emitEvent: false });
        })
      )
      .subscribe();

    this.dateControl.valueChanges
      .pipe(
        startWith(this.dateControl.value),
        skip(1),
        map(date => {
          if (!date) return null;
          return format(date, 'dd/MM/yyyy', config) as RawDateDayFirst;
        }),
        filter(date => !!date),
        takeUntil(this.#destroy$)
      )
      .subscribe(date => {
        if (date) {
          this.#criteria.setDate(date);
        }
      });

    this.frequencyControl.valueChanges
      .pipe(distinctUntilChanged(), takeUntil(this.#destroy$))
      .subscribe(value => {
        this.#criteria.setFrequency(value);
      });

    this.paymentStatusControl.valueChanges
      .pipe(distinctUntilChanged(), takeUntil(this.#destroy$))
      .subscribe(value => {
        this.#criteria.setPaymentStatus(value);
      });

    this.invoiceStatusControl.valueChanges
      .pipe(distinctUntilChanged(), takeUntil(this.#destroy$))
      .subscribe(value => {
        this.#criteria.setInvoiceStatus(value);
        this.#update$.next();
      });

    this.payments$
      .pipe(takeUntil(this.#destroy$))
      .subscribe(payments => this.updateAutocompleteData(payments));
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
