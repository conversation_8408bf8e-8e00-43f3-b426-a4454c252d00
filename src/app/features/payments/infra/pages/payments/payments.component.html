@if (vm$ | async; as context) {
  <section class="py-2 pb-6 px-4 md:px-8 bg-dark/10">
    <div class="flex items-baseline justify-between">
      <aplz-ui-form-datepicker
        [maxDate]="today"
        [formControl]="dateControl"
        [centerText]="true"
        legend="Seleccione fecha"></aplz-ui-form-datepicker>

      <div
        class="flex gap-2 items-center flex-wrap flex-shrink-0 flex-grow justify-end">
        @if (context.hasPayments && canGenerateInvoices()) {
          <button
            aplzButton
            aplzAppearance="solid"
            aplzColor="dark"
            size="sm"
            [rounded]="true"
            (click)="generateInvoice()">
            <span class="mr-1">
              <aplz-ui-icon name="bank-notes" size="xs"></aplz-ui-icon>
            </span>

            Generar Facturas
          </button>
        }
        <button
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="sm"
          [rounded]="true"
          (click)="sendReport()">
          <span class="mr-1">
            <aplz-ui-icon name="download-tray" size="xs"></aplz-ui-icon>
          </span>

          Descargar
        </button>
      </div>
    </div>

    @if (context.stats) {
      <aplz-ui-card class="-mt-3" appearance="shadow">
        <div
          class="grid gap-2 grid-cols-1 md:grid-cols-2 xl:grid-cols-3 3xl:grid-cols-6">
          @for (stat of context.stats; track stat) {
            <aplz-ui-stat-card
              [I18nText]="{
                statCardTitle: stat.statCardTitle,
                helpTooltip: stat.helpTooltip,
              }"
              [pipeName]="stat.pipeName"
              [principalAmount]="stat.value"
              [color]="
                stat.statCardKey === 'adjustmentPlus'
                  ? 'success'
                  : stat.statCardKey === 'adjustmentMinus'
                    ? 'danger'
                    : 'light'
              ">
            </aplz-ui-stat-card>
          }
        </div>
      </aplz-ui-card>
    }

    <div class="mt-6 mb-2">
      <h2 class="text-base font-medium">Estado de las Facturas</h2>
    </div>
    <aplz-ui-card class="">
      <div
        class="grid gap-2 grid-cols-1 md:grid-cols-2 xl:grid-cols-3 3xl:grid-cols-6">
        @for (card of context.invoicesSummary; track card.label) {
          <aplz-ui-stat-card
            [I18nText]="{
              statCardTitle: card.label,
              helpTooltip: card.helpTooltip,
            }"
            [principalAmount]="card.amount"
            color="light">
          </aplz-ui-stat-card>
        }
      </div>
    </aplz-ui-card>

    <!-- Filters section -->
    @if (isBackendReady) {
      <div
        class="flex flex-col sm:flex-row items-start sm:items-center justify-between mt-6 mb-4 w-full gap-2">
        <h2 class="text-base font-medium">Filtros</h2>
        @if (hasActiveFilters()) {
          <button
            aplzButton
            aplzAppearance="solid"
            aplzColor="dark"
            size="sm"
            [rounded]="true"
            (click)="resetFilters()">
            <span class="mr-1">
              <aplz-ui-icon name="x-mark" size="xs"></aplz-ui-icon>
            </span>
            Resetear Filtros
          </button>
        }
      </div>

      <aplz-ui-card class="-mt-3 !overflow-x-clip" appearance="shadow">
        <div
          class="grid gap-2 grid-cols-1 md:grid-cols-2 xl:grid-cols-3 3xl:grid-cols-6">
          <!-- Selector de Frecuencia de Pago -->
          <aplz-ui-form-field class="w-full">
            <aplz-ui-form-label>{{
              selectConfigs.frequency.label
            }}</aplz-ui-form-label>
            <select
              class="w-full p-3"
              aplzFormSelect
              [formControl]="formControls[selectConfigs.frequency.controlName]"
              (change)="onSelectFilterChange($event, 'frequency')"
              [title]="'Seleccionar ' + selectConfigs.frequency.label">
              <option [value]="null">
                {{ selectConfigs.frequency.placeholder }}
              </option>
              @for (
                option of selectConfigs.frequency.options;
                track option.value
              ) {
                <option [value]="option.value">{{ option.label }}</option>
              }
            </select>
          </aplz-ui-form-field>

          <!-- Selector de Estado de Pago -->
          <aplz-ui-form-field class="w-full">
            <aplz-ui-form-label>{{
              selectConfigs.paymentStatus.label
            }}</aplz-ui-form-label>
            <select
              class="w-full p-3"
              aplzFormSelect
              [formControl]="
                formControls[selectConfigs.paymentStatus.controlName]
              "
              (change)="onSelectFilterChange($event, 'paymentStatus')"
              [title]="'Seleccionar ' + selectConfigs.paymentStatus.label">
              <option [value]="null">
                {{ selectConfigs.paymentStatus.placeholder }}
              </option>
              @for (
                option of selectConfigs.paymentStatus.options;
                track option.value
              ) {
                <option [value]="option.value">{{ option.label }}</option>
              }
            </select>
          </aplz-ui-form-field>

          <!-- Selector de Estado de Factura -->
          <aplz-ui-form-field class="w-full">
            <aplz-ui-form-label>{{
              selectConfigs.invoiceStatus.label
            }}</aplz-ui-form-label>
            <select
              class="w-full p-3"
              aplzFormSelect
              [formControl]="
                formControls[selectConfigs.invoiceStatus.controlName]
              "
              (change)="onSelectFilterChange($event, 'invoiceStatus')"
              [title]="'Seleccionar ' + selectConfigs.invoiceStatus.label">
              <option [value]="null">
                {{ selectConfigs.invoiceStatus.placeholder }}
              </option>
              @for (
                option of selectConfigs.invoiceStatus.options;
                track option.value
              ) {
                <option [value]="option.value">{{ option.label }}</option>
              }
            </select>
          </aplz-ui-form-field>

          <!-- Autocomplete fields -->
          <div class="w-full">
            <aplz-ui-autocomplete
              #paymentIdAutocomplete
              class="w-full"
              [items]="paymentIds"
              [displayProperty]="'displayValue'"
              [placeholder]="autocompleteConfigs.paymentId.placeholder"
              [minSearchLength]="autocompleteConfigs.paymentId.minSearchLength"
              [emptyMessage]="autocompleteConfigs.paymentId.emptyMessage"
              (itemSelected)="
                onAutocompleteItemSelected($event, 'paymentIdAutocomplete')
              "
              (itemRemoved)="onAutocompleteItemRemoved('paymentIdAutocomplete')"
              (selectionChange)="($event)">
            </aplz-ui-autocomplete>
          </div>

          <div class="w-full">
            <aplz-ui-autocomplete
              #merchantIdAutocomplete
              class="w-full"
              [items]="merchantIds"
              [displayProperty]="'displayValue'"
              [placeholder]="autocompleteConfigs.merchantId.placeholder"
              [minSearchLength]="autocompleteConfigs.merchantId.minSearchLength"
              [emptyMessage]="autocompleteConfigs.merchantId.emptyMessage"
              (itemSelected)="
                onAutocompleteItemSelected($event, 'merchantIdAutocomplete')
              "
              (itemRemoved)="
                onAutocompleteItemRemoved('merchantIdAutocomplete')
              "
              (selectionChange)="($event)">
            </aplz-ui-autocomplete>
          </div>

          <div class="w-full">
            <aplz-ui-autocomplete
              #merchantNameAutocomplete
              class="w-full"
              [items]="merchantNames"
              [displayProperty]="'displayValue'"
              [placeholder]="autocompleteConfigs.merchantName.placeholder"
              [minSearchLength]="
                autocompleteConfigs.merchantName.minSearchLength
              "
              [emptyMessage]="autocompleteConfigs.merchantName.emptyMessage"
              (itemSelected)="
                onAutocompleteItemSelected($event, 'merchantNameAutocomplete')
              "
              (itemRemoved)="
                onAutocompleteItemRemoved('merchantNameAutocomplete')
              "
              (selectionChange)="($event)">
            </aplz-ui-autocomplete>
          </div>
        </div>
      </aplz-ui-card>
    }
  </section>

  <section
    class="px-4 md:px-8 my-4 flex flex-col md:flex-row items-center justify-center md:justify-between">
    <div class="text-center lg:text-left">
      @if (counterLabel$ | async; as counterLabelTextTemplate) {
        <span class="whitespace-nowrap font-medium text-lg uppercase">
          {{ itemsCounting$ | async | i18nPlural: counterLabelTextTemplate }}
        </span>
      }
    </div>
  </section>

  <section class="px-4 md:px-8 pb-16">
    @if (context.hasPayments) {
      <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto">
        <table aplzSimpleTable aria-label="Payment List">
          <tr aplzSimpleTableHeaderRow>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.id }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.saleAmount }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.feeAmount }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.payAmount }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.adjustmentMinus }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.adjustmentPlus }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.finalAmount }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.paymentFrequency }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.status }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.paymentDate }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.loans }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.adjustments }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.merchantID }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              {{ context.tableHeaders.name }}
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              Estado de factura
            </th>
          </tr>

          @for (item of context.payments; track item.id) {
            <tr aplzSimpleTableBodyRow>
              <td aplzSimpleTableBodyCell class="font-semibold text-center">
                {{ item.id }}
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                {{ item.saleAmount | aplzDynamicPipe: 'currency' }}
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                {{ item.feeAmount | aplzDynamicPipe: 'currency' }}
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                {{ item.payAmount | aplzDynamicPipe: 'currency' }}
              </td>
              <td
                aplzSimpleTableBodyCell
                class="tabular-nums text-center"
                [class.text-special-danger]="item.adjustmentMinus < 0"
                [class.font-semibold]="item.adjustmentMinus < 0">
                {{ item.adjustmentMinus | aplzDynamicPipe: 'currency' }}
              </td>
              <td
                aplzSimpleTableBodyCell
                class="tabular-nums text-center"
                [class.text-special-success]="item.adjustmentPlus > 0"
                [class.font-semibold]="item.adjustmentPlus > 0">
                {{ item.adjustmentPlus | aplzDynamicPipe: 'currency' }}
              </td>
              <td
                aplzSimpleTableBodyCell
                class="font-semibold tabular-nums text-center">
                {{ item.finalAmount | aplzDynamicPipe: 'currency' }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.paymentFrequency }}
              </td>
              <td aplzSimpleTableBodyCell class="font-semibold text-center">
                {{ item.status }}
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                {{ item.paymentDate | aplzDynamicPipe: 'shortDate' }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.loans }}
              </td>
              <td aplzSimpleTableBodyCell class="tabular-nums text-center">
                {{ item.adjustments }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.merchantId }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.name }}
              </td>
              <td
                aplzSimpleTableBodyCell
                class="text-center font-medium"
                [class.text-special-success]="item.invoiceStatus === 'INVOICED'"
                [class.text-special-danger]="item.invoiceStatus === 'ERROR'">
                {{ item.invoiceStatus ?? 'UNKNOWN' }}
              </td>
            </tr>
          }
        </table>
      </div>
      <div class="mt-3">
        <aplz-ui-pagination
          [totalPages]="context.pagesByItemsCounting"
          [currentPage]="context.currentPage"
          (selectedPage)="changePage($event)">
        </aplz-ui-pagination>
      </div>
    } @else {
      <div class="bg-light pt-2 pb-4 rounded-lg shadow-md overflow-x-auto">
        <aplz-ui-common-message
          [i18Text]="{
            title: context.emptyMessageUIText.title,
            description: context.emptyMessageUIText.description,
          }"
          imgName="emptyLoans">
        </aplz-ui-common-message>
      </div>
    }
  </section>
}
