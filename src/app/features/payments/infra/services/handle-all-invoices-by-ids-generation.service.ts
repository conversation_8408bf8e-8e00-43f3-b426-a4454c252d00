import { inject, Injectable } from '@angular/core';
import { NotifierService } from '@aplazo/merchant/shared';
import { AplazoConfirmDialogComponent } from '@aplazo/shared-ui/merchant';
import { DialogRef, DialogService } from '@ngneat/dialog';
import { catchError, lastValueFrom, of, take } from 'rxjs';
import { GenerateInvoicesByIdsUseCase } from '../../application/usecases/generate-invoices-by-ids.usecase';
import { InvoicesByIdsComponent } from '../components/invoices-by-id.component';

@Injectable()
export class HandleInvoicesByIdsGenerationService {
  readonly #dialog = inject(DialogService);
  readonly #notifier = inject(NotifierService);
  readonly #invoiceGenerationUseCase = inject(GenerateInvoicesByIdsUseCase);

  async execute(): Promise<{ withExecution: boolean }> {
    const idsDialog: DialogRef<any, { confirmation: boolean; ids: number[] }> =
      this.#dialog.open(InvoicesByIdsComponent);

    const idsResult = await lastValueFrom(idsDialog.afterClosed$.pipe(take(1)));

    if (!idsResult?.confirmation) {
      this.#notifier.warning({
        title: 'Generación de facturas cancelada',
      });
      return { withExecution: false };
    }

    const dialog = this.#dialog.open(AplazoConfirmDialogComponent, {
      data: {
        title: `¿Está seguro que desea generar las facturas: ${idsResult.ids.join(', ')}?`,
        cancelButton: 'Cancelar',
        acceptButton: 'Generar facturas',
      },
      maxWidth: '320px',
    });

    const confirmationResult = await lastValueFrom(
      dialog.afterClosed$.pipe(take(1))
    );

    if (!confirmationResult.confirmation) {
      this.#notifier.warning({
        title: 'Generación de facturas cancelada',
      });
      return { withExecution: false };
    }

    await lastValueFrom(
      this.#invoiceGenerationUseCase.execute(idsResult.ids).pipe(
        catchError(e => {
          console.warn('Error al generar las facturas', e);
          return of(null);
        }),
        take(1)
      )
    );

    return { withExecution: true };
  }
}
