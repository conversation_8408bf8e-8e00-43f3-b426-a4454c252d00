import { inject, Injectable } from '@angular/core';
import { NotifierService } from '@aplazo/merchant/shared';
import { AplazoConfirmDialogComponent } from '@aplazo/shared-ui/merchant';
import { DialogService } from '@ngneat/dialog';
import { catchError, lastValueFrom, of, take } from 'rxjs';
import { GenerateInvoicesUseCase } from '../../application/usecases/generate-invoices.usecase';

@Injectable()
export class HandleAllInvoicesGenerationService {
  readonly #dialog = inject(DialogService);
  readonly #notifier = inject(NotifierService);
  readonly #invoiceGenerationUseCase = inject(GenerateInvoicesUseCase);

  async execute(date: Date): Promise<{ withExecution: boolean }> {
    const dialog = this.#dialog.open(AplazoConfirmDialogComponent, {
      data: {
        title: '¿Está seguro que desea generar las facturas?',
        cancelButton: 'Cancelar',
        acceptButton: 'Generar facturas',
      },
      maxWidth: '320px',
    });

    const confirmationResult = await lastValueFrom(
      dialog.afterClosed$.pipe(take(1))
    );

    if (!confirmationResult.confirmation) {
      this.#notifier.warning({
        title: 'Generación de facturas cancelada',
      });
      return { withExecution: false };
    }

    await lastValueFrom(
      this.#invoiceGenerationUseCase.execute(date).pipe(
        catchError(e => {
          console.warn('Error al generar las facturas', e);
          return of(null);
        }),
        take(1)
      )
    );

    return { withExecution: true };
  }
}
