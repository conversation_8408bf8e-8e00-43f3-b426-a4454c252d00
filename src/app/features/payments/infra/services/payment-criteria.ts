import { Injectable } from '@angular/core';
import { RawDateDayFirst } from '@aplazo/merchant/shared';
import { format } from 'date-fns';
import {
  BehaviorSubject,
  combineLatest,
  distinctUntilChanged,
  map,
  Observable,
} from 'rxjs';

export interface FilterCriteria {
  date: RawDateDayFirst;
  pageNum: number;
  pageSize: number;
  frequency: string | null;
  paymentStatus: string | null;
  invoiceStatus: string | null;
  paymentId: number | null;
  merchantId: number | null;
  name?: string | null;
}

@Injectable({
  providedIn: 'root',
})
export class PaymentsCriteria {
  readonly #date$ = new BehaviorSubject<RawDateDayFirst>(this.getToday());
  readonly #pageNum$ = new BehaviorSubject<number>(0);
  readonly #pageSize$ = new BehaviorSubject<number>(10);
  readonly #frequency$ = new BehaviorSubject<string | null>(null);
  readonly #paymentStatus$ = new BehaviorSubject<string | null>(null);
  readonly #invoiceStatus$ = new BehaviorSubject<string | null>(null);
  readonly #paymentId$ = new BehaviorSubject<number | null>(null);
  readonly #merchantId$ = new BehaviorSubject<number | null>(null);
  readonly #name$ = new BehaviorSubject<string | null>(null);

  getToday(): RawDateDayFirst {
    return format(new Date(), 'dd/MM/yyyy') as RawDateDayFirst;
  }

  getDate$(): Observable<RawDateDayFirst> {
    return this.#date$.asObservable();
  }

  getPageNum$(): Observable<number> {
    return this.#pageNum$.asObservable();
  }

  getFrequency$(): Observable<string | null> {
    return this.#frequency$.asObservable();
  }

  getPaymentStatus$(): Observable<string | null> {
    return this.#paymentStatus$.asObservable();
  }

  getInvoiceStatus$(): Observable<string | null> {
    return this.#invoiceStatus$.asObservable();
  }

  getPaymentId$(): Observable<number | null> {
    return this.#paymentId$.asObservable();
  }

  getMerchantId$(): Observable<number | null> {
    return this.#merchantId$.asObservable();
  }

  getName$(): Observable<string | null> {
    return this.#name$.asObservable();
  }

  getCriteria$(): Observable<FilterCriteria> {
    return combineLatest([
      this.#date$,
      this.#pageNum$,
      this.#pageSize$,
      this.#frequency$,
      this.#paymentStatus$,
      this.#invoiceStatus$,
      this.#paymentId$,
      this.#merchantId$,
      this.#name$,
    ]).pipe(
      map(
        ([
          date,
          pageNum,
          pageSize,
          frequency,
          paymentStatus,
          invoiceStatus,
          paymentId,
          merchantId,
          name,
        ]) => ({
          date,
          pageNum,
          pageSize,
          frequency,
          paymentStatus,
          invoiceStatus,
          paymentId,
          merchantId,
          name,
        })
      ),
      // Prevent emission if the criteria object hasn't actually changed
      distinctUntilChanged(
        (prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)
      )
    );
  }

  setDate(date: RawDateDayFirst): void {
    if (this.#date$.value === date) {
      return;
    }
    this.#date$.next(date);
  }

  setPageNum(pageNum: number): void {
    if (this.#pageNum$.value === pageNum) {
      return;
    }
    this.#pageNum$.next(pageNum);
  }

  setFrequency(frequency: string | null): void {
    if (this.#frequency$.value === frequency) {
      return;
    }
    this.#frequency$.next(frequency);
  }

  setPaymentStatus(paymentStatus: string | null): void {
    if (this.#paymentStatus$.value === paymentStatus) {
      return;
    }
    this.#paymentStatus$.next(paymentStatus);
  }

  setInvoiceStatus(invoiceStatus: string | null): void {
    if (this.#invoiceStatus$.value === invoiceStatus) {
      return;
    }
    this.#invoiceStatus$.next(invoiceStatus);
  }

  setPaymentId(id: number | null): void {
    if (this.#paymentId$.value === id) {
      return;
    }
    this.#paymentId$.next(id);
  }

  setMerchantId(id: number | null): void {
    if (this.#merchantId$.value === id) {
      return;
    }
    this.#merchantId$.next(id);
  }

  setName(name: string | null): void {
    if (this.#name$.value === name) {
      return;
    }
    this.#name$.next(name);
  }

  resetFilters(): void {
    this.#frequency$.next(null);
    this.#paymentStatus$.next(null);
    this.#invoiceStatus$.next(null);
    this.#paymentId$.next(null);
    this.#merchantId$.next(null);
    this.#name$.next(null);
  }
}
