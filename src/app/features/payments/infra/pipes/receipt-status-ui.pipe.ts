import { Pipe, PipeTransform } from '@angular/core';
import {
  UPLOAD_RECEIPT_STATE,
  UploadReceiptState,
  UploadReceiptStateUI,
} from '../../domain/entities/receipts';

@Pipe({
  standalone: true,
  name: 'receiptStatusUi',
})
export class ReceiptStatusUiPipe implements PipeTransform {
  transform(
    value: UploadReceiptState | null | undefined
  ): UploadReceiptStateUI {
    if (!value) {
      return 'DESCONOCIDO';
    }
    return UPLOAD_RECEIPT_STATE[value];
  }
}
