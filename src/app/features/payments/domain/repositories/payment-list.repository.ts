import { Observable } from 'rxjs';
import { PaymentListRepositoryDto } from '../../application/dtos/list-request.dto';
import {
  PaymentInvoicesRepositoryRequest,
  PaymentInvoicesStatusResponse,
} from '../entities/invoices';
import { PaymentListResponse } from '../entities/list-response';

export abstract class PaymentListRepository {
  abstract getList(
    req: PaymentListRepositoryDto
  ): Observable<PaymentListResponse>;

  abstract getInvoiceStatusList(
    daterange: PaymentInvoicesRepositoryRequest
  ): Observable<PaymentInvoicesStatusResponse[]>;
}
