import { Observable } from 'rxjs';
import {
  InvoiceGenerationByIDSResponse,
  InvoiceGenerationRepositoryRequest,
  InvoiceGenerationResponse,
} from '../entities/payment';

export abstract class PaymentsRepository {
  abstract generateInvoices(
    range: InvoiceGenerationRepositoryRequest
  ): Observable<InvoiceGenerationResponse>;
  abstract generateInvoicesByIds(
    ids: number[]
  ): Observable<InvoiceGenerationByIDSResponse[]>;
}
