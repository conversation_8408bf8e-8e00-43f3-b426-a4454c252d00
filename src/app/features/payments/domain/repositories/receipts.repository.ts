import { HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ReceiptHistoryDto } from '../../application/dtos/upload-receipts.dto';

export abstract class ReceiptsRepository {
  abstract uploadFile(
    identifier: string,
    data: FormData
  ): Observable<HttpEvent<any>>;

  abstract retrieveEvents(): Observable<any>;

  abstract postReceiptHistory(req: ReceiptHistoryDto): Observable<any>;
}
