import {
  Guard,
  RawDateYearFirstWithHyphen,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import { UTCDate } from '@date-fns/utc';
import { endOfDay, startOfDay } from 'date-fns';

export interface Payment {
  id: number;
  merchantId: number;
  name: string;
  saleAmount: number;
  feeAmount: number;
  adjustmentMinus: number;
  adjustmentPlus: number;
  finalAmount: number;
  payAmount: number;
  paymentDate: RawDateYearFirstWithHyphen;
  paymentFrequency: string;
  status: string;
  loans: number;
  adjustments: number;
  invoiceStatus?: string | null;
}

export interface InvoiceGenerationResponse {
  error: number;
  processing: number;
  invoiced: number;
  pending: number;
  excluded: number;
  success?: number;
}

export interface InvoiceGenerationByIDSResponse {
  id: number;
  paymentId: number;
  merchant: string | null;
  status: string;
  paymentDate: RawDateYearFirstWithHyphen | null;
  invoiceDate: RawDateYearFirstWithHyphen | null;
  pdfUrl: string | null;
  xmlUrl: string | null;
  vendor: string;
  vendorInvoiceId: string | number | null;
  error: string[];
}

export interface InvoiceGenerationUIRequest {
  start: Date;
  end: Date;
}

export interface InvoiceGenerationRepositoryRequest {
  start: string; // ISO Date
  end: string; // ISO Date
}

export const toInvoiceGenerationRepositoryRequest = (
  date: Date
): InvoiceGenerationRepositoryRequest => {
  if (!date) {
    throw new RuntimeMerchantError(
      'La fecha para generar las facturas no puede ser nula',
      'toInvoiceGenerationRepositoryRequest::emptyDate'
    );
  }

  Guard.againstInvalidDate({
    date: date,
    errorMessage:
      'La fecha para generar las facturas no es válida. Por favor verifique.',
  });

  const utcDate = new UTCDate(date);

  const startDate = startOfDay(utcDate);
  const endDate = endOfDay(utcDate);

  return {
    start: startDate.toISOString(),
    end: endDate.toISOString(),
  };
};
