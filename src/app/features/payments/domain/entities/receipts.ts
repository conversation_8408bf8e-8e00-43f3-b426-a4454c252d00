import { RawDateYearFirstWithHyphen } from '@aplazo/merchant/shared';

export const UPLOAD_RECEIPT_STATE = {
  idle: 'CREADO',
  loading: 'CARGANDO',
  loaded: 'CARGADO',
  ready: 'LISTO',
  uploadError: 'CARGA FALLIDA',
  processingError: 'CREACIÓN FALLIDA',
  unknown: 'DESCONOCIDO',
} as const;

export type UploadReceiptState = keyof typeof UPLOAD_RECEIPT_STATE;
export type UploadReceiptStateUI =
  (typeof UPLOAD_RECEIPT_STATE)[UploadReceiptState];

export const trackedState: UploadReceiptState[] = [
  'uploadError',
  'ready',
  'unknown',
  'processingError',
];

export const areAllStateTracked = (states: UploadReceiptState[]): boolean => {
  if (states.length === 0) {
    return false;
  }

  const includesTrackedState = states.map(state => {
    return trackedState.includes(state);
  });

  const allStatesAreTracked = includesTrackedState.every(Boolean);

  return allStatesAreTracked;
};

export const areAllStateSuccess = (states: UploadReceiptState[]): boolean => {
  if (states.length === 0) {
    return false;
  }

  const includesSuccessState = states.map(state => {
    return state === 'ready';
  });

  const allStatesAreSuccess = includesSuccessState.every(Boolean);

  return allStatesAreSuccess;
};

export interface ReceiptHistory {
  id: number;
  fileName: string;
  createdAt: string;
  status: UploadReceiptState;
  uploadedBy: string;
  receiptsGenerated: number;
  paymentDate: string;
}

export interface ReceiptHistoryUI {
  id: number;
  fileName: string;
  status: UploadReceiptState;
  uploadedBy: string;
  receiptsGenerated: number;
  createdAt: Date;
  paymentDate: RawDateYearFirstWithHyphen;
}
