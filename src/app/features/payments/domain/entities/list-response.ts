import { PageableResponse } from '@aplazo/merchant/shared';
import { Payment } from './payment';
import { ReceiptHistory, ReceiptHistoryUI } from './receipts';

export interface PaymentListResponse extends PageableResponse {
  content: Payment[];
}

export interface UploadReceiptsResponse extends PageableResponse {
  content: ReceiptHistory[];
}

export interface UploadReceiptsUIDto extends PageableResponse {
  content: ReceiptHistoryUI[];
}
