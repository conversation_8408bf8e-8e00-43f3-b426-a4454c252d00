import {
  B2BDate<PERSON><PERSON><PERSON>,
  Guard,
  RawDateYearFirstWithHyphen,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';

export const PAYMENT_INVOICE_SUMMARY_KEYS = [
  'processing',
  'invoiced',
  'pending',
  'error',
  'excluded',
] as const;

export type PaymentInvoiceSummaryKey =
  (typeof PAYMENT_INVOICE_SUMMARY_KEYS)[number];

export type PaymentInvoiceSummaryResponse = {
  [key in PaymentInvoiceSummaryKey]: number;
};

export type PaymentInvoicesSummaryUI = {
  [key in PaymentInvoiceSummaryKey]: {
    label: string;
    amount: number;
    tooltip?: string;
  };
};

export interface PaymentInvoicesRepositoryRequest {
  start: RawDateYearFirstWithHyphen;
  end: RawDateYearFirstWithHyphen;
  frequency?: string | null;
  paymentStatus?: string | null;
  invoiceStatus?: string | null;
  paymentId?: number | null;
  merchantId?: number | null;
  name?: string | null;
}

export interface PaymentInvoicesStatusResponse {
  id: number;
  paymentId: number;
  merchant: {
    id: number;
    email: string;
    status: string;
  };
  status: string;
  paymentDate: string; // RawDateYearFirstWithHyphen
  invoiceDate: string | null;
  pdfUrl: string | null;
  xmlUrl: string | null;
  vendor: string | null;
  vendorInvoiceId: unknown;
  error: unknown;
}

export const fromSummaryUIToRepositoryRequest = (
  daterange: B2BDateRange
): PaymentInvoicesRepositoryRequest => {
  if (!Guard.againstInvalidRawDateDayFirst(daterange.startDate).succeeded) {
    throw new RuntimeMerchantError(
      'La fecha de inicio para consultar resumen de facturas no es válida',
      'fromSummaryUIToRepositoryRequest::invalidDate::startDate'
    );
  }

  if (!Guard.againstInvalidRawDateDayFirst(daterange.endDate).succeeded) {
    throw new RuntimeMerchantError(
      'La fecha de fin para consultar resumen de facturas no es válida',
      'fromSummaryUIToRepositoryRequest::invalidDate::endDate'
    );
  }

  return {
    start: daterange.startDate
      .split('/')
      .reverse()
      .join('-') as RawDateYearFirstWithHyphen,
    end: daterange.endDate
      .split('/')
      .reverse()
      .join('-') as RawDateYearFirstWithHyphen,
  };
};
