import { UploadReceiptState } from '../../domain/entities/receipts';

export interface UploadReceiptUIDto {
  state: UploadReceiptState;
  data: {
    identifier: string;
    file: File;
    progress: number;
    message?: string;
    totalFilesProcessed?: number;
  };
  error: unknown;
}

export interface ProcessedReceiptResponseDto {
  type: string;
  filename: string;
  message: string;
  totalFilesProcessed: string;
}

export interface ReceiptHistoryDto {
  fileName: string;
  status: string;
  receiptsGenerated?: number;
  paymentDate: string;
}

export interface ReceiptHistoryRequest {
  pageNum: number;
  pageSize: number;
}
