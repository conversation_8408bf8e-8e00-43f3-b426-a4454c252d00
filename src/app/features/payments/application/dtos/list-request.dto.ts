import {
  RawDateDayFirst,
  RawDateYearFirstWithHyphen,
} from '@aplazo/merchant/shared';

export interface PaymentListRequestDto {
  date: RawDateDayFirst;
  pageNum: number;
  pageSize: number;
  frequency?: string | null;
  paymentStatus?: string | null;
  invoiceStatus?: string | null;
  merchantId?: number | null;
  paymentId?: number | null;
  orderBy?: string | null;
  order?: string | null;
  name?: string | null;
}

export interface PaymentListRepositoryDto {
  date: RawDateYearFirstWithHyphen;
  pageNum: number;
  pageSize: number;
  frequency?: string | null;
  paymentStatus?: string | null;
  invoiceStatus?: string | null;
  merchantId?: number | null;
  paymentId?: number | null;
  orderBy?: string | null;
  order?: string | null;
  name?: string | null;
}
