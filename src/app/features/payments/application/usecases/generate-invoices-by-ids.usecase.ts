import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, tap, throwError } from 'rxjs';
import { InvoiceGenerationByIDSResponse } from '../../domain/entities/payment';
import { PaymentsRepository } from '../../domain/repositories/payments.repository';

@Injectable()
export class GenerateInvoicesByIdsUseCase
  implements BaseUsecase<number[], Observable<void>>
{
  readonly #repository = inject(PaymentsRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(ids: number[]): Observable<void> {
    const idLoader = this.#loader.show();

    try {
      if (!Array.isArray(ids) || ids.length === 0) {
        throw new RuntimeMerchantError(
          'No se han seleccionado facturas para generar',
          'GenerateInvoicesByIdsUseCase::emptyIds'
        );
      }

      return this.#repository.generateInvoicesByIds(ids).pipe(
        tap(response => {
          if (!response) {
            throw new RuntimeMerchantError(
              'Error al generar las facturas',
              'GenerateInvoicesByIdsUseCase::generateInvoicesByIds::emptyResponse'
            );
          }

          const result = response.reduce(
            (
              acc: {
                error: {
                  count: number;
                  paymentIds: number[];
                };
                invoiced: {
                  count: number;
                  paymentIds: number[];
                };
              },
              curr: InvoiceGenerationByIDSResponse
            ) => {
              if (curr.status.toLowerCase() === 'error') {
                acc.error.count++;
                acc.error.paymentIds.push(curr.paymentId ?? curr.id);
                return acc;
              }

              if (curr.status.toLowerCase() === 'invoiced') {
                acc.invoiced.count++;
                acc.invoiced.paymentIds.push(curr.paymentId);
                return acc;
              }

              return acc;
            },
            {
              error: {
                count: 0,
                paymentIds: [],
              },
              invoiced: {
                count: 0,
                paymentIds: [],
              },
            }
          );

          if (result.invoiced.count > 0) {
            this.#notifier.success({
              title: `Facturas generadas: ${result.invoiced.paymentIds.join(', ')}`,
              message: `Total: ${result.invoiced.count}`,
            });
          }

          if (result.error.count > 0) {
            this.#notifier.warning({
              title: `Facturas con error: ${result.error.paymentIds.join(', ')}`,
              message: `Total: ${result.error.count}`,
            });
          }
        }),

        map(() => {
          return void 0;
        }),

        catchError(error => this.#errorHandler.handle<never>(error)),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
