import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RawDateDayFirst,
  RawDateYearFirstWithHyphen,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, of, take } from 'rxjs';
import { invalidDateErrorMessage } from '../../../shared/domain/messages-text';
import { PaymentSummaryResponse } from '../../domain/entities/summary-response';
import { PaymentSummaryRepository } from '../../domain/repositories/payment-summary.repository';

export const emptyPaymentSummaryResponse: PaymentSummaryResponse = {
  paymentDate: undefined,
  saleAmount: 0,
  feeAmount: 0,
  adjustmentPlus: 0,
  adjustmentMinus: 0,
  finalAmount: 0,
  payAmount: 0,
};

@Injectable({ providedIn: 'root' })
export class GetSummaryUseCase
  implements BaseUsecase<RawDateDayFirst, Observable<PaymentSummaryResponse>>
{
  readonly #repository: PaymentSummaryRepository<
    RawDateYearFirstWithHyphen,
    Observable<PaymentSummaryResponse>
  > = inject(PaymentSummaryRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(req: RawDateDayFirst): Observable<PaymentSummaryResponse> {
    const loaderId = this.#loader.show();

    try {
      if (!Guard.againstInvalidRawDateDayFirst(req).succeeded) {
        throw new RuntimeMerchantError(
          invalidDateErrorMessage,
          'GetSummaryUseCase::invalidDate'
        );
      }

      const repoDate = req
        .split('/')
        .reverse()
        .join('-') as RawDateYearFirstWithHyphen;

      return this.#repository.getByDate(repoDate).pipe(
        map(stats => stats ?? emptyPaymentSummaryResponse),

        map(stats => {
          const cleaned: PaymentSummaryResponse = {
            paymentDate: stats.paymentDate,
            saleAmount: stats.saleAmount ?? 0,
            feeAmount: stats.feeAmount ?? 0,
            adjustmentPlus: stats.adjustmentPlus ?? 0,
            adjustmentMinus: stats.adjustmentMinus ?? 0,
            finalAmount: stats.finalAmount ?? 0,
            payAmount: stats.payAmount ?? 0,
          };

          return cleaned;
        }),

        catchError(err => {
          return this.#errorHandler.handle(
            err,
            of(emptyPaymentSummaryResponse)
          );
        }),

        take(1),

        finalize(() => this.#loader.hide(loaderId))
      );
    } catch (error: unknown) {
      this.#loader.hide(loaderId);

      return this.#errorHandler.handle(error, of(emptyPaymentSummaryResponse));
    }
  }
}
