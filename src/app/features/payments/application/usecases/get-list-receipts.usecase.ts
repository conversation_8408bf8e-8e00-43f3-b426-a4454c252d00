import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RawDateYearFirstWithHyphen,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, of, take } from 'rxjs';
import {
  UploadReceiptsResponse,
  UploadReceiptsUIDto,
} from '../../domain/entities/list-response';
import { ReceiptListRepository } from '../../domain/repositories/receipt-list.repository';
import { ReceiptHistoryRequest } from '../dtos/upload-receipts.dto';

export const emptyListResponse: UploadReceiptsUIDto = {
  content: [],
  number: 0,
  size: 10,
  totalElements: 0,
  totalPages: 0,
  hasContent: false,
  numberOfElements: 0,
  first: true,
  last: true,
};

@Injectable({ providedIn: 'root' })
export class GetListReceiptUseCase
  implements BaseUsecase<ReceiptHistoryRequest, Observable<UploadReceiptsUIDto>>
{
  readonly #repository: ReceiptListRepository<
    ReceiptHistoryRequest,
    Observable<UploadReceiptsResponse>
  > = inject(ReceiptListRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(req: ReceiptHistoryRequest): Observable<UploadReceiptsUIDto> {
    const loaderId = this.#loader.show();

    try {
      Guard.againstInvalidNumbers(req.pageNum, 'GetListReceiptUseCase');
      Guard.againstInvalidNumbers(req.pageSize, 'GetListReceiptUseCase');

      return this.#repository
        .getList({
          pageNum: req.pageNum,
          pageSize: req.pageSize,
        })
        .pipe(
          map(stats => stats ?? emptyListResponse),

          map(stats => {
            const mapped = stats.content.map(item => {
              const createdAt = new Date(item.createdAt);

              return {
                ...item,
                createdAt: createdAt,
                paymentDate: item.paymentDate as RawDateYearFirstWithHyphen,
              };
            });

            return {
              ...stats,
              content: mapped,
            };
          }),
          catchError(err => {
            return this.#errorHandler.handle<Observable<UploadReceiptsUIDto>>(
              err,
              of(emptyListResponse)
            );
          }),

          take(1),

          finalize(() => this.#loader.hide(loaderId))
        );
    } catch (error: unknown) {
      this.#loader.hide(loaderId);

      return this.#errorHandler.handle<Observable<UploadReceiptsUIDto>>(
        error,
        of(emptyListResponse)
      );
    }
  }
}
