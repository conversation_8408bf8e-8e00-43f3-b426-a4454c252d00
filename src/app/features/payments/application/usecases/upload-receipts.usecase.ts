/* eslint-disable no-case-declarations */
import {
  HttpDownloadProgressEvent,
  HttpErrorResponse,
  HttpEvent,
  HttpEventType,
} from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  combineLatest,
  combineLatestWith,
  EMPTY,
  map,
  Observable,
  of,
  OperatorFunction,
  pipe,
  retry,
  scan,
  takeWhile,
  tap,
} from 'rxjs';
import { areAllStateTracked } from '../../domain/entities/receipts';
import { ReceiptsRepository } from '../../domain/repositories/receipts.repository';
import {
  ProcessedReceiptResponseDto,
  ReceiptHistoryDto,
  UploadReceiptUIDto,
} from '../dtos/upload-receipts.dto';

@Injectable()
export class UploadReceiptsUseCase
  implements
    BaseUsecase<
      { identifier: string; files: File[]; paymentDate: string },
      Observable<UploadReceiptUIDto[]>
    >
{
  readonly #repository = inject(ReceiptsRepository);
  readonly #usecaseError = inject(UseCaseErrorHandler);
  readonly #notifier = inject(NotifierService);

  execute(request: {
    identifier: string;
    files: File[];
    multiple?: boolean;
    paymentDate: string;
  }): Observable<UploadReceiptUIDto[]> {
    try {
      const clonedFiles = request.files.slice(0);
      const requests: Observable<UploadReceiptUIDto>[] = [];
      const multipleEnabled = request.multiple ?? false;

      if (!multipleEnabled && clonedFiles.length > 1) {
        throw new RuntimeMerchantError(
          'Solo se puede cargar un archivo a la vez',
          'UploadReceiptsUseCase::execute::multipleFiles'
        );
      }

      for (const f of clonedFiles) {
        if (!f.name || !f.size) {
          throw new RuntimeMerchantError(
            'Para completar la operación es necesario al menos un archivo',
            'UploadReceiptsUseCase::execute::emptyFiles'
          );
        }

        const formData = new FormData();
        formData.append('files', f);
        requests.push(
          this.#repository.uploadFile(request.identifier, formData).pipe(
            this.#uploadResponseToValidState(request.identifier, f),

            catchError(err => {
              if (
                err instanceof HttpErrorResponse &&
                err.status === 400 &&
                err.error?.error &&
                err.error?.error.startsWith('File already exists')
              ) {
                this.#notifier.warning({
                  title: 'Lo sentimos la carga falló',
                  message: `El archivo ${f.name} ya existe. Por favor, omita la carga de este archivo.`,
                });

                return of({
                  state: 'uploadError',

                  data: {
                    progress: 0,
                    file: f,
                    identifier: request.identifier,
                  },
                  error: {
                    message: 'El archivo ya existe',
                    filename: f.name,
                    eventType: HttpEventType.ResponseHeader,
                  },
                } satisfies UploadReceiptUIDto);
              }
              if (err instanceof HttpErrorResponse && err.status === 413) {
                this.#notifier.warning({
                  title: 'Lo sentimos la carga falló',
                  message: `El archivo ${f.name} excede el tamaño permitido. Por favor, omita la carga de este archivo.`,
                });

                return of({
                  state: 'uploadError',

                  data: {
                    progress: 0,
                    file: f,
                    identifier: request.identifier,
                  },
                  error: {
                    message: 'Archivo excede el tamaño permitido',
                    filename: f.name,
                    eventType: HttpEventType.ResponseHeader,
                  },
                } satisfies UploadReceiptUIDto);
              }
              throw err;
            })
          )
        );
      }

      return combineLatest(requests).pipe(
        combineLatestWith(
          this.#repository.retrieveEvents().pipe(
            this.#retrieveProcessingStatus(),

            retry({
              resetOnSuccess: true,
              delay: err => {
                console.log('retrying::', err);
                /** Handle timeout usecase and complete when the
                 * error is untracked
                 */
                if (err instanceof HttpErrorResponse && err.status === 0) {
                  return of(true);
                }

                return EMPTY;
              },
            })
          )
        ),

        map(([state, processedReceipts]) => {
          const newState = state.slice(0);

          processedReceipts.forEach(event => {
            const index = newState.findIndex(
              s => s.data.file.name === event.filename
            );

            if (event.type === 'INFO') {
              if (index >= 0) {
                newState[index].state = 'ready';
                newState[index].data.message = event.message;
                newState[index].data.totalFilesProcessed = Number(
                  event.totalFilesProcessed
                );
              }
            }

            if (event.type === 'ERROR') {
              if (index >= 0) {
                newState[index].state = 'processingError';
                newState[index].data.message = event.message;
                newState[index].data.totalFilesProcessed = 0;
              }
            }
          });

          return newState;
        }),
        tap(items => {
          const fileHistory = items.find(
            f => !['loading', 'loaded'].includes(f.state)
          );
          if (fileHistory) {
            this.#postReceiptHistory(
              fileHistory,
              request.paymentDate
            ).subscribe();
          }
        }),
        takeWhile(states => {
          const onlyStates = states.map(s => s.state);

          const allStatesAreTracked = areAllStateTracked(onlyStates);

          return !allStatesAreTracked;
        }, true),

        catchError(err => this.#usecaseError.handle<never>(err))
      );
    } catch (error) {
      return this.#usecaseError.handle(error, EMPTY);
    }
  }

  #retrieveProcessingStatus(): OperatorFunction<
    HttpEvent<any>,
    ProcessedReceiptResponseDto[]
  > {
    return pipe(
      map<HttpEvent<any>, ProcessedReceiptResponseDto[]>(event => {
        switch (event.type) {
          case HttpEventType.DownloadProgress:
            const downloadEvent = event as HttpDownloadProgressEvent;
            const events = JSON.stringify(
              downloadEvent.partialText
                ?.replaceAll('data:', '')
                .split('\n')
                .filter(Boolean) ?? '[]'
            );

            const parsed = JSON.parse(events);

            const final = parsed
              .map((i: string) => JSON.parse(i))
              .map((item: any) => {
                if (item.type === 'ERROR') {
                  return {
                    type: item.type,
                    filename: item.data?.file ?? 'unknown',
                    message: item.message,
                    totalFilesProcessed: item.data?.subFiles ?? '0',
                  };
                }

                return {
                  type: item.type,
                  filename: item.data.file,
                  message: `Hemos generado ${item.data.subFiles} recibos.`,
                  totalFilesProcessed: item.data.subFiles,
                };
              });

            return final;
          default:
            return [] as ProcessedReceiptResponseDto[];
        }
      })
    );
  }

  #postReceiptHistory(
    fileHistory: UploadReceiptUIDto,
    date: string
  ): Observable<any> {
    const req: ReceiptHistoryDto = {
      fileName: fileHistory.data.file.name,
      status: fileHistory.state,
      receiptsGenerated: fileHistory.data.totalFilesProcessed,
      paymentDate: date,
    };
    return this.#repository.postReceiptHistory(req).pipe(
      catchError(error => {
        console.warn(error);
        return EMPTY;
      })
    );
  }

  #uploadResponseToValidState(
    identifier: string,
    file: File
  ): OperatorFunction<HttpEvent<any>, UploadReceiptUIDto> {
    return pipe(
      scan<HttpEvent<any>, UploadReceiptUIDto>(
        (acc, event) => {
          switch (event.type) {
            case HttpEventType.Sent:
              return {
                state: 'loading',
                data: {
                  progress: 0,
                  file,
                  identifier,
                },
                error: null,
              } satisfies UploadReceiptUIDto;

            case HttpEventType.UploadProgress:
              // Compute and show the % done:
              // eslint-disable-next-line no-case-declarations
              const percentDone = event.total
                ? Math.round((100 * event.loaded) / event.total)
                : 0;

              return {
                state: 'loading',
                data: {
                  progress: percentDone,
                  file,
                  identifier,
                },
                error: null,
              } satisfies UploadReceiptUIDto;

            case HttpEventType.Response:
              return {
                state: event.ok ? 'loaded' : 'uploadError',
                data: {
                  progress: acc.data.progress,
                  file,
                  identifier,
                  message: 'Archivo cargado',
                },
                error: event.ok
                  ? null
                  : {
                      message: 'Error al subir el archivo: ' + file.name,
                      filename: file.name,
                      eventType: event.type,
                    },
              } satisfies UploadReceiptUIDto;

            default:
              return {
                state: acc.state,
                data: {
                  progress: acc.data.progress,
                  file,
                  identifier,
                },
                error: acc.error ?? {
                  message: (acc.error as any)?.message ?? '',
                  eventType: event.type,
                  filename: file.name,
                },
              } satisfies UploadReceiptUIDto;
          }
        },
        {
          state: 'idle',
          data: {
            progress: 0,
            file,
            identifier,
          },
          error: null,
        } satisfies UploadReceiptUIDto
      )
    );
  }
}
