import { inject, Injectable } from '@angular/core';
import {
  B2BDateRange,
  BaseUsecase,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, of, take } from 'rxjs';
import {
  fromSummaryUIToRepositoryRequest,
  PaymentInvoicesSummaryUI,
} from '../../domain/entities/invoices';
import { PaymentInvoicesRepository } from '../../domain/repositories/invoices.repository';

export const defaultPaymentSummaryUI: PaymentInvoicesSummaryUI = {
  processing: {
    label: 'En proceso',
    amount: 0,
  },
  invoiced: {
    label: 'Facturado',
    amount: 0,
  },
  pending: {
    label: 'Pendiente',
    amount: 0,
  },
  error: {
    label: 'Error',
    amount: 0,
  },
  excluded: {
    label: 'Excluido',
    amount: 0,
  },
};

@Injectable()
export class GetInvoicesSummaryUseCase
  implements BaseUsecase<B2BDateRange, Observable<PaymentInvoicesSummaryUI>>
{
  readonly #repository = inject(PaymentInvoicesRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: B2BDateRange): Observable<PaymentInvoicesSummaryUI> {
    const id = this.#loader.show();

    try {
      const request = fromSummaryUIToRepositoryRequest(args);

      return this.#repository.getSummary(request).pipe(
        map(resp => {
          return {
            processing: {
              label: 'En proceso',
              amount: resp?.processing ?? 0,
            },
            invoiced: {
              label: 'Facturado',
              amount: resp?.invoiced ?? 0,
            },
            pending: {
              label: 'Pendiente',
              amount: resp?.pending ?? 0,
            },
            error: {
              label: 'Error',
              amount: resp?.error ?? 0,
            },
            excluded: {
              label: 'Excluido',
              amount: resp?.excluded ?? 0,
            },
          };
        }),

        catchError(error =>
          this.#errorHandler.handle(error, of(defaultPaymentSummaryUI))
        ),

        take(1),

        finalize(() => this.#loader.hide(id))
      );
    } catch (error) {
      this.#loader.hide(id);

      return this.#errorHandler.handle(error, of(defaultPaymentSummaryUI));
    }
  }
}
