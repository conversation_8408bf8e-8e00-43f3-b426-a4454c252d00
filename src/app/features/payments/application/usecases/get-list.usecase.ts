import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RawDateYearFirstWithHyphen,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  combineLatestWith,
  finalize,
  map,
  Observable,
  of,
  take,
} from 'rxjs';
import { invalidDateErrorMessage } from '../../../shared/domain/messages-text';
import { PaymentInvoicesStatusResponse } from '../../domain/entities/invoices';
import { PaymentListResponse } from '../../domain/entities/list-response';
import { PaymentListRepository } from '../../domain/repositories/payment-list.repository';
import { PaymentListRequestDto } from '../dtos/list-request.dto';

export const emptyListResponse: PaymentListResponse = {
  content: [],
  number: 0,
  size: 10,
  totalElements: 0,
  totalPages: 0,
  hasContent: false,
  numberOfElements: 0,
  first: true,
  last: true,
};

@Injectable({ providedIn: 'root' })
export class GetListUseCase
  implements BaseUsecase<PaymentListRequestDto, Observable<PaymentListResponse>>
{
  readonly #repository = inject(PaymentListRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(req: PaymentListRequestDto): Observable<PaymentListResponse> {
    const loaderId = this.#loader.show();

    try {
      if (!Guard.againstInvalidRawDateDayFirst(req.date).succeeded) {
        throw new RuntimeMerchantError(
          invalidDateErrorMessage,
          'GetListUseCase::invalidDate'
        );
      }

      Guard.againstInvalidNumbers(req.pageNum, 'GetListUseCase');
      Guard.againstInvalidNumbers(req.pageSize, 'GetListUseCase');

      const repoDate = req.date
        .split('/')
        .reverse()
        .join('-') as RawDateYearFirstWithHyphen;

      const frequencyToUppercase =
        req.frequency && req.frequency.length > 0
          ? req.frequency.toUpperCase()
          : null;

      return this.#repository
        .getList({
          date: repoDate,
          pageNum: req.pageNum,
          pageSize: req.pageSize,
          frequency: frequencyToUppercase,
          paymentStatus: req.paymentStatus,
          invoiceStatus: req.invoiceStatus,
          paymentId: req.paymentId,
          merchantId: req.merchantId,
          name: req.name,
        })
        .pipe(
          combineLatestWith(
            this.#repository.getInvoiceStatusList({
              start: repoDate,
              end: repoDate,
              frequency: frequencyToUppercase,
              paymentStatus: req.paymentStatus,
              invoiceStatus: req.invoiceStatus,
              paymentId: req.paymentId,
              merchantId: req.merchantId,
              name: req.name,
            })
          ),

          map(([payments, invoices]) => {
            if (!payments) {
              return emptyListResponse;
            }

            const serialized = invoices.reduce(
              (acc, invoice) => {
                if (!acc[invoice.paymentId]) {
                  acc[invoice.paymentId] = invoice;
                }

                return acc;
              },
              {} as Record<number, PaymentInvoicesStatusResponse>
            );

            const content =
              payments.content && Array.isArray(payments.content)
                ? payments.content.map(p => ({
                    ...p,
                    invoiceStatus: serialized[p.id]?.status || null,
                  }))
                : [];

            const withInvoiceStatus = {
              ...payments,
              content,
            };

            return withInvoiceStatus;
          }),

          catchError(err => {
            return this.#errorHandler.handle<Observable<PaymentListResponse>>(
              err,
              of(emptyListResponse)
            );
          }),

          take(1),

          finalize(() => this.#loader.hide(loaderId))
        );
    } catch (error: unknown) {
      this.#loader.hide(loaderId);

      return this.#errorHandler.handle<Observable<PaymentListResponse>>(
        error,
        of(emptyListResponse)
      );
    }
  }
}
