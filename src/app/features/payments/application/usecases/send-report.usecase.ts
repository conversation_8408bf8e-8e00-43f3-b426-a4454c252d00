import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, of, take, tap } from 'rxjs';
import { invalidDateErrorMessage } from '../../../shared/domain/messages-text';
import { REPORT_TYPES, ReportKey } from '../../domain/entities/reports';
import { PaymentReportRepository } from '../../domain/repositories/payment-report.repository';
import {
  PaymentReportRepositoryDto,
  PaymentReportRequestDto,
} from '../dtos/report-request.dto';

@Injectable({ providedIn: 'root' })
export class SendReportUseCase
  implements BaseUsecase<PaymentReportRequestDto, Observable<void>>
{
  readonly #repository: PaymentReportRepository<
    PaymentReportRepositoryDto,
    Observable<void>
  > = inject(PaymentReportRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: PaymentReportRequestDto): Observable<void> {
    const loaderId = this.#loader.show();

    try {
      if (!Guard.againstInvalidRawDateDayFirst(args.date).succeeded) {
        throw new RuntimeMerchantError(
          invalidDateErrorMessage,
          'SendReportUseCase::invalidDate'
        );
      }

      if (!Guard.againstNullOrUndefined(args, 'email').succeeded) {
        throw new RuntimeMerchantError(
          'Se requiere un email',
          'SendReportUseCase::invalidEmail'
        );
      }

      if (!Guard.againstNullOrUndefined(args, 'type').succeeded) {
        throw new RuntimeMerchantError(
          'Se requiere un tipo de reporte',
          'SendReportUseCase::invalidType'
        );
      }

      const type = args.type as ReportKey;

      if (!REPORT_TYPES[type]) {
        throw new RuntimeMerchantError(
          'Tipo de reporte no valido',
          'SendReportUseCase::invalidType'
        );
      }

      return this.#repository
        .getReport({
          date: args.date,
          emails: args.email,
          type: REPORT_TYPES[type],
        })
        .pipe(
          tap(() => {
            this.#notifier.success({
              title: 'Reporte enviado',
              message:
                'En breve recibira un correo electrónico con el reporte.',
            });
          }),

          catchError(err => {
            return this.#errorHandler.handle(err, of(undefined));
          }),

          take(1),

          finalize(() => this.#loader.hide(loaderId))
        );
    } catch (error) {
      this.#loader.hide(loaderId);

      return this.#errorHandler.handle(error, of(undefined));
    }
  }
}
