import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, tap, throwError } from 'rxjs';
import {
  InvoiceGenerationResponse,
  toInvoiceGenerationRepositoryRequest,
} from '../../domain/entities/payment';
import { PaymentsRepository } from '../../domain/repositories/payments.repository';

@Injectable()
export class GenerateInvoicesUseCase
  implements BaseUsecase<Date, Observable<InvoiceGenerationResponse>>
{
  readonly #repository = inject(PaymentsRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(date: Date): Observable<InvoiceGenerationResponse> {
    const idLoader = this.#loader.show();

    try {
      const request = toInvoiceGenerationRepositoryRequest(date);

      return this.#repository.generateInvoices(request).pipe(
        tap(response => {
          this.#notifier.success({
            title: 'Facturas generadas correctamente',
            message: `${response.invoiced}`,
          });

          this.#notifier.warning({
            title: 'Facturas con error',
            message: `${response.error}`,
          });

          this.#notifier.info({
            title: 'Facturas en proceso',
            message: `${response.processing}`,
          });
        }),

        catchError(error => this.#errorHandler.handle<never>(error)),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
