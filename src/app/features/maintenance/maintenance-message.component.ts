import { AsyncPipe } from '@angular/common';
import { Component, inject, On<PERSON><PERSON>roy, SecurityContext } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { I18NService } from '@aplazo/i18n';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { map, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-maintenance-message',
  imports: [AsyncPipe, AplazoLogoComponent],
  template: `
    @if (textUI$ | async; as textUI) {
      <main
        class="flex flex-col justify-center gap-16 w-full max-w-md mx-auto py-16">
        <aplz-ui-logo size="md"></aplz-ui-logo>
        <figure class="aspect-video w-3/4 mx-auto">
          <img
            data-testid="maintenance-image"
            [src]="textUI?.imageUrl"
            alt="under maintenance"
            class="w-full h-full object-cover" />
        </figure>
        <h1 class="text-2xl font-medium text-center">{{ textUI?.title }}</h1>
        <p class="text-pretty text-lg text-dark-secondary leading-9">
          {{ textUI?.body }}
        </p>
      </main>
    }
  `,
})
export class MaintenanceMessageComponent implements OnDestroy {
  readonly #i18n = inject(I18NService);
  readonly #sanitizer = inject(DomSanitizer);

  readonly #destroy = new Subject<void>();

  readonly textUI$ = this.#i18n
    .getTranslateObjectByKey<{
      title: string;
      body: string;
      imageUrl: string;
    }>({
      key: 'unavailableScreen',
      scope: 'maintenance',
    })
    .pipe(
      map(d => {
        return {
          ...d,
          imageUrl: this.#sanitizer.sanitize(SecurityContext.URL, d.imageUrl),
        };
      }),
      takeUntil(this.#destroy)
    );

  ngOnDestroy(): void {
    this.#destroy.next();
    this.#destroy.complete();
  }
}
