import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { invalidWebpagePatternError } from './messages-text';
import { webpagePattern } from './patterns';

export class WebPage {
  #value: string;
  get value(): string {
    return this.#value;
  }

  private constructor(value: string) {
    this.#value = value;
  }

  static create(value: string): WebPage {
    if (!value) {
      throw new RuntimeMerchantError(
        'El valor de la página web no puede estar vacío.',
        'WebPage::create::emptyWebpageValue'
      );
    }

    const validatedPatterns = webpagePattern.exec(value);

    if (!validatedPatterns) {
      throw new RuntimeMerchantError(
        invalidWebpagePatternError,
        'WebPage::create::invalidEmailPattern'
      );
    }

    return new WebPage(value);
  }
}
