export const invalidApiBaseUrlError = 'No se proporciono la URL de la API';
export const expectedErrorTitle = 'Detectamos un error, ';
export const unexpectedErrorTitle = 'Parece que algo salió mal, ';
export const invalidDateErrorMessage = 'Fecha inválida';
export const invalidDateFormatErrorMessage = 'Formato de fecha inválido';
export const successNewMerchantMessage =
  'El comercio ha sido creado exitosamente.';
export const alreadyRegisteredEmailMessage =
  'El correo ya se encuentra registrado';
export const invalidWebpagePatternError =
  'La página web ingresada no es válida. Ej: https://www.example.com';
export const invalidMerchantUpdateStatusError =
  'Ingrese un estatus válido para el comercio';
export const badRequestMerchantStatusError =
  'Revise el nuevo estatus del comercio y vuelva a intentarlo. Si el problema persiste, contacte a soporte.';
export const formWithErrorsNotifierTitle = 'Revise la información ingresada';
export const formWithErrorsNotifierMessage =
  'Los campos para edición no pueden estar vacíos o contener errores';
export const formWithNoChangesTitle = 'No hay cambios para guardar';
