import { Injectable } from '@angular/core';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { BehaviorSubject } from 'rxjs';
import { HelicarrierRoute } from '../../../core/domain/route-names';
import { MenuStore } from '../../application/services/menu.store';

export const menuInvalidDefaultErrorMessage =
  'El menú no es válido. Por favor, verifique la información.';

@Injectable({ providedIn: 'root' })
export class SimpleMenuStore implements MenuStore {
  readonly #menu = new BehaviorSubject<HelicarrierRoute[]>([]);

  readonly menu$ = this.#menu.asObservable();

  getMenu(): HelicarrierRoute[] {
    return this.#menu.getValue();
  }

  setMenu(menu: HelicarrierRoute[]): void {
    if (!menu || !Array.isArray(menu)) {
      throw new RuntimeMerchantError(
        menuInvalidDefaultErrorMessage,
        'MenuStore::setMenu::invalidMenu'
      );
    }

    this.#menu.next(menu);
  }
}
