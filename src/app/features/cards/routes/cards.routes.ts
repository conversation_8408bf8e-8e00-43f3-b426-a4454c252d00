import { Route } from '@angular/router';
import { LocationsPageComponent } from '../pages/locations-page.component';
import { BulkOrdersPageComponent } from '../pages/bulk-orders-page.component';
import { ROUTES_CONFIG } from '../../core/domain/route-names';

export default [
  {
    path: '',
    redirectTo: ROUTES_CONFIG.cards,
    pathMatch: 'full',
  },
  {
    path: ROUTES_CONFIG.cards,
    children: [
      {
        path: '',
        redirectTo: ROUTES_CONFIG.cardsLocations,
        pathMatch: 'full',
      },
      {
        path: ROUTES_CONFIG.cardsLocations,
        component: LocationsPageComponent,
        title: 'Ubicaciones de Tarjetas',
      },
      {
        path: ROUTES_CONFIG.cardsBulkOrders,
        component: BulkOrdersPageComponent,
        title: '<PERSON><PERSON><PERSON> Ma<PERSON> Tarjet<PERSON>',
      },
    ],
  },
] satisfies Route[];
