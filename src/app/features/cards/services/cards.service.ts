import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../core/infra/config/environments';

export interface CardLocation {
  location_id: string;
  name: string;
  address1: string;
  address2?: string | null;
  city: string;
  state: string;
  postalCode: string;
}

export interface CardLocationsResponse {
  content: CardLocation[];
  pageable: {
    pageNumber: number;
    pageSize: number;
    sort: any[];
    offset: number;
    paged: boolean;
    unpaged: boolean;
  };
  totalPages: number;
  totalElements: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  size: number;
  number: number;
  sort: any[];
  empty: boolean;
}

export interface CreateLocationRequest {
  name: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  parentLocation: string;
  parentLocationType: number;
}

export interface BulkOrderRequest {
  numberOfCards: number;
  shipToName: string;
  shipToAddress: string;
  shipToCity: string;
  shipToStateOrProvince: string;
  shipToPostalCode: string;
  locationId: string;
}

@Injectable({
  providedIn: 'root',
})
export class CardsService {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #baseUrl = this.#environment.gatewayUrl;

  getLocations(
    page: number = 0,
    size: number = 12
  ): Observable<CardLocationsResponse> {
    const params = {
      page: page.toString(),
      size: size.toString(),
    };

    return this.#http.get<CardLocationsResponse>(
      `${this.#baseUrl}/physical-card/v1/locations`,
      { params }
    );
  }

  createLocation(request: CreateLocationRequest): Observable<CardLocation> {
    return this.#http.post<CardLocation>(
      `${this.#baseUrl}/physical-card/v1/locations`,
      request
    );
  }

  createBulkOrder(request: BulkOrderRequest): Observable<any> {
    return this.#http.post<any>(
      `${this.#baseUrl}/physical-card/v1/bulk-orders`,
      request
    );
  }
}
