<aplz-ui-card>
  <div class="flex flex-col gap-4">
    <div class="flex justify-between items-center">
      <h2 class="text-lg font-semibold">Ubicaciones de Tarjetas</h2>
      <button
        aplzButton
        aplzAppearance="solid"
        aplzColor="dark"
        size="md"
        (click)="createLocation()">
        Registrar ubicación
      </button>
    </div>
    <div class="overflow-x-auto">
      <table aplzSimpleTable aria-label="Locations List">
        <tr aplzSimpleTableHeaderRow>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            ID
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Nombre
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Dirección
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Ciudad
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Estado
          </th>
          <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
            Código Postal
          </th>
        </tr>
        @if (locations().length > 0) {
          @for (item of locations(); track item.location_id) {
            <tr aplzSimpleTableBodyRow [striped]="true">
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.location_id }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.name }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.address1 }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.city }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.state }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.postalCode }}
              </td>
            </tr>
          }
        } @else {
          <tr aplzSimpleTableBodyRow>
            <td aplzSimpleTableBodyCell colspan="6" class="text-center py-8">
              No hay ubicaciones registradas.
            </td>
          </tr>
        }
      </table>
    </div>
    @if (totalPages() > 1) {
      <div class="mt-3 flex justify-center">
        <aplz-ui-pagination
          [totalPages]="totalPages()"
          [currentPage]="currentPage()"
          (selectedPage)="changePage($event)">
        </aplz-ui-pagination>
      </div>
    }
  </div>
</aplz-ui-card>
