import { Component, ChangeDetectionStrategy, inject } from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { DialogRef } from '@ngneat/dialog';
import { DialogService } from '@ngneat/dialog';
import { take, tap } from 'rxjs/operators';
import { BulkOrderFormComponent } from '../components/bulk-order-form.component';

@Component({
  selector: 'app-bulk-orders-page',
  imports: [AplazoButtonComponent],
  template: `
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-semibold text-gray-900">
          Órdenes Masivas de Tarjetas
        </h1>
        <button
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          (click)="createBulkOrder()">
          <span class="mr-2">Crear</span>
          orden
        </button>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <p class="text-gray-600">
          Desde aquí puedes crear órdenes masivas de tarjetas físicas. Haz clic
          en "Crear orden" para comenzar.
        </p>
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BulkOrdersPageComponent {
  private readonly dialog = inject(DialogService);

  createBulkOrder() {
    const dialog: DialogRef<any, any> = this.dialog.open(
      BulkOrderFormComponent,
      {
        enableClose: false,
      }
    );

    dialog.afterClosed$
      .pipe(
        take(1),
        tap(result => {
          if (result?.hasConfirmation) {
            // Optionally refresh data or show success message
            console.log('Bulk order created successfully');
          }
        })
      )
      .subscribe();
  }
}
