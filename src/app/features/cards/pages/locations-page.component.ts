import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { DialogRef, DialogService } from '@ngneat/dialog';
import { take, tap } from 'rxjs';
import { LocationFormComponent } from '../components/location-form.component';
import { CardLocationsResponse, CardsService } from '../services/cards.service';

@Component({
  selector: 'app-locations-page',
  imports: [
    AplazoButtonComponent,
    AplazoCardComponent,
    AplazoSimpleTableComponents,
    AplazoPaginationComponent,
  ],
  templateUrl: './locations-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LocationsPageComponent implements OnInit {
  private readonly cardsService = inject(CardsService);
  private readonly dialog = inject(DialogService);

  private readonly page = signal(0);
  private readonly size = signal(12);
  private readonly locationsResponse = signal<CardLocationsResponse | null>(
    null
  );
  private readonly isLoading = signal(false);
  private isInitialized = false;

  readonly locations = computed(() => this.locationsResponse()?.content ?? []);
  readonly totalPages = computed(
    () => this.locationsResponse()?.totalPages ?? 1
  );
  readonly currentPage = computed(() => this.page());

  ngOnInit() {
    this.fetchLocations();
    this.isInitialized = true;
  }

  fetchLocations() {
    this.isLoading.set(true);
    this.cardsService.getLocations(this.page(), this.size()).subscribe({
      next: res => {
        this.locationsResponse.set(res);
        this.isLoading.set(false);
      },
      error: error => {
        console.error('Failed to fetch card locations:', error);
        this.locationsResponse.set(null);
        this.isLoading.set(false);
      },
    });
  }

  changePage(page: number) {
    // Prevent calls during initialization
    if (!this.isInitialized) {
      return;
    }

    // Pagination component sends 0-based page numbers (as per documentation)
    // Use the page number directly
    const newPageIndex = Math.max(0, page);

    // Prevent unnecessary API calls if we're already on the requested page
    if (newPageIndex === this.page()) {
      return;
    }

    this.page.set(newPageIndex);
    this.fetchLocations();
  }

  createLocation() {
    const dialog: DialogRef<any, any> = this.dialog.open(
      LocationFormComponent,
      {
        enableClose: false,
      }
    );

    dialog.afterClosed$
      .pipe(
        take(1),
        tap(result => {
          if (result?.hasConfirmation) {
            this.fetchLocations();
          }
        })
      )
      .subscribe();
  }
}
