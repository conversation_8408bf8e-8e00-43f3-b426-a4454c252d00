import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { NotifierService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import {
  CardsService,
  BulkOrderRequest,
  CardLocation,
  CardLocationsResponse,
} from '../services/cards.service';
import { take, tap, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import { signal } from '@angular/core';

interface MexicanState {
  code: string;
  name: string;
}

@Component({
  selector: 'app-bulk-order-form',
  template: `
    <aplz-ui-card>
      <h1 class="text-2xl font-medium mt-8 mx-6">Nueva orden masiva</h1>
      <form [formGroup]="form" (ngSubmit)="finish()" class="mx-6 mt-12">
        <!-- Number of Cards field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Número de tarjetas</aplz-ui-form-label>
          <input type="number" aplzFormInput formControlName="numberOfCards" />
          <ng-container aplzFormError>
            @if (
              numberOfCardsControl.touched &&
              numberOfCardsControl.hasError('required')
            ) {
              <p>El número de tarjetas es requerido</p>
            }
            @if (
              numberOfCardsControl.touched &&
              numberOfCardsControl.hasError('min')
            ) {
              <p>Debe ser al menos 1 tarjeta</p>
            }
            @if (
              numberOfCardsControl.touched &&
              numberOfCardsControl.hasError('max')
            ) {
              <p>No puede exceder 10,000 tarjetas</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <!-- Ship To Name field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Nombre para envío</aplz-ui-form-label>
          <input type="text" aplzFormInput formControlName="shipToName" />
          <ng-container aplzFormError>
            @if (
              shipToNameControl.touched &&
              shipToNameControl.hasError('required')
            ) {
              <p>El nombre para envío es requerido</p>
            }
            @if (
              shipToNameControl.touched &&
              shipToNameControl.hasError('maxlength')
            ) {
              <p>No puede exceder 100 caracteres</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <!-- Ship To Address field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Dirección de envío</aplz-ui-form-label>
          <input type="text" aplzFormInput formControlName="shipToAddress" />
          <ng-container aplzFormError>
            @if (
              shipToAddressControl.touched &&
              shipToAddressControl.hasError('required')
            ) {
              <p>La dirección de envío es requerida</p>
            }
            @if (
              shipToAddressControl.touched &&
              shipToAddressControl.hasError('maxlength')
            ) {
              <p>No puede exceder 200 caracteres</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <!-- Ship To City field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Ciudad de envío</aplz-ui-form-label>
          <input type="text" aplzFormInput formControlName="shipToCity" />
          <ng-container aplzFormError>
            @if (
              shipToCityControl.touched &&
              shipToCityControl.hasError('required')
            ) {
              <p>La ciudad de envío es requerida</p>
            }
            @if (
              shipToCityControl.touched &&
              shipToCityControl.hasError('maxlength')
            ) {
              <p>No puede exceder 50 caracteres</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <!-- Ship To State or Province field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Estado/Provincia de envío</aplz-ui-form-label>
          <select aplzFormSelect formControlName="shipToStateOrProvince">
            <option value="">Selecciona un estado</option>
            @for (state of mexicanStates; track state.code) {
              <option [value]="state.code">{{ state.name }}</option>
            }
          </select>
          <ng-container aplzFormError>
            @if (
              shipToStateOrProvinceControl.touched &&
              shipToStateOrProvinceControl.hasError('required')
            ) {
              <p>El estado/provincia de envío es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <!-- Ship To Postal Code field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Código postal de envío</aplz-ui-form-label>
          <input type="text" aplzFormInput formControlName="shipToPostalCode" />
          <ng-container aplzFormError>
            @if (
              shipToPostalCodeControl.touched &&
              shipToPostalCodeControl.hasError('required')
            ) {
              <p>El código postal de envío es requerido</p>
            }
            @if (
              shipToPostalCodeControl.touched &&
              shipToPostalCodeControl.hasError('pattern')
            ) {
              <p>Formato inválido. Use: 12345, 12345-1234 o K1A 0B1</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <!-- Location ID field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Ubicación</aplz-ui-form-label>
          <select aplzFormSelect formControlName="locationId">
            <option value="">Selecciona una ubicación</option>
            @for (location of locations(); track location.location_id) {
              <option [value]="location.location_id">
                {{ location.name }} - {{ location.city }}, {{ location.state }}
              </option>
            }
          </select>
          <ng-container aplzFormError>
            @if (
              locationIdControl.touched &&
              locationIdControl.hasError('required')
            ) {
              <p>La ubicación es requerida</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <div class="flex justify-end mb-8 mt-10 gap-4">
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="close(false)">
            Cancelar
          </button>
          <button
            aplzButton
            type="submit"
            aplzAppearance="solid"
            aplzColor="dark"
            size="md"
            [disabled]="form.disabled">
            <span class="mr-2">Crear</span>
            orden
          </button>
        </div>
      </form>
    </aplz-ui-card>
  `,
  imports: [
    ReactiveFormsModule,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
    AplazoCardComponent,
  ],
})
export class BulkOrderFormComponent {
  readonly #dialogRef: DialogRef<any, any> = inject(DialogRef);
  readonly #notifier = inject(NotifierService);
  readonly #cardsService = inject(CardsService);

  readonly locations = signal<CardLocation[]>([]);

  readonly mexicanStates: MexicanState[] = [
    { code: 'AG', name: 'Aguascalientes' },
    { code: 'BC', name: 'Baja California' },
    { code: 'BS', name: 'Baja California Sur' },
    { code: 'CM', name: 'Campeche' },
    { code: 'CS', name: 'Chiapas' },
    { code: 'CH', name: 'Chihuahua' },
    { code: 'DF', name: 'Ciudad de México' },
    { code: 'CO', name: 'Coahuila' },
    { code: 'CL', name: 'Colima' },
    { code: 'DG', name: 'Durango' },
    { code: 'GT', name: 'Guanajuato' },
    { code: 'GR', name: 'Guerrero' },
    { code: 'HG', name: 'Hidalgo' },
    { code: 'JC', name: 'Jalisco' },
    { code: 'MI', name: 'Michoacán' },
    { code: 'MO', name: 'Morelos' },
    { code: 'MX', name: 'Estado de México' },
    { code: 'NA', name: 'Nayarit' },
    { code: 'NL', name: 'Nuevo León' },
    { code: 'OA', name: 'Oaxaca' },
    { code: 'PU', name: 'Puebla' },
    { code: 'QE', name: 'Querétaro' },
    { code: 'QR', name: 'Quintana Roo' },
    { code: 'SL', name: 'San Luis Potosí' },
    { code: 'SI', name: 'Sinaloa' },
    { code: 'SO', name: 'Sonora' },
    { code: 'TB', name: 'Tabasco' },
    { code: 'TM', name: 'Tamaulipas' },
    { code: 'TL', name: 'Tlaxcala' },
    { code: 'VE', name: 'Veracruz' },
    { code: 'YU', name: 'Yucatán' },
    { code: 'ZA', name: 'Zacatecas' },
  ];

  readonly numberOfCardsControl = new FormControl<number>(1, {
    nonNullable: true,
    validators: [Validators.required, Validators.min(1), Validators.max(10000)],
  });

  readonly shipToNameControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required, Validators.maxLength(100)],
  });

  readonly shipToAddressControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required, Validators.maxLength(200)],
  });

  readonly shipToCityControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required, Validators.maxLength(50)],
  });

  readonly shipToStateOrProvinceControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });

  readonly shipToPostalCodeControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [
      Validators.required,
      Validators.pattern(/^\d{5}(-\d{4})?$|^[A-Z]\d[A-Z][ ]?\d[A-Z]\d$/),
    ],
  });

  readonly locationIdControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });

  readonly form = new FormGroup({
    numberOfCards: this.numberOfCardsControl,
    shipToName: this.shipToNameControl,
    shipToAddress: this.shipToAddressControl,
    shipToCity: this.shipToCityControl,
    shipToStateOrProvince: this.shipToStateOrProvinceControl,
    shipToPostalCode: this.shipToPostalCodeControl,
    locationId: this.locationIdControl,
  });

  constructor() {
    this.loadLocations();
  }

  private loadLocations(): void {
    this.#cardsService
      .getLocations(0, 100) // Load more locations to ensure we get all of them
      .pipe(
        take(1),
        tap((response: CardLocationsResponse) => {
          this.locations.set(response.content);
        }),
        catchError(() => {
          this.#notifier.error({
            title: 'Error al cargar ubicaciones',
            message: 'No se pudieron cargar las ubicaciones disponibles',
          });
          this.locations.set([]);
          return of(void 0);
        })
      )
      .subscribe();
  }

  close(confirmation: boolean) {
    this.#dialogRef.close({
      hasConfirmation: confirmation,
    });
  }

  finish(): void {
    this.form.markAllAsTouched();

    if (this.form.invalid) {
      this.#notifier.warning({
        title:
          'Por favor, completa los campos requeridos o corrije los errores indicados.',
      });
      return;
    }

    const request: BulkOrderRequest = {
      numberOfCards: this.numberOfCardsControl.value,
      shipToName: this.shipToNameControl.value,
      shipToAddress: this.shipToAddressControl.value,
      shipToCity: this.shipToCityControl.value,
      shipToStateOrProvince: this.shipToStateOrProvinceControl.value,
      shipToPostalCode: this.shipToPostalCodeControl.value,
      locationId: this.locationIdControl.value,
    };

    this.#cardsService
      .createBulkOrder(request)
      .pipe(
        take(1),
        tap(() => {
          this.#notifier.success({
            title: 'Orden masiva creada exitosamente',
          });
          this.close(true);
        }),
        catchError(() => {
          this.#notifier.error({
            title: 'Error al crear la orden masiva',
            message: 'Por favor intenta nuevamente',
          });
          return of(void 0);
        })
      )
      .subscribe();
  }
}
