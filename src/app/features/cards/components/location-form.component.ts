import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NotifierService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import { catchError, of, take, tap } from 'rxjs';
import { CardsService, CreateLocationRequest } from '../services/cards.service';

interface MexicanState {
  code: string;
  name: string;
}

@Component({
  selector: 'app-location-form',
  template: `
    <aplz-ui-card>
      <h1 class="text-2xl font-medium mt-8 mx-6">Nueva ubicación</h1>
      <form [formGroup]="form" (ngSubmit)="finish()" class="mx-6 mt-12">
        <!-- Name field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Nombre de la ubicación</aplz-ui-form-label>
          <input type="text" aplzFormInput formControlName="name" />
          <ng-container aplzFormError>
            @if (nameControl.touched && nameControl.hasError('required')) {
              <p>El nombre es requerido</p>
            }
            @if (nameControl.touched && nameControl.hasError('maxlength')) {
              <p>El nombre no puede exceder 85 caracteres</p>
            }
            @if (nameControl.touched && nameControl.hasError('pattern')) {
              <p>
                El nombre solo puede contener letras, números, espacios y los
                caracteres: _ ' - .
              </p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <!-- Address field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Dirección</aplz-ui-form-label>
          <input type="text" aplzFormInput formControlName="address" />
          <ng-container aplzFormError>
            @if (
              addressControl.touched && addressControl.hasError('required')
            ) {
              <p>La dirección es requerida</p>
            }
            @if (
              addressControl.touched && addressControl.hasError('minlength')
            ) {
              <p>La dirección debe tener al menos 4 caracteres</p>
            }
            @if (
              addressControl.touched && addressControl.hasError('maxlength')
            ) {
              <p>La dirección no puede exceder 40 caracteres</p>
            }
            @if (addressControl.touched && addressControl.hasError('pattern')) {
              <p>
                La dirección solo puede contener letras, números y espacios
                (4-40 caracteres)
              </p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <!-- City field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Ciudad</aplz-ui-form-label>
          <input type="text" aplzFormInput formControlName="city" />
          <ng-container aplzFormError>
            @if (cityControl.touched && cityControl.hasError('required')) {
              <p>La ciudad es requerida</p>
            }
            @if (cityControl.touched && cityControl.hasError('maxlength')) {
              <p>La ciudad no puede exceder 30 caracteres</p>
            }
            @if (cityControl.touched && cityControl.hasError('pattern')) {
              <p>
                La ciudad solo puede contener letras, espacios, puntos y guiones
              </p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <!-- State field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Estado</aplz-ui-form-label>
          <select aplzFormSelect formControlName="state">
            <option value="">Selecciona un estado</option>
            @for (state of mexicanStates; track state.code) {
              <option [value]="state.code">{{ state.name }}</option>
            }
          </select>
          <ng-container aplzFormError>
            @if (stateControl.touched && stateControl.hasError('required')) {
              <p>El estado es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <!-- Postal Code field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Código Postal</aplz-ui-form-label>
          <input type="text" aplzFormInput formControlName="postalCode" />
          <ng-container aplzFormError>
            @if (
              postalCodeControl.touched &&
              postalCodeControl.hasError('required')
            ) {
              <p>El código postal es requerido</p>
            }
            @if (
              postalCodeControl.touched &&
              postalCodeControl.hasError('maxlength')
            ) {
              <p>El código postal no puede exceder 10 caracteres</p>
            }
            @if (
              postalCodeControl.touched && postalCodeControl.hasError('pattern')
            ) {
              <p>Formato inválido. Use: 12345, 12345-1234 o K1A 0B1</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <div class="flex justify-end mb-8 mt-10 gap-4">
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="close(false)">
            Cancelar
          </button>
          <button
            aplzButton
            type="submit"
            aplzAppearance="solid"
            aplzColor="dark"
            size="md"
            [disabled]="form.disabled">
            <span class="mr-2">Crear</span>
            ubicación
          </button>
        </div>
      </form>
    </aplz-ui-card>
  `,
  imports: [
    ReactiveFormsModule,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
    AplazoCardComponent,
  ],
})
export class LocationFormComponent {
  readonly #dialogRef: DialogRef<any, any> = inject(DialogRef);
  readonly #notifier = inject(NotifierService);
  readonly #cardsService = inject(CardsService);

  readonly mexicanStates: MexicanState[] = [
    { code: 'AG', name: 'Aguascalientes' },
    { code: 'BC', name: 'Baja California' },
    { code: 'BS', name: 'Baja California Sur' },
    { code: 'CM', name: 'Campeche' },
    { code: 'CS', name: 'Chiapas' },
    { code: 'CH', name: 'Chihuahua' },
    { code: 'DF', name: 'Ciudad de México' },
    { code: 'CO', name: 'Coahuila' },
    { code: 'CL', name: 'Colima' },
    { code: 'DG', name: 'Durango' },
    { code: 'GT', name: 'Guanajuato' },
    { code: 'GR', name: 'Guerrero' },
    { code: 'HG', name: 'Hidalgo' },
    { code: 'JC', name: 'Jalisco' },
    { code: 'MI', name: 'Michoacán' },
    { code: 'MO', name: 'Morelos' },
    { code: 'MX', name: 'Estado de México' },
    { code: 'NA', name: 'Nayarit' },
    { code: 'NL', name: 'Nuevo León' },
    { code: 'OA', name: 'Oaxaca' },
    { code: 'PU', name: 'Puebla' },
    { code: 'QE', name: 'Querétaro' },
    { code: 'QR', name: 'Quintana Roo' },
    { code: 'SL', name: 'San Luis Potosí' },
    { code: 'SI', name: 'Sinaloa' },
    { code: 'SO', name: 'Sonora' },
    { code: 'TB', name: 'Tabasco' },
    { code: 'TM', name: 'Tamaulipas' },
    { code: 'TL', name: 'Tlaxcala' },
    { code: 'VE', name: 'Veracruz' },
    { code: 'YU', name: 'Yucatán' },
    { code: 'ZA', name: 'Zacatecas' },
  ];

  readonly nameControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [
      Validators.required,
      Validators.maxLength(85),
      Validators.pattern(/^[A-Za-z0-9 _'\\.\\-]+$/),
    ],
  });

  readonly addressControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [
      Validators.required,
      Validators.minLength(4),
      Validators.maxLength(40),
      Validators.pattern(/^[A-Za-z0-9 ]{4,40}$/),
    ],
  });

  readonly cityControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [
      Validators.required,
      Validators.maxLength(30),
      Validators.pattern(/^[A-Za-z .-]{1,30}$/),
    ],
  });

  readonly stateControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });

  readonly postalCodeControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [
      Validators.required,
      Validators.maxLength(10),
      Validators.pattern(/^\d{5}(-\d{4})?$|^[A-Z]\d[A-Z][ ]?\d[A-Z]\d$/),
    ],
  });

  readonly form = new FormGroup({
    name: this.nameControl,
    address: this.addressControl,
    city: this.cityControl,
    state: this.stateControl,
    postalCode: this.postalCodeControl,
  });

  close(confirmation: boolean) {
    this.#dialogRef.close({
      hasConfirmation: confirmation,
    });
  }

  finish(): void {
    this.form.markAllAsTouched();

    if (this.form.invalid) {
      this.#notifier.warning({
        title:
          'Por favor, completa los campos requeridos o corrije los errores indicados.',
      });
      return;
    }

    const request: CreateLocationRequest = {
      name: this.nameControl.value,
      address: this.addressControl.value,
      city: this.cityControl.value,
      state: this.stateControl.value,
      postalCode: this.postalCodeControl.value,
      parentLocation: '0',
      parentLocationType: 0,
    };

    this.#cardsService
      .createLocation(request)
      .pipe(
        take(1),
        tap(() => {
          this.#notifier.success({
            title: 'Ubicación creada exitosamente',
          });
          this.close(true);
        }),
        catchError(error => {
          this.#notifier.error({
            title: 'Error al crear la ubicación',
            message: 'Por favor intenta nuevamente',
          });
          return of(void 0);
        })
      )
      .subscribe();
  }
}
