import { inject, Injectable } from '@angular/core';
import { NotifierService } from '@aplazo/merchant/shared';
import { DialogConfig, DialogRef, DialogService } from '@ngneat/dialog';
import { lastValueFrom, take } from 'rxjs';
import { UpdateOperatorUseCase } from '../../application/usecases/edit-user.usecase';
import {
  OperatorUIDto,
  OperatorWithPasswordDto,
  OperatorWithPasswordUIDto,
  serializeUserResponse,
  toOperatorsList,
} from '../../domain/dtos/user.dto';
import { OperatorFormComponent } from '../components/user-form/user-form.component';
import { MerchantStoreService } from './merchant-store.service';

@Injectable()
export class UpdateUserWithDialogFormService {
  readonly #dialog = inject(DialogService);
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #notifier = inject(NotifierService);
  readonly #editUseCase = inject(UpdateOperatorUseCase);

  async execute(user: OperatorUIDto): Promise<void> {
    const currMId = await lastValueFrom(
      this.#merchantStore.selectedMerchantId$.pipe(take(1))
    );

    const dialogResult = await this.#getDialogResult(user);

    const cancelTitle = 'Edición cancelada';

    if (!dialogResult?.hasConfirmation) {
      this.#notifier.info({ title: cancelTitle });
      return;
    }

    this.#simpleFieldsValidation(dialogResult);

    const hasChanges = this.#hasChanges({
      operator: dialogResult,
      storedOperator: user,
    });

    if (!hasChanges) {
      this.#notifier.warning({
        title: 'No hay cambios para guardar',
      });
      return;
    }

    const request = this.#getRequest({
      operator: dialogResult,
      merchantId: currMId,
      storedOperator: user,
    });

    const response = await lastValueFrom(
      this.#editUseCase
        .execute(request as OperatorWithPasswordDto)
        .pipe(take(1))
    );

    const serializedResponse = serializeUserResponse(currMId, response);

    const storedUsers = await lastValueFrom(
      this.#merchantStore.merchantUsers$.pipe(take(1))
    );

    if (!storedUsers || !storedUsers.length) {
      const newList = toOperatorsList([serializedResponse]);

      this.#merchantStore.setUsers(newList);
      return;
    }

    const newList = JSON.parse(JSON.stringify(storedUsers));

    newList.data[serializedResponse.idAccount] = serializedResponse;
    newList.length = Object.keys(newList.data).length;
    newList.merchantId = currMId;

    this.#merchantStore.setUsers(newList);
  }

  async #getDialogResult(
    operator: OperatorUIDto
  ): Promise<OperatorWithPasswordUIDto | undefined> {
    const branches = await lastValueFrom(
      this.#merchantStore.merchantBranches$.pipe(take(1))
    );

    const data = {
      id: operator.idAccount,
      storefronts: operator.branches?.map(i => i.id) ?? undefined,
      username: operator.username,
      email: operator.userEmail,
      active: operator.userStatus === 'Active',
      role: operator.role,
      platform: operator.platform,
      merchantId: operator.merchantId,
      branches,
    };

    const config: Partial<DialogConfig> = {
      enableClose: false,
      width: '320px',
      data,
    };

    const dialogRef = this.#dialog.open(
      OperatorFormComponent,
      config
    ) as DialogRef<any, OperatorWithPasswordUIDto>;

    const dialogResult = await lastValueFrom(
      dialogRef.afterClosed$.pipe(take(1))
    );

    return dialogResult;
  }

  #simpleFieldsValidation(
    operator: OperatorWithPasswordUIDto | undefined
  ): void | never {
    const username = operator?.username?.trim() ?? null;

    if (!username) {
      this.#notifier.warning({
        title: 'El nombre de usuario no puede estar vacío',
      });
      return;
    }

    const password = operator?.password?.trim() ?? null;

    if (!password && !operator?.idAccount) {
      this.#notifier.warning({
        title: 'La contraseña no puede estar vacía',
      });
      return;
    }

    const role = operator?.role ?? null;

    if (!role) {
      this.#notifier.warning({
        title: 'El rol no puede estar vacío',
      });
      return;
    }

    const platform = operator?.platform ?? null;

    const branchesId = operator?.branchesId ?? null;

    if (platform && platform === 'POSUI' && !branchesId) {
      this.#notifier.warning({
        title: 'El id de la sucursal es requerido para un rol de POSUI',
      });
      return;
    }
  }

  #hasChanges(args: {
    operator: OperatorWithPasswordUIDto;
    storedOperator: OperatorUIDto | undefined;
  }): boolean {
    const isStoredActive =
      (args.storedOperator && args.storedOperator.userStatus === 'Active') ??
      false;
    const isActive = args.operator.active ?? false;

    return !(
      args.operator.username === args.storedOperator?.username &&
      args.operator.userEmail === args.storedOperator?.userEmail &&
      args.operator.role === args.storedOperator?.role &&
      !!args.storedOperator?.branches &&
      Array.isArray(args.storedOperator?.branches) &&
      args.storedOperator.branches.every(
        i => !!i && args.operator.branchesId?.includes(i.id)
      ) &&
      args.operator.branchesId?.every(
        i =>
          !!i &&
          (args?.storedOperator?.branches?.findIndex(s => s.id === i) ?? -1) >
            -1
      ) &&
      args.storedOperator?.idAccount === args.operator.idAccount &&
      !args.operator.password &&
      isStoredActive === isActive
    );
  }

  #getRequest(args: {
    operator: OperatorWithPasswordUIDto;
    merchantId: number;
    storedOperator: OperatorUIDto | undefined;
  }): Partial<OperatorWithPasswordUIDto> {
    const request: Partial<OperatorWithPasswordDto> = {
      merchantId: args.merchantId,
      idAccount: args.operator.idAccount,
    };

    if (
      args.operator.platform &&
      args.operator.platform === 'POSUI' &&
      ((args.storedOperator?.branches &&
        Array.isArray(args.storedOperator.branches) &&
        args.storedOperator.branches.every(i =>
          args.operator.branchesId?.includes(i.id)
        ) &&
        args.operator.branchesId?.every(
          i =>
            (args?.storedOperator?.branches?.findIndex(s => s.id === i) ?? -1) >
            -1
        )) ||
        !args.storedOperator?.branches)
    ) {
      request.branchesId = args.operator.branchesId;
    } else if (args.operator.platform && args.operator.platform === 'Panel') {
      request.branchesId = undefined;
    }

    if (args.operator.password) {
      request.password = args.operator.password;
    }

    if (
      args.operator.username &&
      args.operator.username !== args.storedOperator?.username
    ) {
      request.username = args.operator.username;
    }

    if (
      args.operator.userEmail &&
      args.operator.userEmail !== args.storedOperator?.userEmail
    ) {
      request.userEmail = args.operator.userEmail;
    }

    if (
      args.operator.role &&
      args.operator.role !== args.storedOperator?.role
    ) {
      request.role = args.operator.role;
    }

    if (Object.prototype.hasOwnProperty.call(args.operator, 'active')) {
      request.active = args.operator.active;
    }
    return request;
  }
}
