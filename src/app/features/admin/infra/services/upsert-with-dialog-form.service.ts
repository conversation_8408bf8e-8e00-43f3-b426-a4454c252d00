import { inject, Injectable } from '@angular/core';
import { NotifierService, TemporalService } from '@aplazo/merchant/shared';
import { DialogConfig, DialogService } from '@ngneat/dialog';
import { lastValueFrom, map, of, switchMap, take } from 'rxjs';
import { UpsertBranchUseCase } from '../../application/usecases/upsert-branch.usecase';
import { BranchUI } from '../../domain/dtos/branch.dto';
import { VALID_INTEGRATION_TYPES_TO_CREATE_STORE } from '../../domain/entities/integration-types';
import { BranchFormComponent } from '../components/branch-form.component';
import { MerchantStoreService } from './merchant-store.service';

@Injectable()
export class UpsertStorefrontWithDialogFormService {
  readonly #dialog = inject(DialogService);
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #notifier = inject(NotifierService);
  readonly #usecase = inject(UpsertBranchUseCase);
  readonly #temporal = inject(TemporalService);

  async execute(branch?: BranchUI): Promise<void> {
    const intType = this.#merchantStore.getIntegrationType();

    if (VALID_INTEGRATION_TYPES_TO_CREATE_STORE.indexOf(intType as any) < 0) {
      this.#notifier.error({
        title: 'No se puede crear/editar un storefront',
        message:
          'El tipo de integración no permite las operaciones con storefronts',
      });

      return;
    }

    const merchantId = this.#merchantStore.selectedMerchantId;

    const injectedData: Partial<DialogConfig> = {
      enableClose: false,
      width: '320px',
    };

    if (branch) {
      injectedData.data = {
        name: branch.branchName,
        id: branch.branchId,
        active: branch.deleted == null,
      };
    }

    const cancelTitle = branch
      ? 'Actualización cancelada'
      : 'Creación cancelada';
    const dialogRef = this.#dialog.open(BranchFormComponent, injectedData);

    const result = await lastValueFrom(
      dialogRef.afterClosed$.pipe(
        switchMap(result => {
          if (!result?.hasConfirmation) {
            this.#notifier.info({ title: cancelTitle });
            return of(null);
          }

          return this.#usecase.execute({
            merchantId,
            name: result.name,
            active: result.active,
            id: result.id,
          });
        }),
        take(1)
      )
    );

    if (!result) {
      return;
    }

    let created: Date | null = null;
    let updated: Date | null = null;
    let deleted: Date | null = null;

    try {
      created = this.#temporal.fromStringToDate(result.created);
      updated = this.#temporal.fromStringToDate(result.updated);
      deleted = result.deleted
        ? this.#temporal.fromStringToDate(result.deleted)
        : null;
    } catch (error) {
      console.warn(error);
    }

    const resultToUI: BranchUI = {
      branchId: result.branchId,
      branchName: result.branchName,
      banned: result.banned,
      created: created,
      updated: updated,
      deleted: deleted,
      merchantConfigId: result.merchantConfigId,
    };

    const clonedStores = await lastValueFrom(
      this.#merchantStore.merchantBranches$.pipe(
        take(1),
        map(b => b.data)
      )
    );

    const selectedStoreIndex = clonedStores.findIndex(
      i => i.branchId === resultToUI.branchId
    );

    if (selectedStoreIndex >= 0) {
      clonedStores.splice(selectedStoreIndex, 1, resultToUI);
    } else {
      clonedStores.unshift(resultToUI);
    }

    this.#merchantStore.setBranches({
      merchantId,
      data: clonedStores,
    });
  }
}
