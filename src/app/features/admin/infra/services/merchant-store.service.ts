import { Injectable } from '@angular/core';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { BehaviorSubject } from 'rxjs';
import { defaultAccountInfo } from '../../application/usecases/get-account-info.usecase';
import { defaultBasicsInfo } from '../../application/usecases/get-basics-info.usecase';
import { defaultGeneralsInfo } from '../../application/usecases/get-general-info.usecase';
import { defaultInvoiceInfo } from '../../application/usecases/get-invoice-info.usecase';
import { defaultMdrInfo } from '../../application/usecases/get-mdr-info.usecase';
import { AccountInfoUIResponse } from '../../domain/dtos/account.dto';
import { MerchantBasicsUI } from '../../domain/dtos/basics.dto';
import { BranchUI } from '../../domain/dtos/branch.dto';
import { ContactUI } from '../../domain/dtos/contact.dto';
import { MerchantGeneralInfoUI } from '../../domain/dtos/general-info.dto';
import { InvoiceUI } from '../../domain/dtos/invoice.dto';
import { InstallmentFrequency, MdrList } from '../../domain/dtos/mdr.dto';
import { OperatorsUIList } from '../../domain/dtos/user.dto';
import { IntegrationType } from '../../domain/entities/integration-types';

@Injectable({ providedIn: 'root' })
export class MerchantStoreService {
  readonly #selectedMerchantId = new BehaviorSubject<number>(0);
  readonly #merchantBasics$ = new BehaviorSubject<MerchantBasicsUI>(
    defaultBasicsInfo
  );
  readonly #merchantGenerals$ = new BehaviorSubject<MerchantGeneralInfoUI>(
    defaultGeneralsInfo
  );
  readonly #merchantAccount$ = new BehaviorSubject<AccountInfoUIResponse>(
    defaultAccountInfo
  );
  readonly #merchantMdr$ = new BehaviorSubject<MdrList | null>(null);
  readonly #merchantContacts$ = new BehaviorSubject<{
    merchantId: number;
    data: ContactUI[];
  }>({
    merchantId: 0,
    data: [],
  });
  readonly #merchantInvoice$ = new BehaviorSubject<InvoiceUI>(
    defaultInvoiceInfo
  );
  readonly #merchantBranches$ = new BehaviorSubject<{
    merchantId: number;
    data: BranchUI[];
  }>({
    merchantId: 0,
    data: [],
  });
  readonly #merchantUsers$ = new BehaviorSubject<OperatorsUIList | null>(null);
  readonly #searchBy$ = new BehaviorSubject<{ id: number; name: string }>({
    id: 0,
    name: '',
  });

  merchantBasics$ = this.#merchantBasics$.asObservable();
  merchantGenerals$ = this.#merchantGenerals$.asObservable();
  merchantAccount$ = this.#merchantAccount$.asObservable();
  merchantMdr$ = this.#merchantMdr$.asObservable();
  merchantContacts$ = this.#merchantContacts$.asObservable();
  merchantInvoice$ = this.#merchantInvoice$.asObservable();
  merchantBranches$ = this.#merchantBranches$.asObservable();
  merchantUsers$ = this.#merchantUsers$.asObservable();
  searchBy$ = this.#searchBy$.asObservable();
  selectedMerchantId$ = this.#selectedMerchantId.asObservable();

  get selectedMerchantId() {
    return this.#selectedMerchantId.getValue();
  }

  getBranchesWithMerchantId(): { merchantId: number; data: BranchUI[] } {
    return this.#merchantBranches$.getValue();
  }

  getIntegrationType(): IntegrationType {
    return this.#merchantBasics$.getValue().intType as IntegrationType;
  }

  setSelectedMerchantId(merchantId: number) {
    if (this.#selectedMerchantId.value === merchantId) {
      return;
    }

    this.#selectedMerchantId.next(merchantId);
  }

  setMerchantBasics(merchant: MerchantBasicsUI | null) {
    const isEmptyMerchant = !merchant || !merchant?.merchantId;

    if (isEmptyMerchant) {
      this.#merchantBasics$.next(defaultBasicsInfo);
      return;
    }

    const isSameMerchant =
      this.#merchantBasics$.value?.merchantId === merchant?.merchantId &&
      this.#merchantBasics$.value?.merchantName === merchant?.merchantName &&
      this.#merchantBasics$.value?.status === merchant?.status &&
      this.#merchantBasics$.value?.intType === merchant?.intType;

    if (isSameMerchant) {
      return;
    }

    this.#merchantBasics$.next(merchant);
  }

  setMerchantGenerals(generals: MerchantGeneralInfoUI | null) {
    const isEmptyGenerals = !generals || !generals?.merchantId;

    if (isEmptyGenerals) {
      this.#merchantGenerals$.next(defaultGeneralsInfo);
      return;
    }

    const isSameGenerals =
      this.#merchantGenerals$.value?.merchantId === generals?.merchantId &&
      this.#merchantGenerals$.value?.mComInfoId === generals?.mComInfoId &&
      this.#merchantGenerals$.value?.mQuestionnaireId ===
        generals?.mQuestionnaireId &&
      this.#merchantGenerals$.value?.merchantEmail ===
        generals?.merchantEmail &&
      this.#merchantGenerals$.value?.merchantAddress ===
        generals?.merchantAddress &&
      this.#merchantGenerals$.value?.merchantWebsite ===
        generals?.merchantWebsite &&
      this.#merchantGenerals$.value?.merchantIndustry ===
        generals?.merchantIndustry &&
      this.#merchantGenerals$.value?.merchantAov === generals?.merchantAov &&
      this.#merchantGenerals$.value?.merchantRevenue ===
        generals?.merchantRevenue &&
      this.#merchantGenerals$.value?.merchantCategory ===
        generals?.merchantCategory;

    if (isSameGenerals) {
      return;
    }

    this.#merchantGenerals$.next(generals);
  }

  setMerchantAccount(account: AccountInfoUIResponse | null) {
    const isEmptyAccount = !account || !account?.merchantId;

    if (isEmptyAccount) {
      this.#merchantAccount$.next(defaultAccountInfo);
      return;
    }

    const isSameAccount =
      this.#merchantAccount$.value?.merchantId === account?.merchantId &&
      this.#merchantAccount$.value?.token === account?.token &&
      this.#merchantAccount$.value?.email === account?.email &&
      this.#merchantAccount$.value?.bank === account?.bank &&
      this.#merchantAccount$.value?.bankAccount === account?.bankAccount;

    if (isSameAccount) {
      return;
    }

    this.#merchantAccount$.next(account);
  }

  setMdrInfo(mdr: MdrList | null) {
    const isEmptyMdr = !mdr || Object.keys(mdr).length === 0;

    if (isEmptyMdr) {
      this.#merchantMdr$.next(defaultMdrInfo);
      return;
    }

    const stored = this.#merchantMdr$.getValue();

    const isSameMdr =
      !!stored &&
      Object.keys(mdr).length === Object.keys(stored).length &&
      Object.keys(mdr).every(key => {
        const parsedKey = Number(key) as InstallmentFrequency;

        if (isNaN(parsedKey)) {
          throw new RuntimeMerchantError(
            'El id de la frecuencia de pagos debe ser un número entero válido.',
            'MerchantStoreService::setMdrInfo'
          );
        }

        const proposed = mdr[parsedKey];

        if (
          stored[parsedKey]?.feeOps === proposed?.feeOps &&
          stored[parsedKey]?.feePct === proposed?.feePct &&
          stored[parsedKey]?.promoFee === proposed?.promoFee &&
          stored[parsedKey]?.promoFeeEndDate === proposed?.promoFeeEndDate &&
          stored[parsedKey]?.promoFeeEndIsoDate ===
            proposed?.promoFeeEndIsoDate &&
          stored[parsedKey]?.installmentFrequency ===
            proposed?.installmentFrequency &&
          stored[parsedKey]?.installmentFrequencyId ===
            proposed?.installmentFrequencyId
        ) {
          return true;
        }

        return false;
      });

    if (isSameMdr) {
      return;
    }

    this.#merchantMdr$.next(mdr);
  }

  setMerchantContacts(contacts: ContactUI[] | null) {
    if (!contacts || contacts.length === 0) {
      this.#merchantContacts$.next({
        merchantId: 0,
        data: [],
      });
      return;
    }

    const isSameContacts =
      this.#merchantContacts$.value.data.length === contacts.length &&
      this.#merchantContacts$.value.data.every((contact, idx) => {
        return (
          contact.email === contacts[idx].email &&
          contact.id === contacts[idx].id &&
          contact.merchantId === contacts[idx].merchantId &&
          contact.role === contacts[idx].role &&
          contact.businessArea === contacts[idx].businessArea &&
          contact.phone === contacts[idx].phone
        );
      });

    if (isSameContacts) {
      return;
    }

    this.#merchantContacts$.next({
      merchantId: contacts[0].merchantId,
      data: [...contacts],
    });
  }

  setMerchantInvoice(invoice: InvoiceUI | null) {
    if (!invoice) {
      this.#merchantInvoice$.next(defaultInvoiceInfo);
      return;
    }

    const stored = this.#merchantInvoice$.getValue();

    const isSameInvoice =
      stored?.merchantId === invoice?.merchantId &&
      stored?.id === invoice?.id &&
      stored.name === invoice?.name &&
      stored?.rfc === invoice?.rfc &&
      stored?.currency === invoice?.currency &&
      stored?.email === invoice?.email &&
      stored?.zipCode === invoice?.zipCode &&
      stored?.invoiceRegime === invoice?.invoiceRegime &&
      stored?.paymentMethod === invoice?.paymentMethod &&
      stored?.paymentForm === invoice?.paymentForm &&
      stored?.cfdi === invoice?.cfdi;

    if (isSameInvoice) {
      return;
    }

    this.#merchantInvoice$.next(invoice);
  }

  setBranches(args: { merchantId: number; data: BranchUI[] } | null) {
    if (!args) {
      this.#merchantBranches$.next({ merchantId: 0, data: [] });
      return;
    }

    if (
      args.merchantId === this.#merchantBranches$.value.merchantId &&
      args.data.length === this.#merchantBranches$.value.data.length &&
      args.data.every((branch, idx) => {
        return (
          branch.branchId ===
            this.#merchantBranches$.value.data[idx].branchId &&
          branch.branchName ===
            this.#merchantBranches$.value.data[idx].branchName &&
          branch.banned === this.#merchantBranches$.value.data[idx].banned
        );
      })
    ) {
      return;
    }

    this.#merchantBranches$.next(args);
  }

  setUsers(users: OperatorsUIList | null) {
    if (!users || !users.data || users.length === 0) {
      this.#merchantUsers$.next(null);
    }

    const storedList = this.#merchantUsers$.getValue();

    const isSameMerchantId =
      users && storedList && users.merchantId === storedList.merchantId;

    const isSameLength =
      users && storedList && users.length === storedList.length;

    const hasDataSameKeys =
      users &&
      storedList &&
      Object.keys(users.data).every(key => !!storedList.data[Number(key)]);

    const isSameData =
      users &&
      storedList &&
      Object.keys(users.data).every(key => {
        const parsedKey = Number(key);
        const proposed = users.data[parsedKey];

        return (
          storedList.data[parsedKey]?.idAccount === proposed.idAccount &&
          storedList.data[parsedKey]?.username === proposed.username &&
          storedList.data[parsedKey]?.role === proposed.role &&
          storedList.data[parsedKey]?.userStatus === proposed.userStatus &&
          storedList.data[parsedKey]?.userEmail === proposed.userEmail &&
          storedList.data[parsedKey]?.platform === proposed.platform &&
          storedList.data[parsedKey]?.branches === proposed.branches &&
          storedList.data[parsedKey]?.createdAt === proposed.createdAt &&
          storedList.data[parsedKey]?.updatedAt === proposed.updatedAt &&
          storedList.data[parsedKey]?.deletedAt === proposed.deletedAt
        );
      });

    if (isSameMerchantId && isSameLength && hasDataSameKeys && isSameData) {
      return;
    }

    this.#merchantUsers$.next(users);
  }

  setSearchBy(searchBy: { id?: number; name?: string } | null) {
    if (!searchBy) {
      this.#searchBy$.next({ id: 0, name: '' });
      return;
    }

    if (
      searchBy.id === this.#searchBy$.value.id &&
      searchBy.name === this.#searchBy$.value.name
    ) {
      return;
    }

    if (!searchBy.name && searchBy.id) {
      this.#searchBy$.next({ id: searchBy.id, name: '' });
    }

    if (searchBy.name && !searchBy.id) {
      this.#searchBy$.next({ id: 0, name: searchBy.name ?? '' });
    }
  }

  clearSearchBy() {
    this.#searchBy$.next({ id: 0, name: '' });
  }

  clearAll() {
    this.#selectedMerchantId.next(0);
    this.#merchantBasics$.next(defaultBasicsInfo);
    this.#merchantGenerals$.next(defaultGeneralsInfo);
    this.#merchantAccount$.next(defaultAccountInfo);
    this.#merchantMdr$.next(defaultMdrInfo);
    this.#merchantContacts$.next({ merchantId: 0, data: [] });
    this.#merchantInvoice$.next(defaultInvoiceInfo);
    this.#merchantBranches$.next({ merchantId: 0, data: [] });
    this.#merchantUsers$.next(null);
    this.#searchBy$.next({ id: 0, name: '' });
  }
}
