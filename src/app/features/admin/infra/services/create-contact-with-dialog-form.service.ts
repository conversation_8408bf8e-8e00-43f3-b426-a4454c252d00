import { inject, Injectable } from '@angular/core';
import {
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { DialogConfig, DialogRef, DialogService } from '@ngneat/dialog';
import { lastValueFrom, of, take } from 'rxjs';
import { CreateOneContactUseCase } from '../../application/usecases/new-contact.usecase';
import {
  ContactUI,
  serializeRetrievedContactResponse,
} from '../../domain/dtos/contact.dto';
import { ContactFormComponent } from '../components/contact-form/contact-form.component';
import { MerchantStoreService } from './merchant-store.service';

@Injectable()
export class CreateContactWithDialogFormService {
  readonly #dialog = inject(DialogService);
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #notifier = inject(NotifierService);
  readonly #createUseCase = inject(CreateOneContactUseCase);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  async execute(): Promise<void> {
    try {
      const currMId = await lastValueFrom(
        this.#merchantStore.selectedMerchantId$.pipe(take(1))
      );

      const config: Partial<DialogConfig> = {
        enableClose: false,
        width: '320px',
      };

      const dialogRef = this.#dialog.open(
        ContactFormComponent,
        config
      ) as DialogRef<any, Partial<ContactUI & { hasConfirmation: boolean }>>;

      const dialogResult = await lastValueFrom(
        dialogRef.afterClosed$.pipe(take(1))
      );

      const cancelTitle = 'Creación cancelada';

      if (!dialogResult?.hasConfirmation) {
        this.#notifier.info({ title: cancelTitle });
        return;
      }

      const email = dialogResult.email?.trim() ?? null;

      if (!email) {
        throw new RuntimeMerchantError(
          'El correo no puede estar vacío',
          'CreateContactWithDialogFormService::validation::emptyEmail'
        );
      }

      const role = dialogResult?.role ?? null;

      if (!role) {
        throw new RuntimeMerchantError(
          'El rol no puede estar vacío',
          'CreateContactWithDialogFormService::validation::emptyRole'
        );
      }

      const businessArea = dialogResult?.businessArea ?? null;

      if (!businessArea) {
        throw new RuntimeMerchantError(
          'El área de negocio no puede estar vacía',
          'CreateContactWithDialogFormService::validation::emptyBusinessArea'
        );
      }

      if (!currMId) {
        throw new RuntimeMerchantError(
          'No se ha seleccionado un comercio',
          'CreateContactWithDialogFormService::validation::emptyMerchantId'
        );
      }

      const phone = dialogResult?.phone ?? '';

      const result = await lastValueFrom(
        this.#createUseCase
          .execute({
            email,
            phone,
            role,
            businessArea,
            merchantId: currMId,
          })
          .pipe(take(1))
      );

      const serialized = serializeRetrievedContactResponse(result);

      const storedUsers = await lastValueFrom(
        this.#merchantStore.merchantContacts$.pipe(take(1))
      );

      if (!storedUsers || !storedUsers.data || storedUsers.data.length === 0) {
        this.#merchantStore.setMerchantContacts([serialized]);
        return;
      }

      const newList = JSON.parse(
        JSON.stringify(storedUsers.data)
      ) as ContactUI[];
      newList.unshift(serialized);
      this.#merchantStore.setMerchantContacts(newList);
    } catch (error) {
      console.warn((error as any)?.message ?? error);
      await lastValueFrom(
        this.#errorHandler.handle(error, of(null)).pipe(take(1))
      );
    }
  }
}
