import { Route } from '@angular/router';
import { ROUTES_CONFIG } from '../../../core/domain/route-names';
import { NewMerchantUseCase } from '../../application/usecases/new-merchant.usecase';
import {
  provideCreationRepositories,
  provideMerchantAdminRepositories,
  provideMerchantAdminUseCases,
} from '../config/providers';
import { exitCreationGuard } from '../guards/exit-creation.guard';
import { MerchantStoreService } from '../services/merchant-store.service';

export default [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: ROUTES_CONFIG.onboarding,
  },
  {
    path: ROUTES_CONFIG.onboarding,
    providers: [MerchantStoreService],

    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: ROUTES_CONFIG.merchantCreation,
      },
      {
        path: ROUTES_CONFIG.merchantCreation,
        loadComponent: () =>
          import('../pages/creation.component').then(
            m => m.MerchantCreationComponent
          ),
        providers: [provideCreationRepositories(), NewMerchantUseCase],
        canDeactivate: [exitCreationGuard],
        data: {
          title: 'Creación de comercio',
        },
      },
      {
        path: ROUTES_CONFIG.merchantAdmin,
        loadComponent: () =>
          import('../layout/admin-layout.component').then(
            stl => stl.AdminLayoutComponent
          ),
        providers: [
          provideMerchantAdminRepositories(),
          provideMerchantAdminUseCases(),
        ],
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: ROUTES_CONFIG.merchantSettingsSearch,
          },
          {
            path: ROUTES_CONFIG.merchantSettingsSearch,
            children: [],
            data: {
              title: 'Búsqueda de comercio',
            },
          },
          {
            path: ROUTES_CONFIG.merchantSettingsBasics,
            loadComponent: () =>
              import('../pages/basics/basics-settings.component').then(
                m => m.MerchantBasicsSettingsComponent
              ),
            data: {
              title: 'Información básica',
            },
          },
          {
            path: ROUTES_CONFIG.merchantSettingsInfo,
            loadComponent: () =>
              import('../pages/generals/info-settings.component').then(
                m => m.MerchantInfoSettingsComponent
              ),
            data: {
              title: 'Información del comercio',
            },
          },
          {
            path: ROUTES_CONFIG.merchantSettingsAccount,
            loadComponent: () =>
              import('../pages/account/account-settings.component').then(
                m => m.MerchantAccountSettingsComponent
              ),
            data: {
              title: 'Cuenta',
            },
          },
          {
            path: ROUTES_CONFIG.merchantSettingsMdr,
            loadComponent: () =>
              import('../pages/mdr/mdr-settings.component').then(
                m => m.MerchantMDRSettingsComponent
              ),
            data: {
              title: 'MDR',
            },
          },
          {
            path: ROUTES_CONFIG.merchantSettingsContact,
            loadComponent: () =>
              import('../pages/contact/contact-settings.component').then(
                m => m.MerchantContactSettingsComponent
              ),
            data: {
              title: 'Contacto',
            },
          },
          {
            path: ROUTES_CONFIG.merchantSettingsBilling,
            loadComponent: () =>
              import('../pages/billing/billing-settings.component').then(
                m => m.MerchantBillingSettingsComponent
              ),
            data: {
              title: 'Facturación',
            },
          },
          {
            path: ROUTES_CONFIG.merchantSettingsBranches,
            loadComponent: () =>
              import('../pages/branches/branch-settings.component').then(
                m => m.MerchantBranchesSettingsComponent
              ),
            data: {
              title: 'Storefronts',
            },
          },
          {
            path: ROUTES_CONFIG.merchantSettingsUsers,
            loadComponent: () =>
              import('../pages/users/users-settings.component').then(
                m => m.MerchantUsersSettingsComponent
              ),
            data: {
              title: 'Usuarios',
            },
          },
        ],
      },
    ],
  },
] satisfies Route[];
