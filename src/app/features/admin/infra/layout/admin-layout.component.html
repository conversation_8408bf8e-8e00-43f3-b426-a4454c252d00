<section class="flex flex-col gap-4 md:gap-8 px-4 md:px-8 py-4 md:py-8 w-full">
  @if (currentMerchantContent$ | async; as currentMerchant) {
    @if (!currentMerchant.hasContent) {
      <aplz-ui-card>
        <app-search-by-id-name [formControl]="searchBy"></app-search-by-id-name>
      </aplz-ui-card>
    } @else {
      <aplz-ui-card>
        <h2 class="text-lg">
          Seleccionaste el merchant
          <strong>
            {{ currentMerchant.data.merchantName }}
          </strong>
          , con el ID
          <strong>
            {{ currentMerchant.data.merchantId }}
          </strong>
        </h2>

        <div class="my-8">
          <button
            aplzButton
            aplzAppearance="stroked"
            size="md"
            aplzColor="info"
            (click)="clearMerchantSelection()">
            Buscar nuevamente
          </button>
        </div>
      </aplz-ui-card>
    }

    <aplz-ui-card>
      @if (merchants$ | async; as merchants) {
        @if (!merchants.hasContent && !merchants.isActiveSearch) {
          <aplz-ui-common-message
            [i18Text]="{
              title: 'Ingrese un ID o nombre de comercio',
              description: '',
            }"
            imgName="emptyLoans">
          </aplz-ui-common-message>
        } @else if (!merchants.hasContent && merchants.isActiveSearch) {
          <aplz-ui-common-message
            [i18Text]="{
              title: 'Intente con una búsqueda diferente',
              description:
                'No encontramos merchants que cumplan con los criterios de búsqueda que seleccionaste',
            }"
            imgName="emptySearch">
          </aplz-ui-common-message>
        } @else {
          <div class="max-h-96 overflow-y-auto">
            <table aplzSimpleTable aria-label="Merchant Result List">
              <tr aplzSimpleTableHeaderRow>
                <th
                  aplzSimpleTableHeaderCell
                  scope="col"
                  class="text-center px-2">
                  ID
                </th>
                <th
                  aplzSimpleTableHeaderCell
                  scope="col"
                  class="text-center px-2">
                  Nombre
                </th>
                <th
                  aplzSimpleTableHeaderCell
                  scope="col"
                  class="text-center px-2">
                  Estatus
                </th>
                <th
                  aplzSimpleTableHeaderCell
                  scope="col"
                  class="text-center px-2">
                  Tipo Integración
                </th>
                <th scope="col"></th>
              </tr>

              @for (item of merchants.data; track item) {
                <tr
                  aplzSimpleTableBodyRow
                  [selected]="item.merchantId === currentMerchant.data.merchantId"
                  [striped]="true"
                [class.bg-dark-background]="
                  item.merchantId === currentMerchant.data.merchantId
                ">
                  <td aplzSimpleTableBodyCell class="font-semibold text-center">
                    {{ item.merchantId }}
                  </td>
                  <td aplzSimpleTableBodyCell class="text-center">
                    {{ item.merchantName }}
                  </td>
                  <td aplzSimpleTableBodyCell class="text-center">
                    {{ item.status }}
                  </td>
                  <td aplzSimpleTableBodyCell class="text-center">
                    {{ item.intType }}
                  </td>
                  <td aplzSimpleTableBodyCell class="text-center">
                    <button
                      aplzButton
                      aplzAppearance="stroked"
                      size="md"
                      (click)="setMerchant(item.merchantId)">
                      Ver Detalle
                    </button>
                  </td>
                </tr>
              }
            </table>
          </div>
        }
      }
    </aplz-ui-card>

    @if (currentMerchant.hasContent) {
      <aplz-ui-card>
        <aplz-ui-tab-group
          (tabSelectionChange)="changeTab($event)"
          [selectedIndex]="currentTabIdx()">
          @for (tab of tabs; track tab.label) {
            <aplz-ui-tab [label]="tab.label">
              @if (
                tab.contentActive &&
                tab.label === 'Storefronts' &&
                (isValidIntTypeToCreateStore$ | async) === true &&
                isRoleEnableToEdit()
                ) {
                <aplz-ui-tab-body>
                  <app-create-one-button
                    title="Agregar Nuevo Storefront para el merchant: "
                    [merchantId]="currentMerchant.data.merchantId"
                  (createOne)="createOneStoreFront()"></app-create-one-button>
                </aplz-ui-tab-body>
              }

              @if (
                tab.contentActive &&
                tab.label === 'Usuarios' &&
                isRoleEnableToEdit()
                ) {
                <aplz-ui-tab-body>
                  <app-create-one-button
                    title="Agregar Nuevo Usuario para el merchant: "
                    [merchantId]="currentMerchant.data.merchantId"
                  (createOne)="createOneOperator()"></app-create-one-button>
                </aplz-ui-tab-body>
              }

              @if (
                tab.contentActive &&
                tab.label === 'Contacto' &&
                isRoleEnableToEdit()
                ) {
                <aplz-ui-tab-body>
                  <app-create-one-button
                    title="Agregar Nuevo Contacto para el merchant"
                    [merchantId]="currentMerchant.data.merchantId"
                    (createOne)="createOneContact()">
                  </app-create-one-button>
                </aplz-ui-tab-body>
              }
            </aplz-ui-tab>
          }
        </aplz-ui-tab-group>
      </aplz-ui-card>

      <div class="max-w-full">
        <router-outlet></router-outlet>
      </div>
    }
  }
</section>
