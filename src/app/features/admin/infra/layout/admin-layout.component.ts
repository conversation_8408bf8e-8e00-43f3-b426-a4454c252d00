import { AsyncPipe } from '@angular/common';
import {
  Component,
  computed,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoCommonMessageComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoTabsComponents } from '@aplazo/shared-ui/tabs';
import {
  combineLatestWith,
  forkJoin,
  map,
  of,
  Subject,
  switchMap,
  take,
  takeUntil,
} from 'rxjs';
import { ROUTES_CONFIG } from '../../../core/domain/route-names';
import { UserStore } from '../../../login/application/services/user.store';
import { GetBasicsInfoUseCase } from '../../application/usecases/get-basics-info.usecase';
import { GetBranchesInfoUseCase } from '../../application/usecases/get-branches-info.usecase';
import { GetGeneralInfoUseCase } from '../../application/usecases/get-general-info.usecase';
import { SearchByIdOrNameUseCase } from '../../application/usecases/search-by-id-name.usecase';
import { VALID_INTEGRATION_TYPES_TO_CREATE_STORE } from '../../domain/entities/integration-types';
import { CreateOneButtonComponent } from '../components/create-one-button.component';
import { SearchByIdOrNameComponent } from '../components/search-by-id-name.component';
import { CreateContactWithDialogFormService } from '../services/create-contact-with-dialog-form.service';
import { CreateUserWithDialogFormService } from '../services/create-user-with-dialog-form.service';
import { MerchantStoreService } from '../services/merchant-store.service';
import { UpsertStorefrontWithDialogFormService } from '../services/upsert-with-dialog-form.service';

export const ADMIN_TABS = Object.freeze(
  [
    {
      label: 'Información básica',
      route: ROUTES_CONFIG.merchantSettingsBasics,
    },
    { label: 'Información general', route: ROUTES_CONFIG.merchantSettingsInfo },
    { label: 'Cuenta', route: ROUTES_CONFIG.merchantSettingsAccount },
    { label: 'Mdr', route: ROUTES_CONFIG.merchantSettingsMdr },
    {
      label: 'Contacto',
      route: ROUTES_CONFIG.merchantSettingsContact,
      contentActive: true,
    },
    { label: 'Facturación', route: ROUTES_CONFIG.merchantSettingsBilling },
    {
      label: 'Storefronts',
      route: ROUTES_CONFIG.merchantSettingsBranches,
      contentActive: true,
    },
    {
      label: 'Usuarios',
      route: ROUTES_CONFIG.merchantSettingsUsers,
      contentActive: true,
    },
  ].map(i => Object.freeze(i))
);

@Component({
  selector: 'app-admin-layout',
  templateUrl: './admin-layout.component.html',
  imports: [
    ReactiveFormsModule,
    AplazoButtonComponent,
    SearchByIdOrNameComponent,
    AplazoSimpleTableComponents,
    AplazoCommonMessageComponent,
    AplazoTabsComponents,
    AplazoCardComponent,
    CreateOneButtonComponent,
    RouterOutlet,
    AsyncPipe,
  ],
})
export class AdminLayoutComponent implements OnInit, OnDestroy {
  readonly #router = inject(Router);
  readonly route = inject(ActivatedRoute);
  readonly #searchUsecase = inject(SearchByIdOrNameUseCase);
  readonly #generalInfoUseCase = inject(GetGeneralInfoUseCase);
  readonly #basicsInfoUseCase = inject(GetBasicsInfoUseCase);
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #userStore = inject(UserStore);
  readonly #createStorefrontUseCase = inject(
    UpsertStorefrontWithDialogFormService
  );
  readonly #createOneOperatorUseCase = inject(CreateUserWithDialogFormService);
  readonly #createOneContactUseCase = inject(
    CreateContactWithDialogFormService
  );
  readonly #getAllBranchesUseCase = inject(GetBranchesInfoUseCase);
  readonly #destroy = new Subject<void>();

  readonly currentMerchantContent$ = this.#merchantStore.merchantBasics$.pipe(
    map(m => ({
      data: m,
      hasContent: (m?.merchantId && m.merchantId > 0) ?? false,
    }))
  );

  readonly #hasBranches = toSignal(
    this.#merchantStore.merchantBranches$.pipe(
      map(
        d => Boolean(d.merchantId) && Array.isArray(d.data) && d.data.length > 0
      )
    )
  );

  readonly hasBranches = computed(() => this.#hasBranches());

  readonly #currentTabIdx = signal(0);
  readonly currentTabIdx = computed(() => this.#currentTabIdx());

  readonly tabs = ADMIN_TABS;

  readonly searchBy = new FormControl<{ id: number; name: string } | null>(
    null
  );
  readonly isValidIntTypeToCreateStore$ =
    this.#merchantStore.merchantBasics$.pipe(
      map(d => d.intType),
      map(intType =>
        VALID_INTEGRATION_TYPES_TO_CREATE_STORE.includes(intType as any)
      )
    );

  readonly #isRoleEnableToEdit = toSignal(
    this.#userStore.roles$.pipe(
      take(1),
      map(
        roles =>
          roles.includes('ROLE_CONTROL_TOWER_ADMIN') ||
          roles.includes('ROLE_CONTROL_TOWER_MERCHANT_OPS')
      )
    )
  );

  isRoleEnableToEdit = computed(() => this.#isRoleEnableToEdit());

  readonly merchants$ = this.#merchantStore.searchBy$.pipe(
    // combineLatestWith is used to
    // allow refreshing the list of merchants
    // when merchantBasics$ emits a new value
    combineLatestWith(this.#merchantStore.merchantBasics$),
    switchMap(([val]) => {
      if (!val.id && !val.name) {
        return of([]);
      }
      return this.#searchUsecase.execute(val);
    }),
    map(results => {
      return {
        data: results ?? [],
        hasContent: results && Array.isArray(results) && results.length > 0,
        isActiveSearch: Boolean(this.searchBy.value),
      };
    }),
    takeUntil(this.#destroy)
  );

  setMerchant(merchantId: number): void {
    const currMId = this.#merchantStore.selectedMerchantId;

    if (currMId === merchantId) {
      return;
    }

    this.#currentTabIdx.set(0);

    this.#merchantStore.setSelectedMerchantId(merchantId);

    forkJoin([
      this.#basicsInfoUseCase.execute(merchantId),
      this.#generalInfoUseCase.execute(merchantId),
      this.#getAllBranchesUseCase.execute(merchantId),
    ])
      .pipe(take(1))
      .subscribe(([basics, generals, branches]) => {
        this.#merchantStore.setMerchantBasics(basics);
        this.#merchantStore.setMerchantGenerals(generals);
        this.#merchantStore.setBranches(branches);
        this.#router.navigate([ROUTES_CONFIG.merchantSettingsBasics], {
          relativeTo: this.route,
        });
      });
  }

  clearMerchantSelection(): void {
    this.#currentTabIdx.set(0);
    this.#merchantStore.clearAll();
    this.#router.navigate([ROUTES_CONFIG.merchantSettingsBasics], {
      relativeTo: this.route,
    });
  }

  changeTab(event: { index: number }): void {
    const tab = this.tabs[event.index];

    this.#currentTabIdx.set(event.index);
    this.#router.navigate([tab.route], {
      relativeTo: this.route,
    });
  }

  createOneStoreFront(): void {
    this.#createStorefrontUseCase.execute();
  }

  createOneOperator(): void {
    this.#createOneOperatorUseCase.execute();
  }

  createOneContact(): void {
    this.#createOneContactUseCase.execute();
  }

  ngOnInit(): void {
    this.searchBy.valueChanges.pipe(takeUntil(this.#destroy)).subscribe(val => {
      this.#merchantStore.setSearchBy(val);
    });
  }

  ngOnDestroy(): void {
    this.#destroy.next();
    this.#destroy.complete();
    this.#merchantStore.clearAll();
  }
}
