import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import {
  InvoiceRepositoryRequest,
  MerchantInvoiceResponse,
} from '../../domain/dtos/invoice.dto';
import { MerchantInvoiceInfoRepository } from '../../domain/repositories/invoice-info.repository';

@Injectable({ providedIn: 'root' })
export class InvoiceWithHttpRepository
  implements MerchantInvoiceInfoRepository
{
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #apiUrl = this.#environment.apiBaseUrl;

  getInfo(merchantId: number): Observable<MerchantInvoiceResponse> {
    return this.#http.get<MerchantInvoiceResponse>(
      `${this.#apiUrl}/api/v1/merchant/${merchantId}/invoice`
    );
  }

  updateOneBy(request: InvoiceRepositoryRequest): Observable<void> {
    return this.#http.post<void>(
      `${this.#apiUrl}/api/v1/merchant/invoice/${request.merchantId}`,
      {
        paymentFormId: request.paymentFormId,
        cfdiId: request.cfdiId,
        currency: request.currency,
        email: request.email,
        paymentMethod: request.paymentMethodId,
        zipCode: request.zipCode,
        invoiceRegime: request.invoiceRegimeId,
        rfc: request.rfc,
        name: request.name,
        facPubGral: request.facPubGral,
      }
    );
  }
}
