import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import { NewMerchantRequest } from '../../domain/dtos/new-merchant-request.dto';
import { NewMerchantResponse } from '../../domain/dtos/new-merchant-response.dto';
import { NewMerchantRespository } from '../../domain/repositories/new-merchant.repository';

@Injectable({
  providedIn: 'root',
})
export class NewMerchantWithHttpRepository
  implements
    NewMerchantRespository<NewMerchantRequest, Observable<NewMerchantResponse>>
{
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #url = this.#environment.apiBaseUrl;

  create(request: NewMerchantRequest): Observable<NewMerchantResponse> {
    return this.#http.post<NewMerchantResponse>(
      this.#url + '/api/v1/merchant',
      request
    );
  }
}
