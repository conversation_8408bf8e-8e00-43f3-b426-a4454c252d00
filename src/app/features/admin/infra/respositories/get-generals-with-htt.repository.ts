import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import {
  MerchantGeneralInfoResponse,
  MerchantGeneralUpdateRepositoryRequest,
} from '../../domain/dtos/general-info.dto';
import { MerchantGeneralInfoRepository } from '../../domain/repositories/general-info.repository';

@Injectable({ providedIn: 'root' })
export class GeneralsWithHttpRepository
  implements MerchantGeneralInfoRepository
{
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #apiUrl = this.#environment.apiBaseUrl;

  getInfo(merchantId: number): Observable<MerchantGeneralInfoResponse> {
    return this.#http.get<MerchantGeneralInfoResponse>(
      `${this.#apiUrl}/api/v1/merchant/${merchantId}/info`
    );
  }

  updateOneBy(
    request: MerchantGeneralUpdateRepositoryRequest
  ): Observable<void> {
    const { merchantId, ...body } = request;
    merchantId;

    return this.#http.post<void>(
      `${this.#apiUrl}/api/v1/merchant/general/${request.merchantId}`,
      body
    );
  }
}
