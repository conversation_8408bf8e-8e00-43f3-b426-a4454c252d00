import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import { MdrResponseDto, MdrUpdateRequest } from '../../domain/dtos/mdr.dto';
import { MerchantMdrInfoRepository } from '../../domain/repositories/mdr-info.repository';

@Injectable({ providedIn: 'root' })
export class GetMdrInfoWithHttpRepository implements MerchantMdrInfoRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #apiUrl = this.#environment.apiBaseUrl;

  getInfo(merchantId: number): Observable<MdrResponseDto> {
    return this.#http.get<MdrResponseDto>(
      `${this.#apiUrl}/api/v1/merchant-loan-fee-frequency/merchant/${merchantId}`
    );
  }

  updateInfo(request: MdrUpdateRequest): Observable<void> {
    const { id, ...req } = request;

    return this.#http.put<void>(
      `${this.#apiUrl}/api/v1/merchant-loan-fee-frequency/${id}`,
      req
    );
  }
}
