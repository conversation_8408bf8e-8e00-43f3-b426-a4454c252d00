import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import {
  OperatorRepositoryRequestDto,
  OperatorResponse,
} from '../../domain/dtos/user.dto';
import { MerchantOperatorsRepository } from '../../domain/repositories/users-info.repository';

@Injectable({ providedIn: 'root' })
export class OperatorsRepositoryWithHttp
  implements MerchantOperatorsRepository
{
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #apiUrl = this.#environment.apiBaseUrl;

  getInfo(merchantId: number): Observable<OperatorResponse[]> {
    return this.#http.get<OperatorResponse[]>(
      `${this.#apiUrl}/api/v1/merchant/${merchantId}/users`
    );
  }

  createOne(user: OperatorRepositoryRequestDto): Observable<OperatorResponse> {
    const { merchantId, ...body } = user;

    return this.#http.post<OperatorResponse>(
      `${this.#apiUrl}/api/v1/merchant/${merchantId}/users`,
      body
    );
  }

  updateOne(
    user: Partial<OperatorRepositoryRequestDto>
  ): Observable<OperatorResponse> {
    const { merchantId, ...body } = user;

    return this.#http.post<OperatorResponse>(
      `${this.#apiUrl}/api/v1/merchant/${merchantId}/users/update`,
      body
    );
  }
}
