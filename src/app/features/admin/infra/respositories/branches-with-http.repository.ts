import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import { BranchResponse } from '../../domain/dtos/branch.dto';
import { MerchantBranchesRepository } from '../../domain/repositories/branches-info.repository';

@Injectable({ providedIn: 'root' })
export class MerchantBranchesWithHttp implements MerchantBranchesRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #apiUrl = this.#environment.apiBaseUrl;

  getInfo(merchantId: number): Observable<BranchResponse[]> {
    return this.#http.get<BranchResponse[]>(
      `${this.#apiUrl}/api/v1/merchant/${merchantId}/branches`
    );
  }

  upsertOne(args: {
    merchantId: number;
    name: string;
    id?: number;
    active?: boolean;
  }): Observable<any> {
    const body: {
      name: string;
      id?: number;
      active?: boolean;
    } = {
      name: args.name,
    };

    if (Object.prototype.hasOwnProperty.call(args, 'id')) {
      body.id = args.id;
    }

    if (Object.prototype.hasOwnProperty.call(args, 'active')) {
      body.active = args.active;
    }

    return this.#http.post(
      `${this.#apiUrl}/api/v1/merchant/${args.merchantId}/branches`,
      body
    );
  }
}
