import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import {
  SearchByRepositoryRequest,
  SearchByRepositoryResponse,
} from '../../domain/dtos/search.dto';
import { SearchMerchantByRepository } from '../../domain/repositories/search-merchant-by.repository';

@Injectable({ providedIn: 'root' })
export class SearchByWithHttpRepository
  implements
    SearchMerchantByRepository<
      SearchByRepositoryRequest,
      Observable<SearchByRepositoryResponse[]>
    >
{
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #http = inject(HttpClient);
  readonly #url = `${this.#environment.apiBaseUrl}/api/v1/merchant/search`;

  searchBy(
    args: SearchByRepositoryRequest
  ): Observable<SearchByRepositoryResponse[]> {
    let params = new HttpParams();

    if (args.id) {
      params = params.append('id', args.id.toString());
    } else {
      params = params.append('name', args.name ?? '');
    }

    return this.#http.get<SearchByRepositoryResponse[]>(this.#url, {
      params,
    });
  }
}
