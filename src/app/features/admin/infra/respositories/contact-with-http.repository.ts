import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import {
  NewContactDto,
  RetrievedContactResponse,
  UpdateContactDto,
} from '../../domain/dtos/contact.dto';
import { MerchantContactsRepository } from '../../domain/repositories/contact-info.repository';

@Injectable({ providedIn: 'root' })
export class MerchantContactsWithHttp implements MerchantContactsRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #apiUrl = this.#environment.apiBaseUrl;

  getInfo(merchantId: number): Observable<RetrievedContactResponse[]> {
    return this.#http.get<RetrievedContactResponse[]>(
      `${this.#apiUrl}/api/v1/merchant/${merchantId}/contact-info`
    );
  }

  createOne(request: NewContactDto): Observable<RetrievedContactResponse> {
    return this.#http.post<RetrievedContactResponse>(
      `${this.#apiUrl}/api/v1/merchant-contact-info`,
      request
    );
  }

  updateOne(request: UpdateContactDto): Observable<RetrievedContactResponse> {
    return this.#http.patch<RetrievedContactResponse>(
      `${this.#apiUrl}/api/v1/merchant-contact-info`,
      request
    );
  }
}
