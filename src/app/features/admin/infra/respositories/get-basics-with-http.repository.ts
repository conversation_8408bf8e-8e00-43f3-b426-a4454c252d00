import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import { MerchantBasicsResponse } from '../../domain/dtos/basics.dto';
import { MerchantBasicInfoRepository } from '../../domain/repositories/basic-info.repository';

@Injectable({ providedIn: 'root' })
export class GetBasicsWithHttpRepository
  implements MerchantBasicInfoRepository
{
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly #apiUrl = this.#environment.apiBaseUrl;

  getInfo(merchantId: number): Observable<MerchantBasicsResponse> {
    return this.#http.get<MerchantBasicsResponse>(
      `${this.#apiUrl}/api/v1/merchant/${merchantId}`
    );
  }

  updateByMerchantId(request: {
    merchantId: number;
    merchantName?: string;
    merchantStatus?: string;
    catIntegrationType?: string;
  }): Observable<void> {
    const body: {
      merchantName?: string;
      merchantStatus?: string;
      catIntegrationType?: string;
    } = {};

    if (request.merchantName) {
      body.merchantName = request.merchantName;
    }

    if (request.merchantStatus) {
      body.merchantStatus = request.merchantStatus;
    }

    if (request.catIntegrationType) {
      body.catIntegrationType = request.catIntegrationType;
    }

    return this.#http.post<void>(
      `${this.#apiUrl}/api/v1/merchant/info/${request.merchantId}`,
      body
    );
  }
}
