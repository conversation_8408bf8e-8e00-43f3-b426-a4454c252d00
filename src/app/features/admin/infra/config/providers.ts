import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { UpdateOperatorUseCase } from '../../application/usecases/edit-user.usecase';
import { GetAccountInfoUseCase } from '../../application/usecases/get-account-info.usecase';
import { GetBasicsInfoUseCase } from '../../application/usecases/get-basics-info.usecase';
import { GetBranchesInfoUseCase } from '../../application/usecases/get-branches-info.usecase';
import { GetContactInfoUseCase } from '../../application/usecases/get-contact-info.usecase';
import { GetGeneralInfoUseCase } from '../../application/usecases/get-general-info.usecase';
import { GetInvoiceInfoUseCase } from '../../application/usecases/get-invoice-info.usecase';
import { GetMdrInfoUseCase } from '../../application/usecases/get-mdr-info.usecase';
import { GetUsersInfoUseCase } from '../../application/usecases/get-users-info.usecase';
import { CreateOneContactUseCase } from '../../application/usecases/new-contact.usecase';
import { CreateOperatorUseCase } from '../../application/usecases/new-user.usecase';
import { SearchByIdOrNameUseCase } from '../../application/usecases/search-by-id-name.usecase';
import { UpdateAccountUsecase } from '../../application/usecases/update-account.usecase';
import { UpdateInvoiceInfoUsecase } from '../../application/usecases/update-invoice-info.usecase';
import { UpdateMdrUsecase } from '../../application/usecases/update-mdr.usecase';
import { UpdateMerchantBasicsUsecase } from '../../application/usecases/update-merchant-basics.usecase';
import { UpdateMerchantGeneralsUsecase } from '../../application/usecases/update-merchant-generals.usecase';
import { UpdateOneContactUseCase } from '../../application/usecases/update-user.usecase';
import { UpsertBranchUseCase } from '../../application/usecases/upsert-branch.usecase';
import { MerchantAccountRepository } from '../../domain/repositories/account-info.repository';
import { MerchantBasicInfoRepository } from '../../domain/repositories/basic-info.repository';
import { MerchantBranchesRepository } from '../../domain/repositories/branches-info.repository';
import { MerchantContactsRepository } from '../../domain/repositories/contact-info.repository';
import { MerchantGeneralInfoRepository } from '../../domain/repositories/general-info.repository';
import { MerchantInvoiceInfoRepository } from '../../domain/repositories/invoice-info.repository';
import { MerchantMdrInfoRepository } from '../../domain/repositories/mdr-info.repository';
import { NewMerchantRespository } from '../../domain/repositories/new-merchant.repository';
import { SearchMerchantByRepository } from '../../domain/repositories/search-merchant-by.repository';
import { MerchantOperatorsRepository } from '../../domain/repositories/users-info.repository';
import { AccountWithHttpRepository } from '../respositories/account-with-http.repository';
import { MerchantBranchesWithHttp } from '../respositories/branches-with-http.repository';
import { MerchantContactsWithHttp } from '../respositories/contact-with-http.repository';
import { GetBasicsWithHttpRepository } from '../respositories/get-basics-with-http.repository';
import { GeneralsWithHttpRepository } from '../respositories/get-generals-with-htt.repository';
import { GetMdrInfoWithHttpRepository } from '../respositories/get-mdr-with-http.repository';
import { InvoiceWithHttpRepository } from '../respositories/invoice-with-http.repository';
import { NewMerchantWithHttpRepository } from '../respositories/new-merchant-with-http.repository';
import { SearchByWithHttpRepository } from '../respositories/search-by-with-http.repository';
import { OperatorsRepositoryWithHttp } from '../respositories/users-with-http.repository';
import { CreateContactWithDialogFormService } from '../services/create-contact-with-dialog-form.service';
import { CreateUserWithDialogFormService } from '../services/create-user-with-dialog-form.service';
import { UpdateContactWithDialogFormService } from '../services/edit-contact-with-dialog-form.service';
import { UpdateUserWithDialogFormService } from '../services/edit-user-with-dialog-form.service';
import { UpsertStorefrontWithDialogFormService } from '../services/upsert-with-dialog-form.service';

export function provideCreationRepositories(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: NewMerchantRespository,
      useClass: NewMerchantWithHttpRepository,
    },
  ]);
}

export function provideMerchantAdminRepositories(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: MerchantBasicInfoRepository,
      useClass: GetBasicsWithHttpRepository,
    },
    {
      provide: MerchantGeneralInfoRepository,
      useClass: GeneralsWithHttpRepository,
    },
    {
      provide: SearchMerchantByRepository,
      useClass: SearchByWithHttpRepository,
    },
    {
      provide: MerchantAccountRepository,
      useClass: AccountWithHttpRepository,
    },
    {
      provide: MerchantMdrInfoRepository,
      useClass: GetMdrInfoWithHttpRepository,
    },
    {
      provide: MerchantContactsRepository,
      useClass: MerchantContactsWithHttp,
    },
    {
      provide: MerchantInvoiceInfoRepository,
      useClass: InvoiceWithHttpRepository,
    },
    {
      provide: MerchantBranchesRepository,
      useClass: MerchantBranchesWithHttp,
    },
    {
      provide: MerchantOperatorsRepository,
      useClass: OperatorsRepositoryWithHttp,
    },
  ]);
}

export function provideMerchantAdminUseCases(): EnvironmentProviders {
  return makeEnvironmentProviders([
    GetBasicsInfoUseCase,
    GetGeneralInfoUseCase,
    SearchByIdOrNameUseCase,
    GetAccountInfoUseCase,
    GetMdrInfoUseCase,
    GetContactInfoUseCase,
    GetInvoiceInfoUseCase,
    GetBranchesInfoUseCase,
    GetUsersInfoUseCase,
    UpdateMerchantBasicsUsecase,
    UpsertBranchUseCase,
    UpsertStorefrontWithDialogFormService,
    UpdateMerchantGeneralsUsecase,
    UpdateAccountUsecase,
    UpdateInvoiceInfoUsecase,
    UpdateMdrUsecase,
    CreateOperatorUseCase,
    CreateUserWithDialogFormService,
    UpdateUserWithDialogFormService,
    UpdateOperatorUseCase,
    CreateOneContactUseCase,
    CreateContactWithDialogFormService,
    UpdateOneContactUseCase,
    UpdateContactWithDialogFormService,
  ]);
}
