import { inject } from '@angular/core';
import { CanDeactivateFn } from '@angular/router';
import { AplazoConfirmDialogComponent } from '@aplazo/shared-ui/merchant';
import { DialogService } from '@ngneat/dialog';
import { lastValueFrom, take } from 'rxjs';
import { MerchantCreationComponent } from '../pages/creation.component';

export const exitCreationGuard: CanDeactivateFn<
  MerchantCreationComponent
> = async (component: MerchantCreationComponent) => {
  const isPristine = component.form.pristine;
  const hasFinished = component.merchantCreated();

  if (hasFinished) {
    return true;
  }

  if (isPristine) {
    return true;
  }

  const dialog = inject(DialogService);

  const result: { confirmation: boolean } | undefined = await lastValueFrom(
    dialog
      .open(AplazoConfirmDialogComponent, {
        data: {
          title:
            'Si sale ahora los datos ingresados se perderan. ¿Está seguro de salir?',
          cancelButton: 'Completar registro',
          acceptButton: 'Salir',
        },
        maxWidth: '320px',
      })
      .afterClosed$.pipe(take(1))
  );

  if (result?.confirmation) {
    return true;
  }

  return false;
};
