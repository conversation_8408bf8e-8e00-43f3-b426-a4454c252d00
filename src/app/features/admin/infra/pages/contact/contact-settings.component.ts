import { AsyncPipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoCommonMessageComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { lastValueFrom, take } from 'rxjs';
import { GetContactInfoUseCase } from '../../../application/usecases/get-contact-info.usecase';
import { ContactUI } from '../../../domain/dtos/contact.dto';
import { UpdateContactWithDialogFormService } from '../../services/edit-contact-with-dialog-form.service';
import { MerchantStoreService } from '../../services/merchant-store.service';

@Component({
  selector: 'app-contact-settings',
  templateUrl: './contact-settings.component.html',
  imports: [
    AplazoCardComponent,
    AplazoSimpleTableComponents,
    AplazoCommonMessageComponent,
    AplazoDynamicPipe,
    AsyncPipe,
  ],
})
export class MerchantContactSettingsComponent implements OnInit {
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #retrieveContacts = inject(GetContactInfoUseCase);
  readonly #updateContact = inject(UpdateContactWithDialogFormService);

  readonly contacts$ = this.#merchantStore.merchantContacts$;

  async setInfo(): Promise<void> {
    const currMId = this.#merchantStore.selectedMerchantId;

    if (!currMId) {
      return;
    }

    const storedContacts = await lastValueFrom(
      this.#merchantStore.merchantContacts$.pipe(take(1))
    );

    if (storedContacts.merchantId === currMId) {
      return;
    }

    const result = await lastValueFrom(
      this.#retrieveContacts.execute(currMId).pipe(take(1))
    );

    this.#merchantStore.setMerchantContacts(result);
  }

  saveChanges(contact: ContactUI): void {
    this.#updateContact.execute(contact);
  }

  ngOnInit(): void {
    this.setInfo();
  }
}
