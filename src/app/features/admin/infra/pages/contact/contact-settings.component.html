<aplz-ui-card>
  @if (contacts$ | async; as contacts) {
    @if (contacts.data.length === 0) {
      <aplz-ui-common-message
        [i18Text]="{
          title: 'No hay contactos registrados',
          description: '',
        }"
        imgName="emptyLoans">
      </aplz-ui-common-message>
    } @else {
      <div class="max-h-96 overflow-y-auto">
        <table aplzSimpleTable aria-label="Merchant Result List">
          <tr aplzSimpleTableHeaderRow>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              ID
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              Departamento
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              Phone
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              Email
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              Rol
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              Última actualización
            </th>
          </tr>

          @for (item of contacts.data; track item) {
            <tr
              aplzSimpleTableBodyRow
              [striped]="true"
              (bodyRowClick)="saveChanges(item)">
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.id }}
              </td>
              <td aplzSimpleTableBodyCell class="font-semibold text-center">
                {{ item.businessArea }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.phone || 'No proporcionado' }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.email }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.role }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.updateAt | aplzDynamicPipe: 'date' }}
              </td>
            </tr>
          }
        </table>
      </div>
    }
  }
</aplz-ui-card>
