<aplz-ui-card>
  <form [formGroup]="form" class="p-4" (ngSubmit)="saveChanges()">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
      <aplz-ui-form-field>
        <aplz-ui-form-label>Merchant ID</aplz-ui-form-label>
        <input
          aplzFormInput
          formControlName="merchantId"
          id="merchant-id"
          type="text" />
        <ng-container aplzFormError>
          @if (
            merchantId.touched &&
            merchantId.invalid &&
            merchantId.hasError('required')
          ) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
      </aplz-ui-form-field>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
      <aplz-ui-form-field>
        <aplz-ui-form-label>Nombre del comercio</aplz-ui-form-label>
        <input
          aplzFormInput
          formControlName="name"
          id="merchant-name"
          aplazoTrimSpaces
          type="text" />
        <ng-container aplzFormError>
          @if (name.touched && name.invalid && name.hasError('required')) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
      </aplz-ui-form-field>
      <fieldset
        class="cursor-not-allowed min-h-[55px] flex items-center border rounded-lg pl-4 -mt-2 mb-8 border-dark-background">
        <legend class="text-sm text-dark-tertiary px-2">
          Última actualización
        </legend>
        <span class="text-dark-secondary">
          {{ updatedAt$ | async | aplzDynamicPipe: 'date' }}
        </span>
      </fieldset>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
      @if (!editing()) {
        <aplz-ui-form-field>
          <aplz-ui-form-label>Estatus</aplz-ui-form-label>
          <input
            aplzFormInput
            formControlName="status"
            id="merchant-status"
            type="text"
            [ngClass]="{
              'shadow-md': isApproved(),
              'shadow-aplazo-aplazo': isApproved(),
            }"
            class="shadow-sm shadow-aplazo-aplazo" />
          <ng-container aplzFormError>
            @if (
              status.touched && status.invalid && status.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      } @else {
        <aplz-ui-select formControlName="status">
          @for (item of statusOptions; track $index) {
            <aplz-ui-option
              [ngValue]="item.value"
              [label]="item.label"></aplz-ui-option>
          }
        </aplz-ui-select>
      }

      @if (!editing()) {
        <aplz-ui-form-field>
          <aplz-ui-form-label>Tipo Integración</aplz-ui-form-label>
          <input
            aplzFormInput
            formControlName="integrationType"
            id="intType"
            type="text" />
          <ng-container aplzFormError>
            @if (
              integrationType.touched &&
              integrationType.invalid &&
              integrationType.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      } @else {
        <aplz-ui-select formControlName="integrationType">
          @for (item of integrationTypeOptions; track $index) {
            <aplz-ui-option
              [ngValue]="item.value"
              [label]="item.label"></aplz-ui-option>
          }
        </aplz-ui-select>
      }
    </div>

    @if (isRoleEnableToEdit()) {
      <div class="flex justify-center md:justify-end gap-6 flex-wrap mt-6">
        @if (!editing()) {
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="enableEdition()">
            Actualizar Información
          </button>
        } @else {
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="cancelEdition()">
            Cancelar Edición
          </button>

          <button
            aplzButton
            type="submit"
            aplzAppearance="solid"
            aplzColor="dark"
            size="md">
            Guardar Cambios
          </button>
        }
      </div>
    }
  </form>
</aplz-ui-card>
