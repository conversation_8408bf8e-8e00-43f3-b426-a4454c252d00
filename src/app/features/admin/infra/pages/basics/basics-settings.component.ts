import { AsyncPipe, NgClass } from '@angular/common';
import {
  Component,
  computed,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NotifierService } from '@aplazo/merchant/shared';
import {
  AplazoDynamicPipe,
  AplazoTrimSpacesDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import {
  isEmpty,
  lastValueFrom,
  map,
  startWith,
  Subject,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { UserStore } from '../../../../login/application/services/user.store';
import { UpdateMerchantBasicsUsecase } from '../../../application/usecases/update-merchant-basics.usecase';
import {
  MerchantBasicsUI,
  MerchantUpdateBasicsUIRequest,
} from '../../../domain/dtos/basics.dto';
import { INTEGRATION_TYPES } from '../../../domain/entities/integration-types';
import { MerchantStoreService } from '../../services/merchant-store.service';

@Component({
  selector: 'app-basics-settings',
  templateUrl: './basics-settings.component.html',
  imports: [
    NgClass,
    AsyncPipe,
    ReactiveFormsModule,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoCardComponent,
    AplazoSelectComponents,
    AplazoDynamicPipe,
    AplazoTrimSpacesDirective,
  ],
})
export class MerchantBasicsSettingsComponent implements OnInit, OnDestroy {
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #notifier = inject(NotifierService);
  readonly #updateStatusUsecase = inject(UpdateMerchantBasicsUsecase);
  readonly #userStore = inject(UserStore);
  readonly #destroy = new Subject<void>();
  readonly updatedAt$ = this.#merchantStore.merchantBasics$.pipe(
    map(m => m.updatedAt)
  );

  readonly statusOptions = [
    { label: 'Creado', value: 'CREATED' },
    { label: 'Período de prueba', value: 'TRIAL_PERIOD' },
    { label: 'Configuración', value: 'CONFIGURATION' },
    { label: 'Aprobación pendiente', value: 'PENDING_APPROVAL' },
    { label: 'Freeze', value: 'FREEZE' },
    { label: 'Aprobado', value: 'APPROVED' },
    { label: 'Rechazado', value: 'REJECTED' },
    { label: 'Bloqueado', value: 'BLOCKED' },
    { label: 'Churn', value: 'CHURN' },
    { label: 'Migrado', value: 'MIGRATED' },
    { label: 'Información pendiente', value: 'PENDING_INFO' },
  ];

  readonly integrationTypeOptions = INTEGRATION_TYPES;

  merchantId = new FormControl<number>(0, {
    nonNullable: true,
    validators: [Validators.required],
  });
  name = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  status = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  integrationType = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });

  form = new FormGroup({
    merchantId: this.merchantId,
    name: this.name,
    status: this.status,
    integrationType: this.integrationType,
  });

  #isApproved = toSignal(
    this.status.valueChanges.pipe(
      startWith(this.status.value),
      map(status => status === 'APPROVED')
    )
  );
  #editing = signal(false);
  #isRoleEnableToEdit = toSignal(
    this.#userStore.roles$.pipe(
      take(1),
      map(
        roles =>
          roles.includes('ROLE_CONTROL_TOWER_ADMIN') ||
          roles.includes('ROLE_CONTROL_TOWER_MERCHANT_OPS')
      )
    )
  );

  isApproved = computed(() => this.#isApproved());
  editing = computed(() => this.#editing());
  isRoleEnableToEdit = computed(() => this.#isRoleEnableToEdit());

  enableEdition(): void {
    this.#editing.set(true);
    this.status.enable();
    this.name.enable();
    this.integrationType.enable();
  }

  async cancelEdition(): Promise<void> {
    await this.#statusRollback();
    this.#disableEdition();
  }

  async saveChanges(): Promise<void> {
    const basics = await this.#lastBasics();

    const request: MerchantUpdateBasicsUIRequest = {
      merchantId: this.merchantId.value,
      merchantName: null,
      status: null,
      intType: null,
    };

    if (this.name.valid && this.name.value !== basics.merchantName) {
      request.merchantName = this.name.value;
    }

    if (this.status.valid && this.status.value !== basics.status) {
      request.status = this.status.value;
    }

    if (
      this.integrationType.valid &&
      this.integrationType.value !== basics.intType
    ) {
      request.intType = this.integrationType.value;
    }

    if (
      this.name.hasError('required') ||
      this.status.hasError('required') ||
      this.integrationType.hasError('required')
    ) {
      this.#notifier.warning({
        title: 'Revise la información ingresada.',
        message: 'Los campos para edición no pueden estar vacíos.',
      });
      return;
    }

    if (!request.merchantName && !request.status && !request.intType) {
      this.#notifier.info({
        title: 'No hay cambios para guardar.',
      });
      return;
    }

    const isCompleteWithoutStream = await lastValueFrom(
      this.#updateStatusUsecase.execute(request).pipe(
        take(1),
        tap(updated => {
          this.#merchantStore.setMerchantBasics({
            ...basics,
            status: updated.merchantStatus ?? basics.status,
            merchantName: updated.merchantName ?? basics.merchantName,
            intType: updated.catIntegrationType ?? basics.intType,
          });
        }),
        isEmpty()
      )
    );

    if (isCompleteWithoutStream) {
      this.#statusRollback();
    }

    this.#disableEdition();
  }

  ngOnInit(): void {
    this.#merchantStore.merchantBasics$
      .pipe(takeUntil(this.#destroy))
      .subscribe(merchant => {
        this.merchantId.setValue(merchant.merchantId);
        this.name.setValue(merchant.merchantName);
        this.status.setValue(merchant.status);
        this.integrationType.setValue(merchant.intType);

        this.#disableEdition();
        this.form.disable();
      });
  }

  ngOnDestroy(): void {
    this.#destroy.next();
    this.#destroy.complete();
  }

  async #lastBasics(): Promise<MerchantBasicsUI> {
    return await lastValueFrom(
      this.#merchantStore.merchantBasics$.pipe(take(1))
    );
  }

  async #statusRollback(): Promise<void> {
    const basics = await this.#lastBasics();

    this.status.setValue(basics.status);
    this.name.setValue(basics.merchantName);
    this.integrationType.setValue(basics.intType);
  }

  #disableEdition(): void {
    this.#editing.set(false);
    this.status.disable();
    this.name.disable();
    this.integrationType.disable();
  }
}
