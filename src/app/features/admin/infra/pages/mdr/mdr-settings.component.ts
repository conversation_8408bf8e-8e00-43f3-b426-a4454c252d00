import {
  Component,
  computed,
  inject,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NotifierService, TemporalService } from '@aplazo/merchant/shared';
import { OnlyDecimalNumbersDirective } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import { AplazoConfirmDialogComponent } from '@aplazo/shared-ui/merchant';
import { DialogService } from '@ngneat/dialog';
import {
  distinctUntilChanged,
  lastValueFrom,
  map,
  Subject,
  take,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs';
import { UserStore } from '../../../../login/application/services/user.store';
import {
  defaultMdrInfo,
  GetMdrInfoUseCase,
} from '../../../application/usecases/get-mdr-info.usecase';
import { UpdateMdrUsecase } from '../../../application/usecases/update-mdr.usecase';
import {
  InstallmentFrequency,
  MdrList,
  MdrUIDto,
} from '../../../domain/dtos/mdr.dto';
import { MerchantStoreService } from '../../services/merchant-store.service';

@Component({
  selector: 'app-merchant-mdr-settings',
  templateUrl: './mdr-settings.component.html',
  imports: [
    ReactiveFormsModule,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoCardComponent,
    AplazoFormDatepickerComponent,
    AplazoSelectComponents,
    OnlyDecimalNumbersDirective,
  ],
})
export class MerchantMDRSettingsComponent implements OnInit, OnDestroy {
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #userStore = inject(UserStore);
  readonly #notifier = inject(NotifierService);
  readonly #temporal = inject(TemporalService);
  readonly #getUsecase = inject(GetMdrInfoUseCase);
  readonly #updateUsecase = inject(UpdateMdrUsecase);
  readonly #dialog = inject(DialogService);

  readonly #destroy$ = new Subject<void>();

  readonly #editing = signal(false);
  #isRoleEnableToEdit = toSignal(
    this.#userStore.roles$.pipe(
      take(1),
      map(
        roles =>
          roles.includes('ROLE_CONTROL_TOWER_ADMIN') ||
          roles.includes('ROLE_CONTROL_TOWER_MERCHANT_OPS')
      )
    )
  );
  readonly #labels = toSignal(
    this.#merchantStore.merchantMdr$.pipe(
      map(mdr => {
        if (mdr) {
          const labels = Object.keys(mdr);

          return labels;
        }

        return ['5'];
      }),

      takeUntil(this.#destroy$)
    ),
    {
      initialValue: [],
    }
  );

  readonly labels = computed(() => this.#labels());

  readonly isRoleEnableToEdit = computed(() => this.#isRoleEnableToEdit());

  readonly editing = computed(() => this.#editing());

  readonly today = this.#temporal.todayRawDayFirst;

  readonly feeOps = new FormControl<number>(0, {
    nonNullable: true,
    validators: [Validators.required, Validators.min(0)],
  });
  readonly feePct = new FormControl<number>(0, {
    nonNullable: true,
    validators: [Validators.required, Validators.max(100), Validators.min(0)],
  });
  readonly promoFee = new FormControl<number>(0, {
    nonNullable: true,
    validators: [Validators.required, Validators.max(100), Validators.min(0)],
  });
  readonly promoFeeEndDate = new FormControl<Date | null>(null);
  readonly updatedAt = new FormControl<Date | null>(null);
  readonly installmentFrequency = new FormControl<InstallmentFrequency>(5, {
    nonNullable: true,
    validators: [Validators.required],
  });

  readonly form = new FormGroup({
    feeOps: this.feeOps,
    feePct: this.feePct,
    promoFee: this.promoFee,
    promoFeeEndDate: this.promoFeeEndDate,
    updatedAt: this.updatedAt,
    installmentFrequency: this.installmentFrequency,
  });

  async start(): Promise<void> {
    const currMId = await lastValueFrom(
      this.#merchantStore.selectedMerchantId$.pipe(take(1))
    );

    let selected: MdrUIDto = defaultMdrInfo[5];

    try {
      const result = await lastValueFrom(
        this.#getUsecase.execute(currMId).pipe(take(1))
      );

      this.#merchantStore.setMdrInfo(result);
      if (result[5]) {
        selected = result[5];
      } else if (result[3]) {
        selected = result[3];
      } else if (result[8]) {
        selected = result[8];
      } else if (result[1]) {
        selected = result[1];
      }
    } catch (error) {
      this.#notifier.error({ title: 'Error al cargar la información de MDR' });
    }

    this.enableEdition();
    this.#fillForm(selected);
    this.#disableEdition();
  }

  enableEdition(): void {
    this.#editing.set(true);
    this.feeOps.enable();
    this.feePct.enable();
    this.promoFee.enable();
    this.promoFeeEndDate.enable();
  }

  async cancelEdition(): Promise<void> {
    await this.#rollback();
    this.#disableEdition();
  }

  async saveChanges(): Promise<void> {
    const currMId = await lastValueFrom(
      this.#merchantStore.selectedMerchantId$.pipe(take(1))
    );

    const storedData = await this.#lastBasics();

    if (!storedData) {
      return;
    }

    const storedMdr = storedData[this.installmentFrequency.value];

    if (!storedMdr) {
      return;
    }

    if (
      this.feeOps.invalid ||
      this.feePct.invalid ||
      this.promoFee.invalid ||
      this.promoFeeEndDate.invalid
    ) {
      this.#notifier.warning({
        title: 'Revise la información ingresada',
        message:
          'Los campos para edición no pueden estar vacíos o contener errores',
      });
      return;
    }

    let isSameInfo = false;

    const isoDate = this.promoFeeEndDate.value
      ? this.promoFeeEndDate.value.toISOString()
      : null;

    if (
      storedMdr.feeOps === this.feeOps.value &&
      storedMdr.feePct === this.feePct.value &&
      storedMdr.promoFee === this.promoFee.value &&
      storedMdr.promoFeeEndIsoDate === isoDate
    ) {
      isSameInfo = true;
    }

    if (isSameInfo) {
      this.#notifier.info({ title: 'No hay cambios para guardar' });
      return;
    }

    const feePct = Number(this.feePct.value);

    if (!isNaN(feePct) && feePct > 0 && feePct < 1) {
      const dialogRef = this.#dialog.open(AplazoConfirmDialogComponent, {
        data: {
          title: `¿Está seguro de continuar con un MDR Fee de ${feePct / 100} ?`,
          cancelButton: 'Corregir MDR fee',
          acceptButton: 'Estoy seguro',
        },
        maxWidth: '320px',
      });

      const result = await lastValueFrom(dialogRef.afterClosed$.pipe(take(1)));

      if (!result?.confirmation) {
        return;
      }
    }

    const promoFee = Number(this.promoFee.value);

    if (!isNaN(promoFee) && promoFee > 0 && promoFee < 1) {
      const dialogRef = this.#dialog.open(AplazoConfirmDialogComponent, {
        data: {
          title: `¿Está seguro de continuar con un Promo Fee de ${promoFee / 100} ?`,
          cancelButton: 'Corregir Promo fee',
          acceptButton: 'Estoy seguro',
        },
        maxWidth: '320px',
      });

      const result = await lastValueFrom(dialogRef.afterClosed$.pipe(take(1)));

      if (!result?.confirmation) {
        return;
      }
    }

    const promoFeeEndIsoDate = this.promoFeeEndDate.value
      ? this.promoFeeEndDate.value.toISOString()
      : null;

    const mdrUI: Partial<MdrUIDto> = {
      feeOps: this.feeOps.value,
      feePct,
      promoFee,
      promoFeeEndDate: promoFeeEndIsoDate,
      promoFeeEndIsoDate,
      merchantId: currMId,
      installmentFrequencyId: storedMdr.installmentFrequencyId,
      id: storedMdr.id,
    };

    try {
      await lastValueFrom(
        this.#updateUsecase.execute(mdrUI).pipe(
          take(1),

          tap(mdr => {
            const newList: MdrList = {
              ...storedData,
              [this.installmentFrequency.value]: {
                ...storedMdr,
                ...mdr,
              },
            };

            this.#merchantStore.setMdrInfo(newList);

            this.updatedAt.setValue(new Date());
          })
        )
      );

      this.#disableEdition();
      this.#notifier.success({ title: 'MDR actualizado correctamente' });
    } catch (error) {
      this.#rollback();
      this.#disableEdition();
      console.warn(error);
    }
  }

  ngOnInit(): void {
    this.start();

    this.installmentFrequency.valueChanges
      .pipe(
        distinctUntilChanged(),
        withLatestFrom(this.#merchantStore.merchantMdr$),

        tap(([installmentFrequency, storedMdr]) => {
          const mdr = (storedMdr && storedMdr[installmentFrequency]) ?? null;

          if (!mdr) {
            return;
          }

          this.#fillForm(mdr);
        }),

        takeUntil(this.#destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  #fillForm(mdr: Partial<MdrUIDto>): void {
    if (mdr?.feeOps != null) {
      this.feeOps.setValue(mdr.feeOps);
    }

    if (mdr?.feePct != null) {
      this.feePct.setValue(mdr.feePct);
    }

    if (mdr?.promoFee != null) {
      this.promoFee.setValue(mdr.promoFee);
    }

    if (mdr?.promoFeeEndDate != null) {
      const date = new Date(mdr.promoFeeEndDate);
      this.promoFeeEndDate.setValue(date);
    } else {
      this.promoFeeEndDate.reset();
    }

    if (mdr?.installmentFrequency != null) {
      this.installmentFrequency.setValue(mdr.installmentFrequency);
    }

    if (mdr?.updatedAt != null) {
      const date = new Date(mdr.updatedAt);
      this.updatedAt.setValue(date);
    } else {
      this.updatedAt.reset();
    }
  }

  async #lastBasics(): Promise<MdrList | null> {
    return await lastValueFrom(this.#merchantStore.merchantMdr$.pipe(take(1)));
  }

  async #rollback(): Promise<void> {
    const data = await this.#lastBasics();
    if (!data) {
      return;
    }

    const mdr = data[this.installmentFrequency.value];

    if (mdr) {
      this.#fillForm(mdr);
    }
  }

  #disableEdition(): void {
    this.#editing.set(false);
    this.feeOps.disable();
    this.feePct.disable();
    this.promoFee.disable();
    this.promoFeeEndDate.disable();
    this.updatedAt.disable();

    if (this.labels().length <= 1) {
      this.installmentFrequency.disable();
    }
  }
}
