<aplz-ui-card>
  <form [formGroup]="form" class="p-4" (ngSubmit)="saveChanges()">
    <div
      class="grid grid-cols-1 md:grid-cols-2 gap-x-4"
      [class.mb-6]="labels().length > 1">
      @if (labels().length <= 1) {
        <aplz-ui-form-field>
          <aplz-ui-form-label>Installments</aplz-ui-form-label>
          <input
            aplzFormInput
            formControlName="installmentFrequency"
            id="instalments"
            type="text" />
          <ng-container aplzFormError>
            @if (
              installmentFrequency.touched &&
              installmentFrequency.invalid &&
              installmentFrequency.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      } @else {
        <div
          class="flex gap-2 items-center p-2 border-dark-secondary border rounded-lg">
          <span class="select-none text-sm font-light text-dark-secondary">
            Installments
          </span>

          <aplz-ui-select formControlName="installmentFrequency">
            @for (item of labels(); track $index) {
              <aplz-ui-option [ngValue]="item" [label]="item"></aplz-ui-option>
            }
          </aplz-ui-select>
        </div>
      }
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
      <aplz-ui-form-field>
        <aplz-ui-form-label>MDR Fee</aplz-ui-form-label>
        <input
          aplzFormInput
          aplazoDecimalNumbers
          formControlName="feePct"
          id="feePct"
          type="text"
          inputmode="numeric" />
        <p
          aplzInputPrefix
          class="text-dark-tertiary mx-2 select-none pt-3 pb-2 font-semibold">
          %
        </p>
        <p aplzFormHint>Número entre 0 y 100</p>
        <ng-container aplzFormError>
          @if (
            feePct.touched && feePct.invalid && feePct.hasError('required')
          ) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
        <ng-container aplzFormError>
          @if (
            feePct.touched &&
            feePct.invalid &&
            !feePct.hasError('required') &&
            feePct.hasError('min')
          ) {
            <p>Debe ser mayor a 0</p>
          }
        </ng-container>
        <ng-container aplzFormError>
          @if (
            feePct.touched &&
            feePct.invalid &&
            !feePct.hasError('required') &&
            feePct.hasError('max')
          ) {
            <p>Debe ser menor a 100</p>
          }
        </ng-container>
      </aplz-ui-form-field>
      <aplz-ui-form-field>
        <aplz-ui-form-label>Mdr Ops</aplz-ui-form-label>
        <input
          aplzFormInput
          aplazoDecimalNumbers
          formControlName="feeOps"
          id="feeOps"
          type="text"
          inputmode="numeric" />
        <p
          aplzInputPrefix
          class="text-dark-tertiary mx-2 select-none pt-3 pb-2 font-semibold">
          $
        </p>
        <ng-container aplzFormError>
          @if (
            feeOps.touched && feeOps.invalid && feeOps.hasError('required')
          ) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
      </aplz-ui-form-field>
      <aplz-ui-form-field>
        <aplz-ui-form-label>Promo fee</aplz-ui-form-label>
        <input
          aplzFormInput
          aplazoDecimalNumbers
          formControlName="promoFee"
          id="promoFee"
          type="text"
          inputmode="numeric" />
        <p
          aplzInputPrefix
          class="text-dark-tertiary mx-2 select-none pt-3 pb-2 font-semibold">
          %
        </p>
        <p aplzFormHint>Número entre 0 y 100</p>
        <ng-container aplzFormError>
          @if (
            promoFee.touched &&
            promoFee.invalid &&
            promoFee.hasError('required')
          ) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
        <ng-container aplzFormError>
          @if (
            promoFee.touched &&
            promoFee.invalid &&
            !promoFee.hasError('required') &&
            promoFee.hasError('min')
          ) {
            <p>Debe ser mayor a 0</p>
          }
        </ng-container>
        <ng-container aplzFormError>
          @if (
            promoFee.touched &&
            promoFee.invalid &&
            !promoFee.hasError('required') &&
            promoFee.hasError('max')
          ) {
            <p>Debe ser menor a 100</p>
          }
        </ng-container>
      </aplz-ui-form-field>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
      <aplz-ui-form-datepicker
        formControlName="promoFeeEndDate"
        legend="Fecha final promo"
        [minDate]="today"></aplz-ui-form-datepicker>
      <aplz-ui-form-datepicker
        formControlName="updatedAt"
        legend="Última actualización"></aplz-ui-form-datepicker>
    </div>

    @if (isRoleEnableToEdit()) {
      <div class="flex justify-center md:justify-end gap-6 flex-wrap mt-6">
        @if (!editing()) {
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="enableEdition()">
            Actualizar Información
          </button>
        } @else {
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="cancelEdition()">
            Cancelar Edición
          </button>

          <button
            aplzButton
            type="submit"
            aplzAppearance="solid"
            aplzColor="dark"
            size="md">
            Guardar Cambios
          </button>
        }
      </div>
    }
  </form>
</aplz-ui-card>
