import {
  Component,
  computed,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NotifierService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import {
  catchError,
  EMPTY,
  lastValueFrom,
  map,
  Subject,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { UserStore } from '../../../../login/application/services/user.store';
import { webpagePattern } from '../../../../shared/domain/patterns';
import { UpdateMerchantGeneralsUsecase } from '../../../application/usecases/update-merchant-generals.usecase';
import {
  MerchantGeneralInfoUI,
  MerchantGeneralUpdateUIRequest,
} from '../../../domain/dtos/general-info.dto';
import { AVERAGE_TICKET } from '../../../domain/entities/average-tickets';
import { GROSS_SALES } from '../../../domain/entities/gross-sales';
import { INDUSTRIES } from '../../../domain/entities/industries';
import { MerchantStoreService } from '../../services/merchant-store.service';

@Component({
  selector: 'app-merchant-info-settings',
  templateUrl: './info-settings.component.html',
  imports: [
    ReactiveFormsModule,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoSelectComponents,
    AplazoCardComponent,
    AplazoSelectComponents,
  ],
})
export class MerchantInfoSettingsComponent implements OnInit, OnDestroy {
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #userStore = inject(UserStore);
  readonly #notifier = inject(NotifierService);
  readonly #updateUsecase = inject(UpdateMerchantGeneralsUsecase);
  readonly #destroy = new Subject<void>();

  #editing = signal(false);
  #isRoleEnableToEdit = toSignal(
    this.#userStore.roles$.pipe(
      take(1),
      map(
        roles =>
          roles.includes('ROLE_CONTROL_TOWER_ADMIN') ||
          roles.includes('ROLE_CONTROL_TOWER_MERCHANT_OPS')
      )
    )
  );

  editing = computed(() => this.#editing());
  isRoleEnableToEdit = computed(() => this.#isRoleEnableToEdit());

  readonly industries = [...INDUSTRIES];
  readonly grossSales = [...GROSS_SALES];
  readonly averages = [...AVERAGE_TICKET];

  readonly email = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required, Validators.email],
  });
  readonly webPage = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required, Validators.pattern(webpagePattern)],
  });
  readonly address = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  readonly industry = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  readonly averageTicket = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  readonly revenue = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });

  readonly form = new FormGroup({
    email: this.email,
    webPage: this.webPage,
    address: this.address,
    industry: this.industry,
    averageTicket: this.averageTicket,
    revenue: this.revenue,
  });

  enableEdition(): void {
    this.#editing.set(true);
    this.form.enable();
  }

  async cancelEdition(): Promise<void> {
    await this.#statusRollback();
    this.#disableEdition();
  }

  async saveChanges(): Promise<void> {
    this.form.markAllAsTouched();

    if (
      this.email.errors ||
      this.webPage.errors ||
      this.address.errors ||
      this.industry.errors ||
      this.averageTicket.errors ||
      this.revenue.errors
    ) {
      this.#notifier.warning({
        title: 'Revise la información ingresada',
        message:
          'Los campos para edición no pueden estar vacíos o contener errores',
      });
      return;
    }

    const generals = await this.#lastBasics();
    const hasChanges = await this.#hasChangesToUpdate(generals);

    if (!hasChanges) {
      this.#notifier.warning({
        title: 'No hay cambios para guardar',
      });
      return;
    }

    const uiRequest: MerchantGeneralUpdateUIRequest = {
      merchantId: this.#merchantStore.selectedMerchantId,
      email: this.email.value,
      address: this.address.value,
      averageOrder: this.averageTicket.value as any,
      industry: this.industry.value as any,
      revenue: this.revenue.value as any,
      website: this.webPage.value,
    };

    await lastValueFrom(
      this.#updateUsecase.execute(uiRequest).pipe(
        take(1),
        tap(updated => {
          this.#merchantStore.setMerchantGenerals({
            ...generals,
            merchantEmail: updated?.merchant?.email ?? generals.merchantEmail,
            merchantWebsite:
              updated?.companyInformation?.website ?? generals.merchantWebsite,
            merchantAddress:
              updated?.companyInformation?.address ?? generals.merchantAddress,
            merchantIndustry:
              updated?.questionnaire?.industry ?? generals.merchantIndustry,
            merchantAov:
              updated?.questionnaire?.average_order ?? generals.merchantAov,
            merchantRevenue:
              updated?.questionnaire?.revenue ?? generals.merchantRevenue,
            merchantCategory:
              updated?.questionnaire?.category ?? generals.merchantCategory,
          });

          this.#disableEdition();
        }),
        catchError(err => {
          console.warn('error', err);

          return EMPTY;
        })
      )
    );
  }

  ngOnInit(): void {
    this.#merchantStore.merchantGenerals$
      .pipe(takeUntil(this.#destroy))
      .subscribe(generals => {
        this.#fillform(generals);

        this.form.disable();
      });
  }

  ngOnDestroy(): void {
    this.#destroy.next();
    this.#destroy.complete();
  }

  async #lastBasics(): Promise<MerchantGeneralInfoUI> {
    return await lastValueFrom(
      this.#merchantStore.merchantGenerals$.pipe(take(1))
    );
  }

  async #statusRollback(): Promise<void> {
    const generals = await this.#lastBasics();

    this.#fillform(generals);
  }

  async #hasChangesToUpdate(generals: MerchantGeneralInfoUI): Promise<boolean> {
    const current = this.form.value;

    if (
      current.email?.trim() === generals.merchantEmail?.trim() &&
      current.webPage?.trim() === generals.merchantWebsite?.trim() &&
      current.address?.trim() === generals.merchantAddress?.trim() &&
      current.industry?.trim() === generals.merchantIndustry?.trim() &&
      current.averageTicket?.trim() === generals.merchantAov?.trim() &&
      current.revenue?.trim() === generals.merchantRevenue?.trim()
    ) {
      return false;
    }

    return true;
  }

  #fillform(generals: MerchantGeneralInfoUI): void {
    this.email.setValue(generals.merchantEmail ?? '');
    this.webPage.setValue(generals.merchantWebsite ?? '');
    this.address.setValue(generals.merchantAddress ?? '');
    this.industry.setValue(generals.merchantIndustry ?? '');
    this.averageTicket.setValue(generals.merchantAov ?? '');
    this.revenue.setValue(generals.merchantRevenue ?? '');
  }

  #disableEdition(): void {
    this.#editing.set(false);
    this.form.disable();
  }
}
