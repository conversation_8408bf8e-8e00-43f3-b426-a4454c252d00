<aplz-ui-card>
  <form [formGroup]="form" class="p-4" (ngSubmit)="saveChanges()">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
      <aplz-ui-form-field>
        <aplz-ui-form-label>Email</aplz-ui-form-label>
        <input aplzFormInput formControlName="email" id="email" type="text" />
        <ng-container aplzFormError>
          @if (email.touched && email.invalid && email.hasError('required')) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
      </aplz-ui-form-field>
      <aplz-ui-form-field>
        <aplz-ui-form-label>Sitio web</aplz-ui-form-label>
        <input
          aplzFormInput
          aplazoNoWhiteSpace
          aplazoLowercase
          formControlName="webPage"
          type="text"
          inputMode="url"
          id="merchant-web-page" />
        <ng-container aplzFormError>
          @if (
            webPage.touched && webPage.invalid && webPage.hasError('required')
          ) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
        <ng-container aplzFormError>
          @if (
            webPage.touched && webPage.invalid && webPage.hasError('pattern')
          ) {
            <p>Debe ser un dominio válido. Ej. https://mi-dominio.com</p>
          }
        </ng-container>
      </aplz-ui-form-field>
    </div>

    <aplz-ui-form-field>
      <aplz-ui-form-label>Dirección</aplz-ui-form-label>
      <input
        aplzFormInput
        aplazoTrimSpaces
        formControlName="address"
        type="text"
        id="merchant-address" />

      <ng-container aplzFormError>
        @if (
          address.touched && address.invalid && address.hasError('required')
        ) {
          <p>Este campo es requerido</p>
        }
      </ng-container>
    </aplz-ui-form-field>

    <div
      class="grid grid-cols-1 md:grid-cols-2 gap-x-4"
      [class.gap-y-6]="editing()">
      @if (!editing()) {
        <aplz-ui-form-field>
          <aplz-ui-form-label>Industria</aplz-ui-form-label>
          <input
            aplzFormInput
            aplazoTrimSpaces
            formControlName="industry"
            type="text"
            id="industry" />

          <ng-container aplzFormError>
            @if (
              industry.touched &&
              industry.invalid &&
              industry.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      } @else {
        <div
          class="flex gap-2 items-center p-2 border-dark-secondary border rounded-lg">
          <span class="select-none text-sm font-light text-dark-secondary">
            Industria
          </span>

          <aplz-ui-select formControlName="industry">
            @for (item of industries; track $index) {
              <aplz-ui-option [ngValue]="item" [label]="item"></aplz-ui-option>
            }
          </aplz-ui-select>
        </div>
      }

      @if (!editing()) {
        <aplz-ui-form-field>
          <aplz-ui-form-label>Ticket promedio</aplz-ui-form-label>
          <input
            aplzFormInput
            aplazoTrimSpaces
            formControlName="averageTicket"
            type="text"
            id="averageTicket" />

          <ng-container aplzFormError>
            @if (
              averageTicket.touched &&
              averageTicket.invalid &&
              averageTicket.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      } @else {
        <div
          class="flex gap-2 items-center p-2 border-dark-secondary border rounded-lg">
          <span class="select-none text-sm font-light text-dark-secondary">
            Ticket promedio
          </span>
          <aplz-ui-select formControlName="averageTicket">
            @for (item of averages; track $index) {
              <aplz-ui-option
                [ngValue]="item.value"
                [label]="item.label"></aplz-ui-option>
            }
          </aplz-ui-select>
        </div>
      }

      @if (!editing()) {
        <aplz-ui-form-field>
          <aplz-ui-form-label>Revenue</aplz-ui-form-label>
          <input
            aplzFormInput
            aplazoTrimSpaces
            formControlName="revenue"
            type="text"
            id="revenue" />

          <ng-container aplzFormError>
            @if (
              revenue.touched && revenue.invalid && revenue.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      } @else {
        <div
          class="flex gap-2 items-center p-2 border-dark-secondary border rounded-lg">
          <span class="select-none text-sm font-light text-dark-secondary">
            Revenue
          </span>
          <aplz-ui-select formControlName="revenue">
            @for (item of grossSales; track $index) {
              <aplz-ui-option
                [ngValue]="item.value"
                [label]="item.label"></aplz-ui-option>
            }
          </aplz-ui-select>
        </div>
      }
    </div>

    @if (isRoleEnableToEdit()) {
      <div class="flex justify-center md:justify-end gap-6 flex-wrap mt-6">
        @if (!editing()) {
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="enableEdition()">
            Actualizar Información
          </button>
        } @else {
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="cancelEdition()">
            Cancelar Edición
          </button>

          <button
            aplzButton
            type="submit"
            aplzAppearance="solid"
            aplzColor="dark"
            size="md">
            Guardar Cambios
          </button>
        }
      </div>
    }
  </form>
</aplz-ui-card>
