<div class="overflow-auto max-h-96 bg-light rounded-lg shadow-md">
  @if (!hasUsers()) {
    <aplz-ui-common-message
      [i18Text]="{
        title: 'No hay Usuarios registrados',
        description: '',
      }"
      imgName="emptyLoans">
    </aplz-ui-common-message>
  } @else {
    <table aplzSimpleTable aria-label="Merchant Users Result List">
      <tr aplzSimpleTableHeaderRow>
        <th aplzSimpleTableHeaderCell scope="col"></th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Account ID
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Username
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Role
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Estatus
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Email
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Creado
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Actualizado
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Activo
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Plataforma
        </th>
      </tr>

      @for (item of users(); track item.idAccount) {
        <tr
          aplzSimpleTableBodyRow
          [striped]="selected() !== item.idAccount"
          (bodyRowClick)="openSelection(item.idAccount)">
          <td aplzSimpleTableBodyCell>
            <button
              aplzButton
              [class.rotate-180]="selected() === item.idAccount"
              class="transition-transform duration-300 ease-in-out">
              <aplz-ui-icon name="chevron-down" size="sm"></aplz-ui-icon>
            </button>
          </td>
          <td aplzSimpleTableBodyCell class="font-semibold text-center">
            {{ item.idAccount }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.username }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.role }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.userStatus }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.userEmail }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.createdAt | aplzDynamicPipe: 'date' }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.updatedAt | aplzDynamicPipe: 'date' }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.userStatus === 'Active' ? 'Si' : 'No' }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.platform }}
          </td>
        </tr>

        @if (selected() === item.idAccount) {
          <tr aplzSimpleTableBodyRow [striped]="selected() === item.idAccount">
            <td aplzSimpleTableBodyCell colspan="9">
              <div class="grid">
                @if (isRoleEnableToEdit()) {
                  <div class="w-full">
                    <h2 class="text-lg font-medium px-4 my-4">Acciones</h2>

                    <div class="flex w-full gap-4 items-center">
                      <button
                        aplzButton
                        aplzAppearance="solid"
                        aplzColor="dark"
                        size="md"
                        (click)="updateOne(item)">
                        Editar
                      </button>
                    </div>
                  </div>
                  <div class="w-full h-0.5 bg-dark-tertiary my-4"></div>
                }
                <div class="w-full">
                  <h2 class="text-lg font-medium px-4 my-4">
                    Sucursales del Usuario
                  </h2>
                  @if (!!item?.branches && item.branches!.length > 0) {
                    <div class="flex w-full gap-4 items-center">
                      @for (branch of item.branches; track branch.id) {
                        <button
                          aplzButton
                          aplzAppearance="solid"
                          aplzColor="aplazo"
                          size="sm"
                          class="cursor-text">
                          {{ branch.id }} - {{ branch.name }}
                        </button>
                      }
                    </div>
                  } @else {
                    <button
                      aplzButton
                      aplzAppearance="solid"
                      aplzColor="warning"
                      size="sm"
                      class="cursor-text">
                      Sin sucursales
                    </button>
                  }
                </div>
              </div>
            </td>
          </tr>
        }
      }
    </table>
  }
</div>
