import {
  Component,
  computed,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoCommonMessageComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { iconChevronDown, iconDocs } from '@aplazo/ui-icons';
import { lastValueFrom, map, Subject, take, takeUntil } from 'rxjs';
import { UserStore } from '../../../../login/application/services/user.store';
import { GetUsersInfoUseCase } from '../../../application/usecases/get-users-info.usecase';
import { OperatorUIDto } from '../../../domain/dtos/user.dto';
import { UpdateUserWithDialogFormService } from '../../services/edit-user-with-dialog-form.service';
import { MerchantStoreService } from '../../services/merchant-store.service';

@Component({
  selector: 'app-merchant-users-settings',
  templateUrl: './users-settings.component.html',
  imports: [
    AplazoSimpleTableComponents,
    AplazoButtonComponent,
    AplazoCommonMessageComponent,
    AplazoDynamicPipe,
    AplazoIconComponent,
  ],
})
export class MerchantUsersSettingsComponent implements OnInit, OnDestroy {
  readonly #iconRegistry = inject(AplazoIconRegistryService);
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #userStore = inject(UserStore);
  readonly #getUseCase = inject(GetUsersInfoUseCase);
  readonly #updateOperatorUseCase = inject(UpdateUserWithDialogFormService);

  readonly #destroy = new Subject<void>();

  readonly #isRoleEnableToEdit = toSignal(
    this.#userStore.roles$.pipe(
      take(1),
      map(
        roles =>
          roles.includes('ROLE_CONTROL_TOWER_ADMIN') ||
          roles.includes('ROLE_CONTROL_TOWER_MERCHANT_OPS')
      )
    )
  );

  isRoleEnableToEdit = computed(() => this.#isRoleEnableToEdit());

  readonly #selected = signal<number | null>(null);
  readonly #branchesClassModifier = signal<string>('');

  readonly selected = computed(() => this.#selected());
  readonly branchesClassModifier = computed(() =>
    this.#branchesClassModifier()
  );

  readonly #usersList = toSignal(
    this.#merchantStore.merchantUsers$.pipe(takeUntil(this.#destroy)),
    {
      initialValue: null,
    }
  );

  readonly hasUsers = computed(() => {
    const users = this.#usersList();

    return !!users && users.length > 0;
  });

  readonly users = computed(() => {
    const users = this.#usersList();

    if (!!users && users.data && Object.keys(users.data).length > 0) {
      const u = Object.values(users.data);

      u.sort((a, b) => b.idAccount - a.idAccount);

      return u;
    }

    return [];
  });

  constructor() {
    this.#iconRegistry.registerIcons([iconChevronDown, iconDocs]);
  }

  async setInfo(): Promise<void> {
    const currMId = await lastValueFrom(
      this.#merchantStore.selectedMerchantId$.pipe(take(1))
    );

    if (!currMId) {
      return;
    }

    const storedUsers = await lastValueFrom(
      this.#merchantStore.merchantUsers$.pipe(take(1))
    );

    if (storedUsers && storedUsers.merchantId === currMId) {
      return;
    }

    const result = await lastValueFrom(
      this.#getUseCase.execute(currMId).pipe(take(1))
    );

    this.#merchantStore.setUsers(result);
  }

  async updateOne(user: OperatorUIDto): Promise<void> {
    await this.#updateOperatorUseCase.execute(user);
  }

  openSelection(id: number): void {
    const current = this.selected();

    if (current === id) {
      this.#selected.set(null);
      return;
    }

    this.#selected.set(id);
  }

  ngOnInit(): void {
    this.setInfo();
  }

  ngOnDestroy(): void {
    this.#destroy.next();
    this.#destroy.complete();
  }
}
