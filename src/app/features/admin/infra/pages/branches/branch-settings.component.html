<aplz-ui-card>
  @if (branches$ | async; as branches) {
    @if (branches.data.length === 0) {
      <aplz-ui-common-message
        [i18Text]="{
          title: 'No hay Storefronts registrados',
          description: '',
        }"
        imgName="emptyLoans">
      </aplz-ui-common-message>
    } @else {
      <div class="max-h-96 overflow-y-auto">
        <table aplzSimpleTable aria-label="Merchant Branches Result List">
          <tr aplzSimpleTableHeaderRow>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              Storefront ID
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              Nombre Storefront
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              Creada
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              Actualizada
            </th>
            <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
              Activa
            </th>
            @if (isValidIntTypeToCreateStore()) {
              <th aplzSimpleTableHeaderCell scope="col">Acciones</th>
            }
          </tr>

          @for (item of branches.data; track item) {
            <tr
              aplzSimpleTableBodyRow
              [striped]="true">
              <td aplzSimpleTableBodyCell class="font-semibold text-center">
                {{ item.branchId }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.branchName }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.created | aplzDynamicPipe: 'date' }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ item.updated | aplzDynamicPipe: 'date' }}
              </td>
              <td aplzSimpleTableBodyCell class="text-center">
                {{ !item.deleted ? 'Si' : 'No' }}
              </td>
              @if (isValidIntTypeToCreateStore()) {
                <td aplzSimpleTableBodyCell class="text-center">
                  <button
                    aplzButton
                    size="md"
                    aplzAppearance="stroked"
                    aplzColor="light"
                    (click)="edit(item)">
                    <aplz-ui-icon name="edit-square" size="xs"></aplz-ui-icon>
                    <span class="ml-1"> Editar </span>
                  </button>
                </td>
              }
            </tr>
          }
        </table>
      </div>
    }
  }
</aplz-ui-card>
