import { AsyncPipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoCommonMessageComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { iconEditSquare } from '@aplazo/ui-icons';
import { lastValueFrom, map, take } from 'rxjs';
import { GetBranchesInfoUseCase } from '../../../application/usecases/get-branches-info.usecase';
import { BranchUI } from '../../../domain/dtos/branch.dto';
import { VALID_INTEGRATION_TYPES_TO_CREATE_STORE } from '../../../domain/entities/integration-types';
import { MerchantStoreService } from '../../services/merchant-store.service';
import { UpsertStorefrontWithDialogFormService } from '../../services/upsert-with-dialog-form.service';

@Component({
  selector: 'app-branch-settings',
  templateUrl: './branch-settings.component.html',
  imports: [
    AplazoCardComponent,
    AplazoSimpleTableComponents,
    AplazoButtonComponent,
    AplazoCommonMessageComponent,
    AplazoIconComponent,
    AplazoDynamicPipe,
    AsyncPipe,
  ],
})
export class MerchantBranchesSettingsComponent implements OnInit {
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #iconRegistry = inject(AplazoIconRegistryService);
  readonly #getAllUsecase = inject(GetBranchesInfoUseCase);
  readonly #upsert = inject(UpsertStorefrontWithDialogFormService);

  readonly branches$ = this.#merchantStore.merchantBranches$;
  readonly isValidIntTypeToCreateStore = toSignal(
    this.#merchantStore.merchantBasics$.pipe(
      map(d => d.intType),
      map(intType =>
        VALID_INTEGRATION_TYPES_TO_CREATE_STORE.includes(intType as any)
      )
    )
  );

  constructor() {
    this.#iconRegistry.registerIcons([iconEditSquare]);
  }

  async setInfo(): Promise<void> {
    const currMId = this.#merchantStore.selectedMerchantId;

    if (!currMId) {
      return;
    }

    const storedBranches = this.#merchantStore.getBranchesWithMerchantId();

    if (storedBranches?.merchantId === currMId) {
      return;
    }

    const result = await lastValueFrom(
      this.#getAllUsecase.execute(currMId).pipe(take(1))
    );

    this.#merchantStore.setBranches(result);
  }

  edit(branch: BranchUI): void {
    this.#upsert.execute(branch);
  }

  ngOnInit(): void {
    this.setInfo();
  }
}
