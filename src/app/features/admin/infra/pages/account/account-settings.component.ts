import {
  Component,
  computed,
  inject,
  OnD<PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NotifierService } from '@aplazo/merchant/shared';
import {
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  EMPTY,
  filter,
  lastValueFrom,
  map,
  Subject,
  take,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs';
import { UserStore } from '../../../../login/application/services/user.store';
import {
  formWithErrorsNotifierMessage,
  formWithErrorsNotifierTitle,
  formWithNoChangesTitle,
} from '../../../../shared/domain/messages-text';
import { GetAccountInfoUseCase } from '../../../application/usecases/get-account-info.usecase';
import { UpdateAccountUsecase } from '../../../application/usecases/update-account.usecase';
import {
  AccountEditionRequest,
  AccountInfoUIResponse,
} from '../../../domain/dtos/account.dto';
import { BANK_CODES, BankCode } from '../../../domain/entities/bank-code';
import { MerchantStoreService } from '../../services/merchant-store.service';

@Component({
  selector: 'app-account-settings',
  templateUrl: './account-settings.component.html',
  imports: [
    ReactiveFormsModule,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoCardComponent,
    OnlyNumbersDirective,
    AplazoTruncateLengthDirective,
  ],
})
export class MerchantAccountSettingsComponent implements OnInit, OnDestroy {
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #notifier = inject(NotifierService);
  readonly #getUseCase = inject(GetAccountInfoUseCase);
  readonly #userStore = inject(UserStore);
  readonly #updateUseCase = inject(UpdateAccountUsecase);

  readonly #destroy$ = new Subject<void>();

  readonly bankAccountMaxLength = 18;

  token = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  email = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required, Validators.email],
  });
  bank = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  bankAccount = new FormControl<string>('', {
    nonNullable: true,
    validators: [
      Validators.required,
      Validators.minLength(this.bankAccountMaxLength),
      Validators.maxLength(this.bankAccountMaxLength),
    ],
  });
  form = new FormGroup({
    token: this.token,
    email: this.email,
    bank: this.bank,
    bankAccount: this.bankAccount,
  });

  #editing = signal(false);
  #isRoleEnableToEdit = toSignal(
    this.#userStore.roles$.pipe(
      take(1),
      map(
        roles =>
          roles.includes('ROLE_CONTROL_TOWER_ADMIN') ||
          roles.includes('ROLE_CONTROL_TOWER_MERCHANT_OPS')
      )
    )
  );

  editing = computed(() => this.#editing());
  isRoleEnableToEdit = computed(() => this.#isRoleEnableToEdit());

  enableEdition(): void {
    this.#editing.set(true);
    this.bank.enable();
    this.bankAccount.enable();
  }

  async cancelEdition(): Promise<void> {
    await this.#statusRollback();
    this.#disableEdition();
  }

  async setAccountInfo(): Promise<void> {
    const currMId = await lastValueFrom(
      this.#merchantStore.selectedMerchantId$.pipe(take(1))
    );

    if (!currMId) {
      return;
    }

    try {
      const result = await lastValueFrom(
        this.#getUseCase.execute(currMId).pipe(take(1))
      );

      this.#merchantStore.setMerchantAccount(result);
      this.hydrateForm(result);
    } catch (error) {
      console.warn(error);
    }
  }

  hydrateForm(result: AccountInfoUIResponse): void {
    this.form.enable();
    this.token.setValue(result.token);
    this.email.setValue(result.email);
    this.bank.setValue(result.bank);
    this.bankAccount.setValue(result.bankAccount);
    this.form.disable();
  }

  async saveChanges(): Promise<void> {
    this.form.markAllAsTouched();

    if (this.bank.errors || this.bankAccount.errors) {
      this.#notifier.warning({
        title: formWithErrorsNotifierTitle,
        message: formWithErrorsNotifierMessage,
      });
      return;
    }

    const data = await this.#storedData();
    const hasChanges = this.#hasChangesToUpdate(data);

    if (!hasChanges) {
      this.#notifier.warning({
        title: formWithNoChangesTitle,
      });
      return;
    }

    const merchantId = await lastValueFrom(
      this.#merchantStore.selectedMerchantId$.pipe(take(1))
    );

    const uiRequest: AccountEditionRequest = {
      merchantId,
      bankName: this.bank.value,
      bankAccount: this.bankAccount.value,
    };

    await lastValueFrom(
      this.#updateUseCase.execute(uiRequest).pipe(
        take(1),
        tap(updated => {
          this.#merchantStore.setMerchantAccount({
            ...data,
            bank: updated.bankName,
            bankAccount: updated.bankAccount,
          });

          this.#disableEdition();
        }),
        catchError(err => {
          console.warn('error', err);

          return EMPTY;
        })
      )
    );
  }

  ngOnInit(): void {
    this.setAccountInfo();
    this.bankAccount.valueChanges
      .pipe(
        filter(v => v?.match(/^\d+$/) != null),
        filter(v => v?.length === 18),
        distinctUntilChanged(),
        debounceTime(450),
        withLatestFrom(
          this.#merchantStore.merchantAccount$.pipe(
            take(1),
            map(a => a?.bank ?? '')
          )
        ),
        tap(([val, bank]) => {
          const firstThree = val.slice(0, 3) as BankCode;
          const bankName = BANK_CODES[firstThree] ?? null;

          if (
            this.editing() &&
            bankName &&
            (bank.toLocaleLowerCase() != bankName.toLocaleLowerCase() ||
              !this.bank.value
                .trim()
                .toLocaleLowerCase()
                .includes(bankName.toLocaleLowerCase()))
          ) {
            this.bank.setValue(bankName);
          }
        }),
        takeUntil(this.#destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  #hasChangesToUpdate(data: { bank: string; bankAccount: string }): boolean {
    const current = this.form.value;

    if (
      current.bank === data.bank &&
      current.bankAccount === data.bankAccount
    ) {
      return false;
    }

    return true;
  }

  async #storedData(): Promise<AccountInfoUIResponse> {
    return await lastValueFrom(
      this.#merchantStore.merchantAccount$.pipe(take(1))
    );
  }

  async #statusRollback(): Promise<void> {
    const data = await this.#storedData();

    this.bank.setValue(data.bank);
    this.bankAccount.setValue(data.bankAccount);
  }

  #disableEdition(): void {
    this.#editing.set(false);
    this.bank.disable();
    this.bankAccount.disable();
  }
}
