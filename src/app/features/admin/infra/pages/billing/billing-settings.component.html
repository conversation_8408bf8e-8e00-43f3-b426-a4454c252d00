<aplz-ui-card>
  <form [formGroup]="form" class="p-4" (ngSubmit)="saveChanges()">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
      <aplz-ui-form-field>
        <aplz-ui-form-label>Correo electrónico</aplz-ui-form-label>
        <input aplzFormInput formControlName="email" id="email" type="text" />
        <ng-container aplzFormError>
          @if (email.touched && email.invalid && email.hasError('required')) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
        <ng-container aplzFormError>
          @if (
            email.touched &&
            email.invalid &&
            !email.hasError('required') &&
            email.hasError('email')
          ) {
            <p>Ingrese un correo electrónico válido</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <div class="flex items-center gap-x-2">
        <input
          id="facPubGral"
          type="checkbox"
          class="w-4 h-4 text-aplazo-aplazo bg-light border-dark-background rounded focus:ring-aplazo-aplazo focus:ring-2"
          formControlName="facPubGral" />
        <label for="facPubGral" class="font-medium text-gray-700">
          Facturación Público General
        </label>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
      <aplz-ui-form-field>
        <aplz-ui-form-label>Razón social</aplz-ui-form-label>
        <input
          aplzFormInput
          formControlName="name"
          id="name"
          type="text"
          aplazoUppercase />
        <ng-container aplzFormError>
          @if (name.touched && name.invalid && name.hasError('required')) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
      </aplz-ui-form-field>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
      <aplz-ui-form-field>
        <aplz-ui-form-label>RFC</aplz-ui-form-label>
        <input
          aplzFormInput
          formControlName="rfc"
          id="rfc"
          type="text"
          aplazoUppercase
          aplazoLettersNumbers
          aplazoTrimSpaces
          [aplazoTruncateLength]="13" />
        <ng-container aplzFormError>
          @if (rfc.touched && rfc.invalid && rfc.hasError('required')) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
        <ng-container aplzFormError>
          @if (
            rfc.touched &&
            rfc.invalid &&
            !rfc.hasError('required') &&
            rfc.hasError(invalidRFCErrorKey)
          ) {
            <p>Revise el RFC ingresado</p>
          }
        </ng-container>
      </aplz-ui-form-field>
      <aplz-ui-form-field>
        <aplz-ui-form-label>Código postal receptor</aplz-ui-form-label>
        <input
          aplzFormInput
          formControlName="zipCode"
          id="zipCode"
          type="text"
          aplazoOnlyNumbers
          [aplazoTruncateLength]="5" />
        <ng-container aplzFormError>
          @if (
            zipCode.touched && zipCode.invalid && zipCode.hasError('required')
          ) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
        <ng-container aplzFormError>
          @if (
            zipCode.touched &&
            zipCode.invalid &&
            !zipCode.hasError('required') &&
            (zipCode.hasError('minlength') || zipCode.hasError('maxlength'))
          ) {
            <p>El código postal debe ser de 5 digitos</p>
          }
        </ng-container>
      </aplz-ui-form-field>
    </div>

    <div
      class="grid grid-cols-1 md:grid-cols-2 gap-x-4"
      [class.mb-6]="editing()"
      [class.gap-y-6]="editing()">
      @if (!editing() || isFacPubGral()) {
        <aplz-ui-form-field>
          <aplz-ui-form-label>Forma de pago</aplz-ui-form-label>
          <input
            aplzFormInput
            formControlName="paymentForm"
            id="paymentForm"
            type="text" />
          <ng-container aplzFormError>
            @if (
              paymentForm.touched &&
              paymentForm.invalid &&
              paymentForm.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      } @else {
        <div
          class="flex gap-2 items-center p-2 border-dark-secondary border rounded-lg">
          <span class="select-none text-sm font-light text-dark-secondary">
            Forma de pago
          </span>
          <aplz-ui-select formControlName="paymentForm">
            @for (item of paymentFormOptions; track $index) {
              <aplz-ui-option [ngValue]="item" [label]="item"></aplz-ui-option>
            }
          </aplz-ui-select>
        </div>
      }

      @if (!editing() || isFacPubGral()) {
        <aplz-ui-form-field>
          <aplz-ui-form-label>CFDI</aplz-ui-form-label>
          <input aplzFormInput formControlName="cfdi" id="cfdi" type="text" />
          <ng-container aplzFormError>
            @if (cfdi.touched && cfdi.invalid && cfdi.hasError('required')) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      } @else {
        <div
          class="flex gap-2 items-center p-2 border-dark-secondary border rounded-lg">
          <span class="select-none text-sm font-light text-dark-secondary">
            CFDI
          </span>
          <aplz-ui-select formControlName="cfdi">
            @for (item of cfdiOptions; track $index) {
              <aplz-ui-option [ngValue]="item" [label]="item"></aplz-ui-option>
            }
          </aplz-ui-select>
        </div>
      }
    </div>

    <div
      class="grid grid-cols-1 md:grid-cols-2 gap-x-4"
      [class.mb-6]="editing()"
      [class.gap-y-6]="editing()">
      @if (!editing() || isFacPubGral()) {
        <aplz-ui-form-field>
          <aplz-ui-form-label>Método de pago</aplz-ui-form-label>
          <input
            aplzFormInput
            formControlName="paymentMethod"
            id="paymentMethod"
            type="text" />
          <ng-container aplzFormError>
            @if (
              paymentMethod.touched &&
              paymentMethod.invalid &&
              paymentMethod.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      } @else {
        <div
          class="flex gap-2 items-center p-2 border-dark-secondary border rounded-lg">
          <span class="select-none text-sm font-light text-dark-secondary">
            Método de pago
          </span>
          <aplz-ui-select formControlName="paymentMethod">
            @for (item of paymentMethodOptions; track $index) {
              <aplz-ui-option [ngValue]="item" [label]="item"></aplz-ui-option>
            }
          </aplz-ui-select>
        </div>
      }

      @if (!editing() || isFacPubGral()) {
        <aplz-ui-form-field>
          <aplz-ui-form-label>Régimen fiscal receptor</aplz-ui-form-label>
          <input
            aplzFormInput
            formControlName="invoiceRegime"
            id="invoiceRegime"
            type="text" />
          <ng-container aplzFormError>
            @if (
              invoiceRegime.touched &&
              invoiceRegime.invalid &&
              invoiceRegime.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      } @else {
        <div
          class="flex gap-2 items-center p-2 border-dark-secondary border rounded-lg">
          <span class="select-none text-sm font-light text-dark-secondary">
            Régimen fiscal receptor
          </span>
          <aplz-ui-select formControlName="invoiceRegime">
            @for (item of invoiceRegimeOptions; track $index) {
              <aplz-ui-option [ngValue]="item" [label]="item"></aplz-ui-option>
            }
          </aplz-ui-select>
        </div>
      }
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
      <aplz-ui-form-field>
        <aplz-ui-form-label>Moneda</aplz-ui-form-label>
        <input
          aplzFormInput
          formControlName="currency"
          id="currency"
          type="text" />
        <ng-container aplzFormError>
          @if (
            currency.touched &&
            currency.invalid &&
            currency.hasError('required')
          ) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
      </aplz-ui-form-field>
    </div>

    @if (isRoleEnableToEdit()) {
      <div class="flex justify-center md:justify-end gap-6 flex-wrap mt-6">
        @if (!editing()) {
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="enableEdition()">
            Actualizar Información
          </button>
        } @else {
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="cancelEdition()">
            Cancelar Edición
          </button>

          <button
            aplzButton
            type="submit"
            aplzAppearance="solid"
            aplzColor="dark"
            size="md">
            Guardar Cambios
          </button>
        }
      </div>
    }
  </form>
</aplz-ui-card>
