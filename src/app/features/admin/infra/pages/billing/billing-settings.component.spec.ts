import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { MerchantBillingSettingsComponent } from './billing-settings.component';
import { ReactiveFormsModule } from '@angular/forms';
import { NotifierService, RFC } from '@aplazo/merchant/shared';
import { UserStore } from '../../../../login/application/services/user.store';
import { GetInvoiceInfoUseCase } from '../../../application/usecases/get-invoice-info.usecase';
import { UpdateInvoiceInfoUsecase } from '../../../application/usecases/update-invoice-info.usecase';
import { MerchantStoreService } from '../../services/merchant-store.service';
import { BehaviorSubject, EMPTY, of, throwError } from 'rxjs';
import {
  InvoiceUI,
  PaymentMethodLabel,
  InvoiceRegimeLabel,
  PaymentFormLabel,
  CfdiLabel,
  PaymentFormCatalogId,
  CfdiCatalogId,
} from '../../../domain/dtos/invoice.dto';
import {
  formWithErrorsNotifierMessage,
  formWithErrorsNotifierTitle,
  formWithNoChangesTitle,
} from '../../../../shared/domain/messages-text';

describe('MerchantBillingSettingsComponent', () => {
  let component: MerchantBillingSettingsComponent;
  let fixture: ComponentFixture<MerchantBillingSettingsComponent>;
  let notifierService: jasmine.SpyObj<NotifierService>;
  let userStore: jasmine.SpyObj<UserStore>;
  let getInvoiceInfoUseCase: jasmine.SpyObj<GetInvoiceInfoUseCase>;
  let updateInvoiceInfoUsecase: jasmine.SpyObj<UpdateInvoiceInfoUsecase>;
  let merchantStore: jasmine.SpyObj<MerchantStoreService>;
  let rolesSubject: BehaviorSubject<string[]>;
  let consoleWarnSpy: jasmine.Spy;

  const mockInvoiceData: InvoiceUI = {
    id: 1,
    merchantId: 1,
    currency: 'MXN',
    email: '<EMAIL>',
    paymentMethod: 'Pago en Una Exhibición (PUE)' as PaymentMethodLabel,
    zipCode: '12345',
    invoiceRegime:
      '601 - General de Ley Personas Morales' as InvoiceRegimeLabel,
    rfc: 'XAXX010101000',
    name: 'Test Company',
    paymentForm: '03: Transferencia electrónica de fondos' as PaymentFormLabel,
    cfdi: 'G03: Gastos en General' as CfdiLabel,
    facPubGral: false,
    paymentFormCode: 3 as PaymentFormCatalogId,
    cfdiCode: 15 as CfdiCatalogId,
  };

  beforeEach(async () => {
    consoleWarnSpy = spyOn(console, 'warn');
    notifierService = jasmine.createSpyObj('NotifierService', ['warning']);
    rolesSubject = new BehaviorSubject(['ROLE_CONTROL_TOWER_ADMIN']);
    userStore = jasmine.createSpyObj('UserStore', [], {
      roles$: rolesSubject,
    });
    getInvoiceInfoUseCase = jasmine.createSpyObj('GetInvoiceInfoUseCase', [
      'execute',
    ]);
    updateInvoiceInfoUsecase = jasmine.createSpyObj(
      'UpdateInvoiceInfoUsecase',
      ['execute']
    );
    merchantStore = jasmine.createSpyObj(
      'MerchantStoreService',
      ['setMerchantInvoice'],
      {
        selectedMerchantId$: new BehaviorSubject(1),
        merchantInvoice$: new BehaviorSubject(mockInvoiceData),
      }
    );

    getInvoiceInfoUseCase.execute.and.returnValue(of(mockInvoiceData));
    updateInvoiceInfoUsecase.execute.and.returnValue(of(mockInvoiceData));

    await TestBed.configureTestingModule({
      imports: [MerchantBillingSettingsComponent, ReactiveFormsModule],
      providers: [
        { provide: NotifierService, useValue: notifierService },
        { provide: UserStore, useValue: userStore },
        { provide: GetInvoiceInfoUseCase, useValue: getInvoiceInfoUseCase },
        {
          provide: UpdateInvoiceInfoUsecase,
          useValue: updateInvoiceInfoUsecase,
        },
        { provide: MerchantStoreService, useValue: merchantStore },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MerchantBillingSettingsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('initialization', () => {
    it('should load invoice data on init', fakeAsync(() => {
      component.ngOnInit();
      tick();

      expect(getInvoiceInfoUseCase.execute).toHaveBeenCalledWith(1);
      expect(merchantStore.setMerchantInvoice).toHaveBeenCalledWith(
        mockInvoiceData
      );
      expect(component.form.getRawValue()).toEqual({
        currency: 'MXN',
        email: '<EMAIL>',
        paymentMethod: 'Pago en Una Exhibición (PUE)',
        zipCode: '12345',
        invoiceRegime: '601 - General de Ley Personas Morales',
        rfc: 'XAXX010101000',
        name: 'Test Company',
        paymentForm: '03: Transferencia electrónica de fondos',
        cfdi: 'G03: Gastos en General',
        facPubGral: false,
      });
    }));

    it('should handle error when loading invoice data', fakeAsync(() => {
      getInvoiceInfoUseCase.execute.and.returnValue(
        throwError(() => new Error('Test error'))
      );

      component.ngOnInit();
      tick();

      expect(component.form.getRawValue()).toEqual({
        currency: 'MXN',
        email: '<EMAIL>',
        paymentMethod: 'Pago en Una Exhibición (PUE)',
        zipCode: '12345',
        invoiceRegime: '601 - General de Ley Personas Morales',
        rfc: 'XAXX010101000',
        name: 'Test Company',
        paymentForm: '03: Transferencia electrónica de fondos',
        cfdi: 'G03: Gastos en General',
        facPubGral: false,
      });
    }));
  });

  describe('form validation', () => {
    beforeEach(() => {
      component.enableEdition();
      fixture.detectChanges();
    });

    it('should validate required fields', () => {
      component.form.patchValue({
        email: '',
        rfc: '',
        name: '',
        zipCode: '',
        invoiceRegime: '',
        currency: '',
        paymentForm: '',
        paymentMethod: '',
        cfdi: '',
      });

      expect(component.form.valid).toBeFalse();
      expect(component.email.errors?.['required']).toBeTruthy();
      expect(component.rfc.errors?.['required']).toBeTruthy();
      expect(component.name.errors?.['required']).toBeTruthy();
      expect(component.zipCode.errors?.['required']).toBeTruthy();
      expect(component.invoiceRegime.errors?.['required']).toBeTruthy();
      expect(component.currency.errors?.['required']).toBeTruthy();
      expect(component.paymentForm.errors?.['required']).toBeTruthy();
      expect(component.paymentMethod.errors?.['required']).toBeTruthy();
      expect(component.cfdi.errors?.['required']).toBeTruthy();
    });

    it('should validate email format', () => {
      component.email.setValue('invalid-email');
      expect(component.email.errors?.['email']).toBeTruthy();

      component.email.setValue('<EMAIL>');
      expect(component.email.errors).toBeNull();
    });

    it('should validate RFC format', () => {
      component.rfc.setValue('INVALID');
      expect(component.rfc.errors?.['invalidRfc']).toBeTruthy();

      component.rfc.setValue('XAXX010101000');
      expect(component.rfc.errors).toBeNull();
    });

    it('should validate zip code length', () => {
      component.zipCode.setValue('123');
      expect(component.zipCode.errors?.['minlength']).toBeTruthy();

      component.zipCode.setValue('123456');
      expect(component.zipCode.errors?.['maxlength']).toBeTruthy();

      component.zipCode.setValue('12345');
      expect(component.zipCode.errors).toBeNull();
    });
  });

  describe('form editing', () => {
    it('should enable form editing', () => {
      component.enableEdition();
      expect(component.editing()).toBeTrue();
      expect(component.form.enabled).toBeTrue();
    });

    it('should disable form editing', fakeAsync(() => {
      component.enableEdition();
      component.cancelEdition();
      tick();

      expect(component.editing()).toBeFalse();
      expect(component.form.disabled).toBeTrue();
    }));

    it('should handle facPubGral changes', () => {
      component.enableEdition();
      component.form.get('facPubGral')?.setValue(true);

      expect(component.isFacPubGral()).toBeTrue();
      expect(component.name.disabled).toBeTrue();
      expect(component.rfc.disabled).toBeTrue();
      expect(component.email.disabled).toBeTrue();
      expect(component.zipCode.disabled).toBeTrue();
      expect(component.invoiceRegime.disabled).toBeTrue();
      expect(component.paymentForm.disabled).toBeTrue();
      expect(component.cfdi.disabled).toBeTrue();
      expect(component.paymentMethod.disabled).toBeTrue();
      expect(component.currency.disabled).toBeTrue();

      component.form.get('facPubGral')?.setValue(false);
      expect(component.isFacPubGral()).toBeFalse();
      expect(component.name.enabled).toBeTrue();
      expect(component.rfc.enabled).toBeTrue();
      expect(component.email.enabled).toBeTrue();
      expect(component.zipCode.enabled).toBeTrue();
      expect(component.invoiceRegime.enabled).toBeTrue();
      expect(component.paymentForm.enabled).toBeTrue();
      expect(component.cfdi.enabled).toBeTrue();
      expect(component.paymentMethod.enabled).toBeTrue();
      expect(component.currency.enabled).toBeTrue();
    });
  });

  describe('saving changes', () => {
    beforeEach(() => {
      component.enableEdition();
      // Asegurarse de que el formulario tenga datos válidos inicialmente
      component.form.patchValue(mockInvoiceData);
      fixture.detectChanges();
    });

    it('should show warning when form has errors', fakeAsync(() => {
      component.email.setValue('invalid-email');
      component.saveChanges();
      tick();

      expect(notifierService.warning).toHaveBeenCalledWith({
        title: formWithErrorsNotifierTitle,
        message: formWithErrorsNotifierMessage,
      });
      expect(updateInvoiceInfoUsecase.execute).not.toHaveBeenCalled();
    }));

    it('should show warning when no changes were made', fakeAsync(() => {
      // No hacemos cambios al formulario, solo intentamos guardar
      component.saveChanges();
      tick();

      expect(notifierService.warning).toHaveBeenCalledWith({
        title: formWithNoChangesTitle,
      });
      expect(updateInvoiceInfoUsecase.execute).not.toHaveBeenCalled();
    }));

    it('should update invoice info when form is valid and has changes', fakeAsync(() => {
      const updatedData = {
        ...mockInvoiceData,
        name: 'Updated Company',
      };

      // Primero aseguramos que el formulario esté válido
      component.form.patchValue(mockInvoiceData);
      // Luego hacemos el cambio
      component.name.setValue('Updated Company');
      updateInvoiceInfoUsecase.execute.and.returnValue(of(updatedData));

      component.saveChanges();
      tick();

      expect(updateInvoiceInfoUsecase.execute).toHaveBeenCalled();
      expect(merchantStore.setMerchantInvoice).toHaveBeenCalledWith(
        updatedData
      );
      expect(component.editing()).toBeFalse();
    }));

    it('should handle error when updating invoice info', fakeAsync(() => {
      // Asegurarse de que el formulario tenga cambios
      component.name.setValue('Updated Company');
      
      // Simular el error
      updateInvoiceInfoUsecase.execute.and.returnValue(
        throwError(() => new Error('Test error'))
      );

      component.saveChanges();
      tick();

      expect(consoleWarnSpy).toHaveBeenCalled();
      expect(component.editing()).toBeFalse();
      expect(merchantStore.setMerchantInvoice).not.toHaveBeenCalled();
    }));
  });

  describe('role-based access', () => {
    it('should allow editing for admin role', () => {
      expect(component.isRoleEnableToEdit()).toBeTrue();
    });

    it('should allow editing for merchant ops role', fakeAsync(() => {
      rolesSubject.next(['ROLE_CONTROL_TOWER_MERCHANT_OPS']);
      
      // Recrear el componente para que tome el nuevo valor del observable
      fixture = TestBed.createComponent(MerchantBillingSettingsComponent);
      component = fixture.componentInstance;
      component.ngOnInit();
      tick();
      fixture.detectChanges();

      expect(component.isRoleEnableToEdit()).toBeTrue();
    }));

    it('should not allow editing for other roles', fakeAsync(() => {
      rolesSubject.next(['ROLE_OTHER']);
      
      // Recrear el componente para que tome el nuevo valor del observable
      fixture = TestBed.createComponent(MerchantBillingSettingsComponent);
      component = fixture.componentInstance;
      component.ngOnInit();
      tick();
      fixture.detectChanges();

      expect(component.isRoleEnableToEdit()).toBeFalse();
    }));
  });
});