import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { NotifierService, RFC } from '@aplazo/merchant/shared';
import {
  AplazoLettersNumbersDirective,
  AplazoTrimSpacesDirective,
  AplazoTruncateLengthDirective,
  AplazoUppercaseDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import { AplazoCommonMessageComponents } from '@aplazo/shared-ui/merchant';
import {
  catchError,
  EMPTY,
  finalize,
  lastValueFrom,
  map,
  of,
  take,
  tap,
} from 'rxjs';
import { UserStore } from '../../../../login/application/services/user.store';
import {
  formWithErrorsNotifierMessage,
  formWithErrorsNotifierTitle,
  formWithNoChangesTitle,
} from '../../../../shared/domain/messages-text';
import { GetInvoiceInfoUseCase } from '../../../application/usecases/get-invoice-info.usecase';
import { UpdateInvoiceInfoUsecase } from '../../../application/usecases/update-invoice-info.usecase';
import {
  CFDI,
  INVOICE_REGIME,
  InvoiceUI,
  PAYMENT_FORM,
  PAYMENT_METHOD,
} from '../../../domain/dtos/invoice.dto';
import { MerchantStoreService } from '../../services/merchant-store.service';

export const invalidRfcErrorKey = 'invalidRfc';

export function validateRfc(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value ?? '';

    try {
      const rfc = RFC.create(value);

      if (rfc.value) {
        return null;
      }

      return { [invalidRfcErrorKey]: true };
    } catch (error) {
      return { [invalidRfcErrorKey]: true };
    }
  };
}

@Component({
  selector: 'app-merchant-billing-settings',
  templateUrl: './billing-settings.component.html',
  imports: [
    ReactiveFormsModule,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoCardComponent,
    AplazoCommonMessageComponents,
    AplazoTrimSpacesDirective,
    OnlyNumbersDirective,
    AplazoLettersNumbersDirective,
    AplazoUppercaseDirective,
    AplazoTruncateLengthDirective,
    AplazoSelectComponents,
  ],
})
export class MerchantBillingSettingsComponent implements OnInit {
  readonly #merchantStore = inject(MerchantStoreService);
  readonly #userStore = inject(UserStore);
  readonly #notifier = inject(NotifierService);
  readonly #getUseCase = inject(GetInvoiceInfoUseCase);
  readonly #updateUseCase = inject(UpdateInvoiceInfoUsecase);

  readonly invalidRFCErrorKey = invalidRfcErrorKey;

  readonly #editing = signal(false);
  readonly #isFacPubGral = signal(false);
  readonly #isRoleEnableToEdit = toSignal(
    this.#userStore.roles$.pipe(
      take(1),
      map(
        roles =>
          roles.includes('ROLE_CONTROL_TOWER_ADMIN') ||
          roles.includes('ROLE_CONTROL_TOWER_MERCHANT_OPS')
      )
    )
  );

  readonly cfdiOptions = Object.keys(CFDI);
  readonly paymentFormOptions = Object.keys(PAYMENT_FORM);
  readonly invoiceRegimeOptions = Object.keys(INVOICE_REGIME);
  readonly paymentMethodOptions = Object.keys(PAYMENT_METHOD);

  readonly editing = computed(() => this.#editing());
  readonly isFacPubGral = computed(() => this.#isFacPubGral());
  readonly isRoleEnableToEdit = computed(() => this.#isRoleEnableToEdit());

  readonly currency = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  readonly email = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required, Validators.email],
  });
  readonly paymentMethod = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  readonly zipCode = new FormControl<string>('', {
    nonNullable: true,
    validators: [
      Validators.required,
      Validators.minLength(5),
      Validators.maxLength(5),
    ],
  });
  readonly invoiceRegime = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  readonly rfc = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required, validateRfc()],
  });
  readonly name = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  readonly paymentForm = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  readonly cfdi = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });

  readonly facPubGral = new FormControl<boolean>(false, {
    nonNullable: true,
  });

  readonly form = new FormGroup({
    currency: this.currency,
    email: this.email,
    paymentMethod: this.paymentMethod,
    zipCode: this.zipCode,
    invoiceRegime: this.invoiceRegime,
    rfc: this.rfc,
    name: this.name,
    paymentForm: this.paymentForm,
    cfdi: this.cfdi,
    facPubGral: this.facPubGral,
  });

  private setupFacPubGralListener(): void {
    this.facPubGral.valueChanges.subscribe(isChecked => {
      if (isChecked) {
        this.disableInvoiceFields();
        this.#isFacPubGral.set(true);
      } else {
        this.enableInvoiceFields();
        this.#isFacPubGral.set(false);
      }
    });
  }

  private disableInvoiceFields(): void {
    const fieldsToDisable = [
      'name',
      'rfc',
      'email',
      'zipCode',
      'invoiceRegime',
      'paymentForm',
      'cfdi',
      'paymentMethod',
      'currency',
    ];

    fieldsToDisable.forEach(fieldName => {
      const control = this.form.get(fieldName);
      if (control) {
        control.disable({ emitEvent: false });
      }
    });
  }

  private enableInvoiceFields(): void {
    if (this.editing()) {
      this.name.enable();
      this.rfc.enable();
      this.email.enable();
      this.zipCode.enable();
      this.invoiceRegime.enable();
      this.paymentForm.enable();
      this.cfdi.enable();
      this.paymentMethod.enable();
      this.currency.enable();
    }
  }

  enableEdition(): void {
    this.name.enable();
    this.rfc.enable();
    this.email.enable();
    this.zipCode.enable();
    this.invoiceRegime.enable();
    this.paymentForm.enable();
    this.cfdi.enable();
    this.paymentMethod.enable();

    this.facPubGral.enable();

    this.#editing.set(true);

    if (this.facPubGral.value) {
      this.disableInvoiceFields();
    }
  }

  async cancelEdition(): Promise<void> {
    await this.#rollback();
    this.#disableEdition();
  }

  async setInfo(): Promise<void> {
    const currMId = await lastValueFrom(
      this.#merchantStore.selectedMerchantId$.pipe(take(1))
    );

    const storedInvoice = await this.#storedData();

    this.#getUseCase
      .execute(currMId)
      .pipe(
        take(1),
        tap(result => {
          this.#merchantStore.setMerchantInvoice(result);
          this.hydrateInfo(result);
        }),
        catchError(() => {
          this.hydrateInfo(storedInvoice);

          return of(null);
        }),
        finalize(() => {
          this.#disableEdition();
        })
      )
      .subscribe();
  }

  hydrateInfo(result: InvoiceUI): void {
    this.form.enable();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { paymentFormCode, cfdiCode, facPubGral, ...data } = result;
    const formData = {
      ...data,
      facPubGral: facPubGral,
    };

    this.form.patchValue(formData);

    this.form.disable();

    if (facPubGral) {
      this.disableInvoiceFields();
    }

    if (!this.editing()) {
      this.form.disable();
    }
  }

  async saveChanges(): Promise<void> {
    this.form.enable();
    this.form.markAllAsTouched();

    if (
      this.name.errors ||
      this.rfc.errors ||
      this.email.errors ||
      this.zipCode.errors ||
      this.invoiceRegime.errors ||
      this.currency.errors ||
      this.paymentForm.errors
    ) {
      this.#notifier.warning({
        title: formWithErrorsNotifierTitle,
        message: formWithErrorsNotifierMessage,
      });
      this.#disableEdition();
      return;
    }

    const data = await this.#storedData();
    const hasChanges = await this.#hasChangesToUpdate(data);

    if (!hasChanges) {
      this.#notifier.warning({
        title: formWithNoChangesTitle,
      });
      this.#disableEdition();
      return;
    }

    const merchantId = await lastValueFrom(
      this.#merchantStore.selectedMerchantId$.pipe(take(1))
    );

    const uiRequest = {
      ...data,
      ...this.form.value,
      merchantId,
    };

    await lastValueFrom(
      this.#updateUseCase.execute(uiRequest as InvoiceUI).pipe(
        take(1),
        tap(updated => {
          this.#merchantStore.setMerchantInvoice(updated);
        }),
        catchError(err => {
          console.warn('error', err);

          return EMPTY;
        }),

        finalize(() => {
          this.#disableEdition();
        })
      )
    );
  }

  ngOnInit(): void {
    this.setInfo();
    this.setupFacPubGralListener();
  }

  async #hasChangesToUpdate(data: Partial<InvoiceUI>): Promise<boolean> {
    const current = this.form.value;

    if (current.facPubGral !== data.facPubGral) {
      return true;
    }

    return (
      current.name !== data.name ||
      current.rfc !== data.rfc ||
      current.email !== data.email ||
      current.zipCode !== data.zipCode ||
      current.invoiceRegime !== data.invoiceRegime ||
      current.currency !== data.currency ||
      current.paymentForm !== data.paymentForm ||
      current.paymentMethod !== data.paymentMethod ||
      current.cfdi !== data.cfdi ||
      current.facPubGral !== data.facPubGral
    );
  }

  async #storedData(): Promise<InvoiceUI> {
    return await lastValueFrom(
      this.#merchantStore.merchantInvoice$.pipe(take(1))
    );
  }

  async #rollback(): Promise<void> {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const data = await this.#storedData();

    this.hydrateInfo(data);
  }

  #disableEdition(): void {
    this.#editing.set(false);
    this.form.disable();
  }
}
