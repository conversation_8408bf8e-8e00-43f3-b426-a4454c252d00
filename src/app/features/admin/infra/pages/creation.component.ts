import { Component, computed, inject, signal } from '@angular/core';
import {
  AbstractControl,
  FormGroup,
  ReactiveFormsModule,
} from '@angular/forms';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoTabsComponents } from '@aplazo/shared-ui/tabs';
import { DialogService } from '@ngneat/dialog';
import { EMPTY, switchMap, take, tap } from 'rxjs';
import { NewMerchantUseCase } from '../../application/usecases/new-merchant.usecase';
import { Prospect } from '../../domain/entities/prospect';
import { BusinessMetricsComponent } from '../components/business-metrics.component';
import { ConfirmProspectComponent } from '../components/confirm-prospect.component';
import { InfoMerchantComponent } from '../components/info-merchant.component';
import { LegalRepresentativeComponent } from '../components/legal-representative.component';

export type FormType<T> = {
  [Properties in keyof T]: AbstractControl<T[Properties]>;
};

@Component({
  selector: 'app-merchant-creation',
  imports: [
    AplazoTabsComponents,
    AplazoFormFieldDirectives,
    AplazoCardComponent,
    ReactiveFormsModule,
    InfoMerchantComponent,
    LegalRepresentativeComponent,
    BusinessMetricsComponent,
  ],
  template: `
    <section class="px-4 md:px-8 py-4 md:py-8">
      <aplz-ui-card>
        <form [formGroup]="form" (ngSubmit)="finish()">
          <aplz-ui-tab-group
            [selectedIndex]="step()"
            (tabSelectionChange)="changeTab($event)">
            <aplz-ui-tab label="Comercio">
              <aplz-ui-tab-body>
                <app-merchant-info (nextStep)="nextStep()"></app-merchant-info>
              </aplz-ui-tab-body>
            </aplz-ui-tab>

            <aplz-ui-tab label="Representante" [disabled]="maxStep() < 1">
              <aplz-ui-tab-body>
                <app-legal-representative
                  (nextStep)="nextStep()"
                  (backStep)="prevStep()"></app-legal-representative>
              </aplz-ui-tab-body>
            </aplz-ui-tab>

            <aplz-ui-tab label="Comerciales" [disabled]="maxStep() < 2">
              <aplz-ui-tab-body>
                <app-business-metrics
                  (nextStep)="finish()"
                  (backStep)="prevStep()"></app-business-metrics>
              </aplz-ui-tab-body>
            </aplz-ui-tab>
          </aplz-ui-tab-group>
        </form>
      </aplz-ui-card>
    </section>
  `,
})
export class MerchantCreationComponent {
  readonly #dialog = inject(DialogService);
  readonly #newMerchantUseCase = inject(NewMerchantUseCase);

  form = new FormGroup<FormType<Prospect>>({} as FormType<Prospect>);
  readonly #currentStep = signal(0);
  readonly #maxStep = signal<boolean[]>([false]);
  readonly #merchantCreated = signal(false);

  readonly step = computed(() => this.#currentStep());
  readonly maxStep = computed(() => this.#maxStep().findIndex(i => !i));
  readonly merchantCreated = computed(() => this.#merchantCreated());

  finish(): void {
    this.#dialog
      .open(ConfirmProspectComponent, {
        data: this.form.value,
        enableClose: false,
        size: 'md',
      })
      .afterClosed$.pipe(
        switchMap(result => {
          if (!result?.confirm) {
            return EMPTY;
          }

          return this.#newMerchantUseCase.execute(this.form.value as Prospect);
        }),
        tap(() => {
          this.#merchantCreated.set(true);
        }),
        take(1)
      )
      .subscribe(() => {
        this.#reset();
      });
  }

  changeTab({ index }: { index: number }) {
    this.#currentStep.set(index);
  }

  nextStep(): void {
    this.#maxStep.update(prev => {
      const updated = [...prev];
      updated[this.#currentStep()] = true;

      if (!updated[this.#currentStep() + 1]) {
        updated.push(false);
      }

      return updated;
    });
    this.#currentStep.update(prev => prev + 1);
  }

  prevStep(): void {
    this.#currentStep.update(prev => prev - 1);
  }

  #reset = () => {
    this.form.controls['business-metrics'].reset();
    this.form.controls['legal-representative'].reset();
    this.form.controls['general-info'].reset();
    this.#currentStep.set(0);
    this.#maxStep.set([false]);
  };
}
