import {
  ChangeDetectionStrategy,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  output,
  ViewEncapsulation,
} from '@angular/core';
import {
  ControlContainer,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Aplazo<PERSON>uttonComponent } from '@aplazo/shared-ui/button';
import { AplazoButtonGroupComponent } from '@aplazo/shared-ui/button-group';
import { AplazoSelectComponents } from '@aplazo/shared-ui/forms';
import { AVERAGE_TICKET } from '../../domain/entities/average-tickets';
import { GROSS_SALES } from '../../domain/entities/gross-sales';
import { INDUSTRIES } from '../../domain/entities/industries';
import { INTEGRATION_TYPES } from '../../domain/entities/integration-types';

@Component({
  selector: 'app-business-metrics',
  imports: [
    ReactiveFormsModule,
    AplazoButtonComponent,
    AplazoButtonGroupComponent,
    AplazoSelectComponents,
  ],
  template: `
    <article [formGroupName]="controlKey" class="py-4 px-3">
      <h1 class="text-xl mb-6">Métricas del Negocio</h1>

      <div class="mt-4 mb-6">
        <h2 class="text-lg font-medium mb-4">Industria</h2>

        <div>
          <aplz-ui-select formControlName="industry">
            @for (opt of industries; track opt) {
              <aplz-ui-option [ngValue]="opt" [label]="opt"> </aplz-ui-option>
            }
          </aplz-ui-select>
          @if (
            industry.touched && industry.invalid && industry.hasError('pattern')
          ) {
            <p
              class="text-sm font-medium text-special-danger -mt-6"
              id="industry-required-error">
              Seleccione una industria
            </p>
          }
        </div>
      </div>

      <div class="mt-4 mb-10">
        <h2 class="text-lg font-medium mb-4">Ticket promedio</h2>

        <aplz-ui-button-group
          [buttons]="averages"
          formControlName="averageTicket">
        </aplz-ui-button-group>
      </div>

      <div class="mt-4 mb-10">
        <h2 class="text-lg font-medium mb-4">Ingresos Anuales</h2>

        <aplz-ui-button-group
          [buttons]="grossSales"
          formControlName="salesVolume">
        </aplz-ui-button-group>
      </div>

      <div class="mt-4 mb-10">
        <h2 class="text-lg font-medium mb-4">Tipo de integración</h2>

        <div>
          <aplz-ui-select formControlName="integrationType">
            @for (opt of intTypes; track opt) {
              <aplz-ui-option [ngValue]="opt.value" [label]="opt.label">
              </aplz-ui-option>
            }
          </aplz-ui-select>

          @if (
            integrationType.touched &&
            integrationType.invalid &&
            integrationType.hasError('pattern')
          ) {
            <p class="text-sm font-medium text-special-danger -mt-6">
              Seleccione un tipo de integración
            </p>
          }
        </div>
      </div>

      <div class="flex justify-end gap-x-6 flex-wrap">
        <button
          aplzButton
          type="button"
          aplzAppearance="stroked"
          aplzColor="light"
          size="md"
          (click)="back()">
          Anterior
        </button>
        <button
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          type="button"
          (click)="next()">
          Finalizar
        </button>
      </div>
    </article>
  `,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  viewProviders: [
    {
      provide: ControlContainer,
      useFactory: () => inject(ControlContainer, { skipSelf: true }),
    },
  ],
})
export class BusinessMetricsComponent implements OnInit, OnDestroy {
  readonly #parentContainer = inject(ControlContainer);
  readonly controlKey = 'business-metrics';
  readonly industries = ['Seleccione una Industria', ...INDUSTRIES];
  readonly grossSales = [...GROSS_SALES];
  readonly averages = [...AVERAGE_TICKET];
  readonly intTypes = [
    { label: 'Seleccione una opción', value: 'Seleccione una opción' },
    ...INTEGRATION_TYPES,
  ];

  readonly industry = new FormControl(this.industries[0], [
    Validators.required,
    Validators.pattern('^(?!Seleccione una Industria$).*$'),
  ]);
  readonly averageTicket = new FormControl('', [Validators.required]);
  readonly salesVolume = new FormControl('', [Validators.required]);
  readonly integrationType = new FormControl(this.intTypes[0].value, [
    Validators.required,
    Validators.pattern('^(?!Seleccione una opción$).*$'),
  ]);

  readonly businessMetricsForm = new FormGroup({
    industry: this.industry,
    averageTicket: this.averageTicket,
    salesVolume: this.salesVolume,
    integrationType: this.integrationType,
  });

  get parentFormGroup() {
    return this.#parentContainer.control as FormGroup;
  }

  nextStep = output<void>();
  backStep = output<void>();

  next(): void {
    this.businessMetricsForm.markAllAsTouched();

    if (this.businessMetricsForm.valid) {
      this.nextStep.emit();
    }
  }

  back(): void {
    this.backStep.emit();
  }

  ngOnInit() {
    this.parentFormGroup.addControl(this.controlKey, this.businessMetricsForm);
  }

  ngOnDestroy() {
    this.parentFormGroup.removeControl(this.controlKey);
  }
}
