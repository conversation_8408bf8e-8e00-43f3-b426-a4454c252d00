import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { AplazoTrimSpacesDirective } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';

@Component({
  selector: 'app-branch-form',
  template: `
    <aplz-ui-card size="sm">
      <form
        [formGroup]="form"
        (ngSubmit)="create()"
        class="flex flex-wrap gap-4 pt-4">
        <div
          class="flex-grow-[999] flex-shrink-0 flex flex-wrap gap-4 items-baseline"
          [class.hidden]="!data?.id">
          @if (data?.id) {
            <aplz-ui-form-field class="flex-grow-1 basis-32">
              <aplz-ui-form-label> ID </aplz-ui-form-label>
              <input
                type="text"
                aplzFormInput
                formControlName="id"
                placeholder="00" />
            </aplz-ui-form-field>
          }

          @if (data?.active === true || data?.active === false) {
            <div class="flex-grow-1 basis-32">
              <div class="flex items-center gap-x-2">
                <input
                  id="active"
                  type="checkbox"
                  class="w-4 h-4 text-aplazo-aplazo bg-light border-dark-background rounded focus:ring-aplazo-aplazo focus:ring-2"
                  formControlName="active" />
                <label for="active"> Activo </label>
              </div>
            </div>
          }
        </div>

        <aplz-ui-form-field class="flex-grow-[999]">
          <aplz-ui-form-label> Nombre del Storefront </aplz-ui-form-label>
          <input
            type="text"
            aplzFormInput
            formControlName="name"
            placeholder="Nombre del Storefront"
            aplazoTrimSpaces />
          <ng-container aplzFormError>
            @if (name.touched && name.hasError('required')) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
          <ng-container aplzFormError>
            @if (
              name.touched &&
              !name.hasError('required') &&
              name.hasError('minlength')
            ) {
              <p>Ingrese por lo menos {{ minChars }} caracteres</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <div class="flex items-center justify-end gap-4 flex-wrap w-full">
          <button
            aplzButton
            type="button"
            size="md"
            aplzAppearance="stroked"
            aplzColor="light"
            (click)="close()">
            Cancelar
          </button>
          <button
            aplzButton
            type="submit"
            size="md"
            aplzAppearance="solid"
            aplzColor="dark">
            {{ data?.id ? 'Actualizar' : 'Crear' }}
          </button>
        </div>
      </form>
    </aplz-ui-card>
  `,
  imports: [
    AplazoCardComponent,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoTrimSpacesDirective,
    ReactiveFormsModule,
  ],
})
export class BranchFormComponent {
  readonly #dialogRef: DialogRef<
    | {
        name?: string;
        id?: number;
        active?: boolean;
      }
    | null
    | undefined
  > = inject(DialogRef);
  readonly minChars = 3;

  readonly data = this.#dialogRef.data;

  readonly name = new FormControl<string>(this.data?.name ?? '', {
    nonNullable: true,
    validators: [Validators.required, Validators.minLength(this.minChars)],
  });

  readonly id = new FormControl<number | null>({
    value: this.data?.id ?? null,
    disabled: true,
  });

  readonly active = new FormControl<boolean>(this.data?.active ?? true, {
    nonNullable: true,
  });

  readonly form = new FormGroup({
    name: this.name,
    id: this.id,
    active: this.active,
  });

  close(): void {
    this.#dialogRef.close({ hasConfirmation: false });
  }

  create(): void {
    this.form.markAllAsTouched();

    if (this.form.dirty && this.form.valid) {
      this.id.enable();
      this.#dialogRef.close({ hasConfirmation: true, ...this.form.value });
      this.name.setValue('');
      this.id.reset();
      this.active.reset();
      this.id.disable();
    }
  }
}
