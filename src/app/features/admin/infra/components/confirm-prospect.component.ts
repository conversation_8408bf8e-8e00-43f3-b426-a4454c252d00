import {
  ChangeDetectionStrategy,
  Component,
  inject,
  ViewEncapsulation,
} from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { DialogRef } from '@ngneat/dialog';
import { Prospect } from '../../domain/entities/prospect';

@Component({
  selector: 'app-confirm-prospect',
  template: `
    <aplz-ui-card>
      <header class="my-6">
        <h2 class="text-center font-medium text-xl">
          Confirme los datos y después de click en Crear
        </h2>
      </header>

      <section class="mt-8 p-4 bg-aplazo-aplazo rounded-xl shadow-lg">
        <div class="flex justify-between text-sm text-light">
          <span class="">
            {{ data?.['business-metrics']?.industry }}
          </span>
          <span class="">
            intType:
            {{ data?.['business-metrics']?.integrationType }}
          </span>
        </div>
        <div class="flex justify-between text-sm text-light">
          <span class="">
            Ticket:
            {{ data?.['business-metrics']?.averageTicket }}
          </span>

          <span class="">
            Ventas:
            {{ data?.['business-metrics']?.salesVolume }}
          </span>
        </div>
        <header class="flex mt-2 my-4">
          <aplz-ui-logo size="lg"></aplz-ui-logo>
        </header>

        <div class="flex flex-col w-full">
          <h3 class="font-medium text-xl mb-2">
            {{ data?.['general-info']?.merchantName }}
          </h3>
          <h4 class="font-medium text-lg mb-4">
            <span class="mr-2">&#9993;</span>
            {{ data?.['general-info']?.email }}
          </h4>

          <p class="text-base font-light text-right">
            <span>
              {{ data?.['general-info']?.webPage }}
            </span>
            <span class="mx-2">&#10073;</span>
            <span>
              {{ data?.['general-info']?.address }}
            </span>
          </p>

          <p class="text-base font-light text-right">
            <span> {{ data?.['legal-representative']?.name }} </span>
            <span class="mx-2">&#10073;</span>
            <span> {{ data?.['legal-representative']?.position }} </span>
            <span class="mx-2">&#10073;</span>
            <span class="font-medium">
              &#9990; {{ data?.['legal-representative']?.phone }}
            </span>
          </p>
        </div>
      </section>

      <footer class="flex justify-end gap-x-6 flex-wrap mt-14">
        <button
          aplzButton
          type="button"
          aplzAppearance="stroked"
          aplzColor="light"
          size="md"
          (click)="close(false)">
          Editar
        </button>

        <button
          aplzButton
          type="button"
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          (click)="close(true)">
          Crear
        </button>
      </footer>
    </aplz-ui-card>
  `,
  imports: [AplazoCardComponent, AplazoButtonComponent, AplazoLogoComponent],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConfirmProspectComponent {
  readonly #dialogRef: DialogRef<Prospect, { confirm: boolean }> =
    inject(DialogRef);

  get data(): Prospect | null {
    return this.#dialogRef.data ?? null;
  }

  close(confirm: boolean): void {
    this.#dialogRef.close({ confirm });
  }
}
