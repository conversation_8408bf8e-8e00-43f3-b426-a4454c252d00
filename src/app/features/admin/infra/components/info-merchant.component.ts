import {
  ChangeDetectionStrategy,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  output,
  ViewEncapsulation,
} from '@angular/core';
import {
  ControlContainer,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  AplazoLowercaseDirective,
  AplazoNoWhiteSpaceDirective,
  AplazoTrimSpacesDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { webpagePattern } from '../../../shared/domain/patterns';

@Component({
  selector: 'app-merchant-info',
  imports: [
    ReactiveFormsModule,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
    AplazoNoWhiteSpaceDirective,
    AplazoLowercaseDirective,
    AplazoTrimSpacesDirective,
  ],
  template: `
    <article [formGroupName]="controlKey" class="py-4 px-3">
      <h1 class="text-xl mb-6">Datos del comercio</h1>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
        <aplz-ui-form-field>
          <aplz-ui-form-label>Nombre del comercio</aplz-ui-form-label>
          <input
            aplzFormInput
            aplazoTrimSpaces
            formControlName="merchantName"
            id="merchant-name"
            type="text" />
          <ng-container aplzFormError>
            @if (
              merchantName.touched &&
              merchantName.invalid &&
              merchantName.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
        <aplz-ui-form-field>
          <aplz-ui-form-label>Correo electrónico</aplz-ui-form-label>
          <input
            aplzFormInput
            aplazoNoWhiteSpace
            aplazoLowercase
            formControlName="email"
            type="text"
            inputMode="email"
            id="merchant-email" />
          <ng-container aplzFormError>
            @if (email.touched && email.invalid && email.hasError('required')) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
          <ng-container aplzFormError>
            @if (email.touched && email.invalid && email.hasError('email')) {
              <p>Debe ser un email. Ej. test{{ '@' }}email.com</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <aplz-ui-form-field>
          <aplz-ui-form-label>Página web</aplz-ui-form-label>
          <input
            aplzFormInput
            aplazoNoWhiteSpace
            aplazoLowercase
            formControlName="webPage"
            type="text"
            inputMode="url"
            id="merchant-web-page" />
          <ng-container aplzFormError>
            @if (
              webPage.touched && webPage.invalid && webPage.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
          <ng-container aplzFormError>
            @if (
              webPage.touched && webPage.invalid && webPage.hasError('pattern')
            ) {
              <p>Debe ser un dominio válido. Ej. https://mi-dominio.com</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      </div>

      <aplz-ui-form-field>
        <aplz-ui-form-label>Dirección</aplz-ui-form-label>
        <input
          aplzFormInput
          aplazoTrimSpaces
          formControlName="address"
          type="text"
          id="merchant-address" />

        <ng-container aplzFormError>
          @if (
            address.touched && address.invalid && address.hasError('required')
          ) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <div class="flex justify-end gap-x-6 flex-wrap">
        <button
          aplzButton
          type="button"
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          (click)="next()">
          Siguiente
        </button>
      </div>
    </article>
  `,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  viewProviders: [
    {
      provide: ControlContainer,
      useFactory: () => inject(ControlContainer, { skipSelf: true }),
    },
  ],
})
export class InfoMerchantComponent implements OnInit, OnDestroy {
  readonly #parentContainer = inject(ControlContainer);
  readonly controlKey = 'general-info';
  readonly email = new FormControl<string>('', [
    Validators.required,
    Validators.email,
  ]);
  readonly merchantName = new FormControl<string>('', [Validators.required]);
  readonly webPage = new FormControl<string>('', [
    Validators.required,
    Validators.pattern(webpagePattern),
  ]);
  readonly address = new FormControl<string>('', [Validators.required]);
  readonly infoMerchantGroup = new FormGroup({
    email: this.email,
    merchantName: this.merchantName,
    webPage: this.webPage,
    address: this.address,
  });

  nextStep = output<void>();

  get parentFormGroup() {
    return this.#parentContainer.control as FormGroup;
  }

  next(): void {
    this.infoMerchantGroup.markAllAsTouched();

    if (this.infoMerchantGroup.valid) {
      this.nextStep.emit();
    }
  }

  ngOnInit() {
    this.parentFormGroup.addControl(this.controlKey, this.infoMerchantGroup);
  }

  ngOnDestroy() {
    this.parentFormGroup.removeControl(this.controlKey);
  }
}
