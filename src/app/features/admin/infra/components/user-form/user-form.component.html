<div class="h-full">
  <aplz-ui-card size="sm" class="h-full min-h-fit overflow-y-auto">
    <h1 class="mt-6 mb-4 font-semibold text-lg text-dark-secondary">
      {{ data?.id ? 'Edición ' : 'Creación ' }} de usuario
    </h1>
    @if (data?.merchantId) {
      <h2 class="my-4 font-light">Merchant ID: {{ data?.merchantId }}</h2>
    }
    @if (!data?.id) {
      <div
        class="flex gap-2 flex-wrap mt-4 mb-6 p-2 rounded-lg border border-dark-tertiary">
        <div class="flex-shrink-0 flex-grow flex items-center ps-4 rounded-lg">
          <input
            id="posui-role"
            type="radio"
            value="POSUI"
            [formControl]="platform"
            class="w-4 h-4 text-dark-secondary bg-dark-background border-dark/30 focus:ring-special-info focus:ring-2" />
          <label
            for="posui-role"
            class="w-full py-4 ms-2 text-sm font-medium text-dark">
            Usuario POSUI
          </label>
        </div>
        <div class="flex-shrink-0 flex-grow flex items-center ps-4 rounded-lg">
          <input
            id="dash-role"
            type="radio"
            [formControl]="platform"
            value="Panel"
            class="w-4 h-4 text-dark-secondary bg-dark-background border-dark/30 focus:ring-special-info focus:ring-2" />
          <label
            for="dash-role"
            class="w-full py-4 ms-2 text-sm font-medium text-dark">
            Usuario Panel
          </label>
        </div>
      </div>
    }
    <form
      [formGroup]="form"
      class="flex flex-wrap gap-4 pt-4"
      (ngSubmit)="create()">
      <div
        class="flex-grow-[999] flex-shrink-0 flex flex-wrap gap-4 items-baseline"
        [class.hidden]="!data?.id">
        @if (data?.id) {
          <aplz-ui-form-field class="flex-grow-1 basis-32">
            <aplz-ui-form-label> Usuario ID </aplz-ui-form-label>
            <input
              id="aplazo-operator-form-id"
              type="text"
              aplzFormInput
              formControlName="id"
              placeholder="00"
              inputmode="numeric" />
          </aplz-ui-form-field>
        }
      </div>

      <aplz-ui-form-field class="flex-grow-[999]">
        <aplz-ui-form-label> Username </aplz-ui-form-label>
        <input
          id="aplazo-operator-form-username"
          type="text"
          aplzFormInput
          formControlName="username"
          placeholder="Username"
          aplazoTrimSpaces
          inputmode="email" />
        <ng-container aplzFormError>
          @if (usernaname.touched && usernaname.hasError('required')) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
        <ng-container aplzFormError>
          @if (
            usernaname.touched &&
            !usernaname.hasError('required') &&
            usernaname.hasError('minlength')
          ) {
            <p>Ingrese por lo menos {{ minUsernameCharacters }} caracteres</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <aplz-mui-password-control
        [textUI]="passwordControlText"
        formControlName="password"></aplz-mui-password-control>

      <aplz-ui-form-field class="flex-grow-[999]" [hideRequiredMarker]="true">
        <aplz-ui-form-label> email </aplz-ui-form-label>
        <input
          id="aplazo-operator-form-email"
          type="text"
          aplzFormInput
          formControlName="email"
          placeholder="<EMAIL>"
          aplazoTrimSpaces
          inputmode="email" />
        <ng-container aplzFormError>
          @if (
            email.touched &&
            !email.hasError('required') &&
            email.hasError('email')
          ) {
            <p>Email invalido. Ej. me&#64;example.com</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <aplz-ui-form-field class="flex-grow-[999]">
        <aplz-ui-form-label> Rol </aplz-ui-form-label>

        <select aplzFormSelect formControlName="role">
          @if (platform.value === 'POSUI') {
            @for (item of posRolesLabels; track $index) {
              <option [ngValue]="item">{{ item }}</option>
            }
          } @else {
            @for (item of dashRolesLabels; track $index) {
              <option [ngValue]="item">{{ item }}</option>
            }
          }
        </select>
      </aplz-ui-form-field>

      @if (data?.active === true || data?.active === false) {
        <div class="flex-grow-1 basis-32">
          <div class="flex items-center gap-x-2">
            <input
              id="aplazo-operator-form-active"
              type="checkbox"
              class="w-4 h-4 text-aplazo-aplazo bg-light border-dark-background rounded focus:ring-aplazo-aplazo focus:ring-2"
              formControlName="active" />
            <label for="aplazo-operator-form-active"> Activo </label>
          </div>
        </div>
      }

      @if (
        platform.value === 'POSUI' &&
        !data?.id &&
        data?.branches &&
        data?.branches?.data
      ) {
        <aplz-ui-form-field class="flex-shrink-0 flex-grow-[999] w-full">
          <aplz-ui-form-label> Storefront ID </aplz-ui-form-label>
          <select aplzFormSelect formControlName="storefronts" multiple>
            @if ((data?.branches?.data?.length ?? 0) > 0) {
              <option [value]="-1">Todas</option>
            }
            @for (item of data!.branches!.data; track item) {
              <option [ngValue]="item.branchId">{{ item.branchId }}</option>
            }
          </select>

          <ng-container aplzFormError>
            @if (
              storefronts.touched &&
              storefronts.hasError('invalidBranches') &&
              !storefronts.hasError('failedBranchValidation')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
          <ng-container aplzFormError>
            @if (
              storefronts.touched &&
              storefronts.hasError('failedBranchValidation')
            ) {
              <p>
                Este Merchant ID no tiene sucursales asociadas. Por favor
                verifique.
              </p>
            }
          </ng-container>
        </aplz-ui-form-field>
      }

      <div class="flex items-center justify-end gap-4 flex-wrap w-full mt-6">
        <button
          aplzButton
          type="button"
          size="md"
          aplzAppearance="stroked"
          aplzColor="light"
          (click)="close()">
          Cancelar
        </button>
        <button
          aplzButton
          type="submit"
          size="md"
          aplzAppearance="solid"
          aplzColor="dark">
          {{ data?.id ? 'Actualizar' : 'Crear' }}
        </button>
      </div>
    </form>
  </aplz-ui-card>
</div>
