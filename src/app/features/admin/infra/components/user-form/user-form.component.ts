import {
  ChangeDetectionStrategy,
  Component,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import {
  AbstractControl,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { AplazoTrimSpacesDirective } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoPasswordControlComponent } from '@aplazo/shared-ui/merchant';
import { DialogRef } from '@ngneat/dialog';
import { startWith, Subject, takeUntil, tap } from 'rxjs';
import { BranchUI } from '../../../domain/dtos/branch.dto';
import {
  DASH_ROLES,
  DashRoleLabel,
  OperatorWithPasswordUIDto,
  POS_ROLES,
  PosRoleLabel,
} from '../../../domain/dtos/user.dto';

@Component({
  selector: 'app-user-form',
  templateUrl: './user-form.component.html',
  imports: [
    AplazoCardComponent,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoPasswordControlComponent,
    AplazoTrimSpacesDirective,
    ReactiveFormsModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OperatorFormComponent implements OnInit, OnDestroy {
  readonly #dialogRef: DialogRef<
    | {
        id?: number;
        storefronts?: number[];
        username?: string;
        email?: string;
        active?: boolean;
        role?: PosRoleLabel | DashRoleLabel;
        platform?: 'POSUI' | 'Panel';
        merchantId?: number;
        branches?: {
          merchantId: number;
          data: BranchUI[];
        };
      }
    | undefined,
    OperatorWithPasswordUIDto
  > = inject(DialogRef);

  readonly #destroy = new Subject<void>();

  readonly minUsernameCharacters = 3;
  readonly minPasswordCharacters = 6;

  readonly data = this.#dialogRef.data;

  readonly posRolesLabels = Object.keys(POS_ROLES) as PosRoleLabel[];
  readonly dashRolesLabels = Object.keys(DASH_ROLES) as DashRoleLabel[];

  readonly platform = new FormControl<'POSUI' | 'Panel'>(
    this.data?.platform ?? 'POSUI'
  );
  readonly usernaname = new FormControl<string>(this.data?.username ?? '', {
    nonNullable: true,
    validators: [
      Validators.required,
      Validators.minLength(this.minUsernameCharacters),
    ],
  });

  readonly id = new FormControl<number | null>({
    value: this.data?.id ?? null,
    disabled: true,
  });

  readonly active = new FormControl<boolean | null>(this.data?.active ?? null);

  readonly role = new FormControl<PosRoleLabel | DashRoleLabel>(
    this.data?.role ?? 'Vendedor(a)'
  );

  readonly storefronts = new FormControl<number[] | null>(
    this.#setInitialStorefronts()
  );

  readonly email = new FormControl<string>(this.data?.email ?? '', {
    nonNullable: true,
    validators: [Validators.email],
  });

  readonly password = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.minLength(this.minPasswordCharacters)],
  });

  readonly form = new FormGroup({
    id: this.id,
    active: this.active,
    username: this.usernaname,
    role: this.role,
    storefronts: this.storefronts,
    email: this.email,
    password: this.password,
  });

  readonly passwordControlText = {
    label: 'Password',
    placeholder: 'SuperSecretPassword',
    requiredError: 'Este campo es requerido',
    minLengthError: `Ingrese por lo menos ${this.minPasswordCharacters} caracteres`,
  };

  close(): void {
    this.#dialogRef.close({ hasConfirmation: false });
  }

  create(): void {
    this.form.markAllAsTouched();

    if (this.form.touched && this.form.valid) {
      this.id.enable();
      this.storefronts.enable();
      this.role.enable();

      const dialogResult: OperatorWithPasswordUIDto = {
        hasConfirmation: true,
        branchesId: this.storefronts.value?.filter(i => i > 0) ?? null,
        password: this.password.value,
        platform: this.platform.value as 'POSUI' | 'Panel',
        role: this.role.value as PosRoleLabel | DashRoleLabel,
        userEmail: this.email.value,
        username: this.usernaname.value,
        idAccount: this.id.value ?? undefined,
        active: this.active.value ?? true,
      };

      this.#dialogRef.close({
        ...dialogResult,
      });
      this.usernaname.setValue('');
      this.password.reset();
      this.email.reset();
      this.storefronts.reset();
      this.active.reset();
      this.id.reset();
      this.id.disable();

      if (this.platform.value === 'Panel') {
        this.storefronts.disable();
      }

      if (this.platform.value === 'POSUI') {
        this.role.disable();
      }
    }
  }

  ngOnInit(): void {
    this.storefronts.valueChanges
      .pipe(
        startWith(this.storefronts.value),
        tap(value => {
          const isAllSelected = Array.isArray(value) && value.includes(-1);

          if (isAllSelected) {
            const all =
              this.data?.branches?.data.map(branch => branch.branchId) ?? [];
            this.storefronts.setValue([-1, ...all], {
              emitEvent: false,
            });
          }
        }),
        takeUntil(this.#destroy)
      )
      .subscribe();
    if (!this.data?.id) {
      this.password.addValidators(Validators.required);

      this.password.updateValueAndValidity();
    }

    this.platform.valueChanges
      .pipe(
        startWith(this.platform.value),
        tap(platform => {
          const injectedRole = this.data?.role;
          const isInjectedAPanelRole =
            Object.keys(DASH_ROLES).findIndex(role => role === injectedRole) >=
            0;

          this.role.enable();
          if (
            platform === 'Panel' &&
            ((!!injectedRole && isInjectedAPanelRole) || !injectedRole)
          ) {
            this.role.setValue(injectedRole ?? 'PANEL_SUPPORT');
          } else if (platform === 'POSUI' && !isInjectedAPanelRole) {
            this.role.setValue(injectedRole ?? 'Vendedor(a)');
          }
          this.role.disable();
        }),
        tap(platform => {
          if (platform === 'POSUI') {
            this.storefronts.enable();
            this.storefronts.setValidators([
              this.#hasBranches,
              this.#branchValidationByPlatform(platform),
            ]);
            this.storefronts.updateValueAndValidity();

            this.role.disable();

            return;
          }

          if (platform === 'Panel') {
            this.storefronts.removeValidators([
              this.#hasBranches,
              this.#branchValidationByPlatform(platform),
            ]);
            this.storefronts.updateValueAndValidity();
            this.storefronts.disable();

            this.role.enable();
          }
        }),
        takeUntil(this.#destroy)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy.next();
    this.#destroy.complete();
  }

  #setInitialStorefronts() {
    const hasStorefronts =
      this.data?.storefronts &&
      Array.isArray(this.data.storefronts) &&
      this.data.storefronts.length > 0;

    if (!hasStorefronts) {
      return null;
    }

    const hasMatchingBranches =
      (this.data.branches?.data?.length ?? -1) ===
      this.data?.storefronts?.length;

    return hasMatchingBranches
      ? [-1, ...(this.data?.storefronts ?? [])]
      : (this.data.storefronts as number[]);
  }

  readonly #hasBranches: ValidatorFn = (control: AbstractControl) => {
    const hasBranches =
      control.value && Array.isArray(control.value) && control.value.length > 0;

    return hasBranches ? null : { invalidBranches: true };
  };

  readonly #branchValidationByPlatform: (
    plarform: 'POSUI' | 'Panel' | null
  ) => ValidatorFn = platform => {
    const hasBranches =
      (this.data?.branches?.data && this.data.branches.data.length > 0) ??
      false;

    return () => {
      if (platform === 'POSUI' && !hasBranches) {
        return { failedBranchValidation: true };
      }

      return null;
    };
  };
}
