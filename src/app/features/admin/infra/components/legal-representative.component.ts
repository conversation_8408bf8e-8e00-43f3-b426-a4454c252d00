import {
  ChangeDetectionStrategy,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  output,
  ViewEncapsulation,
} from '@angular/core';
import {
  ControlContainer,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  AplazoNoWhiteSpaceDirective,
  AplazoTrimSpacesDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';

@Component({
  selector: 'app-legal-representative',
  imports: [
    ReactiveFormsModule,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
    AplazoNoWhiteSpaceDirective,
    AplazoTrimSpacesDirective,
    OnlyNumbersDirective,
  ],
  template: `
    <article [formGroupName]="controlKey" class="py-4 px-3">
      <h1 class="text-xl mb-6">Datos del representante</h1>

      <aplz-ui-form-field>
        <aplz-ui-form-label>Nombre</aplz-ui-form-label>
        <input
          aplzFormInput
          aplazoTrimSpaces
          formControlName="name"
          id="legal-representative-name"
          type="text" />
        <ng-container aplzFormError>
          @if (name.touched && name.invalid && name.hasError('required')) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
        <aplz-ui-form-field>
          <aplz-ui-form-label>Puesto</aplz-ui-form-label>
          <input
            aplzFormInput
            aplazoTrimSpaces
            formControlName="position"
            id="legal-representative-position"
            type="text" />
          <ng-container aplzFormError>
            @if (
              position.touched &&
              position.invalid &&
              position.hasError('required')
            ) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <aplz-ui-form-field>
          <aplz-ui-form-label>Teléfono</aplz-ui-form-label>
          <input
            aplzFormInput
            aplazoNoWhiteSpace
            aplazoOnlyNumbers
            formControlName="phone"
            id="legal-representative-phone"
            type="text"
            inputMode="tel" />
          <span aplzInputPrefix class="ml-2 self-end"> 🇲🇽 +52</span>
          <ng-container aplzFormError>
            @if (phone.touched && phone.invalid && phone.hasError('required')) {
              <p>Este campo es requerido</p>
            }
          </ng-container>
          <ng-container aplzFormError>
            @if (
              phone.touched && phone.invalid && phone.hasError('minlength')
            ) {
              <p>Ingrese número telefónico de 10 dígitos</p>
            }
          </ng-container>
          <ng-container aplzFormError>
            @if (
              phone.touched && phone.invalid && phone.hasError('maxlength')
            ) {
              <p>Ingrese número telefónico de 10 dígitos</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      </div>
      <div class="flex justify-end gap-x-6 flex-wrap">
        <button
          aplzButton
          type="button"
          aplzAppearance="stroked"
          aplzColor="light"
          size="md"
          (click)="back()">
          Anterior
        </button>
        <button
          aplzButton
          type="button"
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          (click)="next()">
          Siguiente
        </button>
      </div>
    </article>
  `,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  viewProviders: [
    {
      provide: ControlContainer,
      useFactory: () => inject(ControlContainer, { skipSelf: true }),
    },
  ],
})
export class LegalRepresentativeComponent implements OnInit, OnDestroy {
  readonly #parentContainer = inject(ControlContainer);
  readonly controlKey = 'legal-representative';
  readonly name = new FormControl('', Validators.required);
  readonly position = new FormControl('', Validators.required);
  readonly phone = new FormControl('', [
    Validators.required,
    Validators.minLength(10),
    Validators.maxLength(10),
  ]);

  readonly legalRepresentativeGroup = new FormGroup({
    name: this.name,
    position: this.position,
    phone: this.phone,
  });

  nextStep = output<void>();
  backStep = output<void>();

  get parentFormGroup() {
    return this.#parentContainer.control as FormGroup;
  }

  next(): void {
    this.legalRepresentativeGroup.markAllAsTouched();

    if (this.legalRepresentativeGroup.valid) {
      this.nextStep.emit();
    }
  }

  back(): void {
    this.backStep.emit();
  }

  ngOnInit() {
    this.parentFormGroup.addControl(
      this.controlKey,
      this.legalRepresentativeGroup
    );
  }

  ngOnDestroy() {
    this.parentFormGroup.removeControl(this.controlKey);
  }
}
