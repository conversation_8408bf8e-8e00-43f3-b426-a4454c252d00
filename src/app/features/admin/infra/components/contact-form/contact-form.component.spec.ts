import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ContactFormComponent } from './contact-form.component';
import { DialogRef } from '@ngneat/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { BusinessArea } from '../../../domain/dtos/contact.dto';

describe('ContactFormComponent', () => {
  let component: ContactFormComponent;
  let fixture: ComponentFixture<ContactFormComponent>;
  let dialogRef: jasmine.SpyObj<DialogRef>;

  const mockDialogData = {
    id: 1,
    email: '<EMAIL>',
    phone: '**********',
    role: 'Manager',
    merchantId: 123,
    businessArea: 'Support' as BusinessArea,
  };

  beforeEach(async () => {
    dialogRef = jasmine.createSpyObj('DialogRef', ['close'], {
      data: mockDialogData,
    });

    await TestBed.configureTestingModule({
      imports: [
        ContactFormComponent,
        ReactiveFormsModule,
      ],
      providers: [
        { provide: DialogRef, useValue: dialogRef },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ContactFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form controls with dialog data', () => {
    expect(component.id.value).toBe(mockDialogData.id);
    expect(component.email.value).toBe(mockDialogData.email);
    expect(component.phone.value).toBe(mockDialogData.phone);
    expect(component.role.value).toBe(mockDialogData.role);
    expect(component.businessArea.value).toBe(mockDialogData.businessArea);
  });

  it('should initialize form controls with default values when no data is provided', () => {
    dialogRef = jasmine.createSpyObj('DialogRef', ['close'], {
      data: undefined,
    });

    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [
        ContactFormComponent,
        ReactiveFormsModule,
      ],
      providers: [
        { provide: DialogRef, useValue: dialogRef },
      ],
    });

    fixture = TestBed.createComponent(ContactFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    expect(component.id.value).toBeNull();
    expect(component.email.value).toBe('');
    expect(component.phone.value).toBe('');
    expect(component.role.value).toBe('');
    expect(component.businessArea.value).toBe('Support');
  });

  it('should close dialog with hasConfirmation false when close is called', () => {
    component.close();
    expect(dialogRef.close).toHaveBeenCalledWith({ hasConfirmation: false });
  });

  describe('form validation', () => {
    it('should mark form as invalid when email is empty', () => {
      component.email.setValue('');
      expect(component.form.valid).toBeFalse();
    });

    it('should mark form as invalid when email format is incorrect', () => {
      component.email.setValue('invalid-email');
      expect(component.form.valid).toBeFalse();
    });

    it('should mark form as invalid when role is empty', () => {
      component.role.setValue('');
      expect(component.form.valid).toBeFalse();
    });

    it('should mark form as invalid when phone number length is less than 10', () => {
      component.phone.setValue('123456789');
      expect(component.form.valid).toBeFalse();
    });

    it('should mark form as invalid when phone number length is more than 10', () => {
      component.phone.setValue('***********');
      expect(component.form.valid).toBeFalse();
    });

    it('should mark form as valid when all required fields are filled correctly', () => {
      component.email.setValue('<EMAIL>');
      component.role.setValue('Manager');
      component.phone.setValue('**********');
      component.businessArea.setValue('Support');
      expect(component.form.valid).toBeTrue();
    });
  });

  describe('create method', () => {
    it('should not close dialog when form is invalid', () => {
      component.email.setValue('');
      component.create();
      expect(dialogRef.close).not.toHaveBeenCalled();
    });

    it('should close dialog with form data when form is valid', () => {
      const expectedData = {
        hasConfirmation: true,
        role: 'Manager',
        email: '<EMAIL>',
        phone: '**********',
        businessArea: 'Support' as BusinessArea,
        merchantId: 123,
        id: 1,
      };

      component.create();
      expect(dialogRef.close).toHaveBeenCalledWith(expectedData);
    });

    it('should reset form controls after successful submission', () => {
      component.create();
      
      expect(component.role.value).toBe('');
      expect(component.email.value).toBe('');
      expect(component.businessArea.value).toBe('Support');
      expect(component.id.value).toBeNull();
      expect(component.id.disabled).toBeTrue();
    });
  });
});