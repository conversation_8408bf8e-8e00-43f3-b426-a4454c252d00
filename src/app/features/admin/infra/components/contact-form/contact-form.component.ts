import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  AplazoTrimSpacesDirective,
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import { Subject } from 'rxjs';
import {
  BUSINESS_AREA,
  BusinessArea,
  ContactUI,
} from '../../../domain/dtos/contact.dto';

@Component({
  selector: 'app-user-form',
  templateUrl: './contact-form.component.html',
  imports: [
    AplazoCardComponent,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
    AplazoTrimSpacesDirective,
    AplazoTruncateLengthDirective,
    OnlyNumbersDirective,
    ReactiveFormsModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContactFormComponent implements OnDestroy {
  readonly #dialogRef: DialogRef<
    | {
        id?: number;
        email?: string;
        phone?: string;
        role?: string;
        merchantId?: number;
        businessArea?: BusinessArea;
      }
    | undefined,
    Partial<ContactUI> & { hasConfirmation: boolean }
  > = inject(DialogRef);

  readonly #destroy = new Subject<void>();

  readonly data = this.#dialogRef.data;

  readonly roles = Object.keys(BUSINESS_AREA).filter(Boolean);

  readonly id = new FormControl<number | null>({
    value: this.data?.id ?? null,
    disabled: true,
  });

  readonly role = new FormControl<string>(this.data?.role ?? '', {
    nonNullable: true,
    validators: [Validators.required],
  });

  readonly businessArea = new FormControl<BusinessArea>(
    this.data?.businessArea ?? 'Support',
    {
      validators: [Validators.required],
    }
  );

  readonly email = new FormControl<string>(this.data?.email ?? '', {
    nonNullable: true,
    validators: [Validators.required, Validators.email],
  });

  readonly phone = new FormControl<string>(this.data?.phone ?? '', {
    validators: [Validators.minLength(10), Validators.maxLength(10)],
  });

  readonly form = new FormGroup({
    id: this.id,
    role: this.role,
    email: this.email,
    phone: this.phone,
    businessArea: this.businessArea,
  });

  close(): void {
    this.#dialogRef.close({ hasConfirmation: false });
  }

  create(): void {
    this.form.markAllAsTouched();

    if (this.form.touched && this.form.valid) {
      this.id.enable();

      const dialogResult = {
        hasConfirmation: true,
        role: this.role.value ?? undefined,
        email: this.email.value,
        phone: this.phone.value ?? '',
        businessArea: this.businessArea.value ?? undefined,
        merchantId: this.data?.merchantId,
        id: this.id.value ?? undefined,
      };

      this.#dialogRef.close({
        ...dialogResult,
      });

      this.role.reset('');
      this.email.reset('');
      this.businessArea.reset('Support');
      this.id.reset(null);
      this.id.disable();
    }
  }

  ngOnDestroy(): void {
    this.#destroy.next();
    this.#destroy.complete();
  }
}
