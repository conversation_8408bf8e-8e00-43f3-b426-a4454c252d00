<div class="h-full">
  <aplz-ui-card size="sm" class="h-full min-h-fit overflow-y-auto">
    <h1 class="mt-6 mb-4 font-semibold text-lg text-dark-secondary">
      {{ data?.id ? 'Edición ' : 'Creación ' }} de contacto
    </h1>
    @if (data?.merchantId) {
      <h2 class="my-4 font-light">Merchant ID: {{ data?.merchantId }}</h2>
    }
    <form
      [formGroup]="form"
      class="flex flex-wrap gap-4 pt-4"
      (ngSubmit)="create()">
      <div
        class="flex-grow-[999] flex-shrink-0 flex flex-wrap gap-4 items-baseline"
        [class.hidden]="!data?.id">
        @if (data?.id) {
          <aplz-ui-form-field class="flex-grow-1 basis-32">
            <aplz-ui-form-label> Contacto ID </aplz-ui-form-label>
            <input
              type="text"
              aplzFormInput
              formControlName="id"
              placeholder="00"
              inputmode="numeric" />
          </aplz-ui-form-field>
        }
      </div>

      <aplz-ui-form-field class="flex-grow-[999]">
        <aplz-ui-form-label> Rol </aplz-ui-form-label>
        <input
          type="text"
          aplzFormInput
          formControlName="role"
          placeholder="Mkt"
          aplazoTrimSpaces />
        <ng-container aplzFormError>
          @if (role.touched && role.hasError('required')) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <aplz-ui-form-field class="flex-grow-[999]">
        <aplz-ui-form-label> Teléfono </aplz-ui-form-label>
        <input
          type="text"
          aplzFormInput
          formControlName="phone"
          placeholder="00 0000 0000"
          aplazoOnlyNumbers
          [aplazoTruncateLength]="10"
          inputmode="tel" />
        <ng-container aplzFormError>
          @if (
            phone.touched &&
            (phone.hasError('minlength') || phone.hasError('maxlength'))
          ) {
            <p>Asegurese de que el teléfono sea de 10 dígitos</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <aplz-ui-form-field class="flex-grow-[999]">
        <aplz-ui-form-label> email </aplz-ui-form-label>
        <input
          type="text"
          aplzFormInput
          formControlName="email"
          placeholder="<EMAIL>"
          aplazoTrimSpaces
          inputmode="email" />
        <ng-container aplzFormError>
          @if (email.touched && email.hasError('required')) {
            <p>Este campo es requerido</p>
          }
        </ng-container>
        <ng-container aplzFormError>
          @if (
            email.touched &&
            !email.hasError('required') &&
            email.hasError('email')
          ) {
            <p>Email invalido. Ej. me&#64;example.com</p>
          }
        </ng-container>
      </aplz-ui-form-field>

      <aplz-ui-form-field class="flex-grow-[999]">
        <aplz-ui-form-label> Área </aplz-ui-form-label>

        <select aplzFormSelect formControlName="businessArea">
          @for (item of roles; track $index) {
            <option [ngValue]="item">{{ item }}</option>
          }
        </select>
      </aplz-ui-form-field>

      <div class="flex items-center justify-end gap-4 flex-wrap w-full mt-6">
        <button
          aplzButton
          type="button"
          size="md"
          aplzAppearance="stroked"
          aplzColor="light"
          (click)="close()">
          Cancelar
        </button>
        <button
          aplzButton
          type="submit"
          size="md"
          aplzAppearance="solid"
          aplzColor="dark">
          {{ data?.id ? 'Actualizar' : 'Crear' }}
        </button>
      </div>
    </form>
  </aplz-ui-card>
</div>
