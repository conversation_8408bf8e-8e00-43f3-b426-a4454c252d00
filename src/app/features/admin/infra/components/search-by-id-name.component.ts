import {
  ChangeDetectionStrategy,
  Component,
  OnDestroy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoFormFieldDirectives,
  ngControlInjector,
  NoopValueAccesorDirective,
} from '@aplazo/shared-ui/forms';
import { NgxMaskDirective } from 'ngx-mask';
import { buffer, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-search-by-id-name',
  template: `
    <form
      class="grid gap-4 grid-cols-1 w-full"
      [formGroup]="form"
      (ngSubmit)="updateValue()">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <aplz-ui-form-field>
          <aplz-ui-form-label> Merchant ID </aplz-ui-form-label>
          <input
            type="text"
            aplzFormInput
            id="merchantId"
            mask="separator.0"
            formControlName="merchantId" />
          <ng-container aplzFormError>
            @if (merchantIdMinError) {
              <p>Ingrese un valor mayor a 0</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <aplz-ui-form-field>
          <aplz-ui-form-label> Nombre Comercio </aplz-ui-form-label>
          <input
            type="text"
            aplzFormInput
            id="merchantName"
            formControlName="merchantName" />
          <ng-container aplzFormError>
            @if (merchantNamePatternError) {
              <p>Solo letras, números y/o {{ '. / - + ( ) { }' }}</p>
            }
          </ng-container>
        </aplz-ui-form-field>
      </div>

      <div class="flex w-1/2 min-w-fit mx-auto">
        <button
          class="w-full"
          aplzButton
          aplzAppearance="solid"
          size="md"
          aplzColor="dark"
          type="submit">
          Buscar
        </button>
      </div>
    </form>
  `,
  imports: [
    ReactiveFormsModule,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
    NgxMaskDirective,
  ],
  hostDirectives: [NoopValueAccesorDirective],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchByIdOrNameComponent implements OnInit, OnDestroy {
  readonly #ngControl = ngControlInjector();
  readonly #destroy$ = new Subject<void>();

  get currentControl() {
    return this.#ngControl?.control as FormControl<{
      id: number;
      name: string;
    }>;
  }

  readonly merchantId = new FormControl<string>('', [
    Validators.pattern(/^\d+$/),
    Validators.min(1),
  ]);

  readonly merchantName = new FormControl<string>('', [
    Validators.pattern(/^[a-zA-ZñÑáéíóúÁÉÍÓÚ\d\s./\-+(){}\\]+$/),
  ]);

  readonly form = new FormGroup({
    merchantId: this.merchantId,
    merchantName: this.merchantName,
  });

  get merchantNamePatternError() {
    return this.merchantName.dirty && this.merchantName.hasError('pattern');
  }

  get merchantIdMinError() {
    return this.merchantId.dirty && this.merchantId.hasError('min');
  }

  updateValue(): void {
    this.form.markAllAsTouched();

    if (this.merchantId.valid && this.merchantId.value) {
      this.currentControl?.setValue({
        id: parseInt(this.merchantId.value),
        name: '',
      });
    }

    if (this.merchantName.valid && this.merchantName.value) {
      this.currentControl?.setValue({
        id: 0,
        name: String(this.merchantName.value),
      });
    }
  }

  ngOnInit(): void {
    this.merchantId.valueChanges
      .pipe(buffer(this.merchantName.valueChanges), takeUntil(this.#destroy$))
      .subscribe(val => {
        const isValid = !isNaN(Number(val));
        if (isValid && this.merchantName.value) {
          this.merchantId.setValue(null);
        }
      });

    this.merchantName.valueChanges
      .pipe(buffer(this.merchantId.valueChanges), takeUntil(this.#destroy$))
      .subscribe(val => {
        const isValidMerchantId =
          this.merchantId.value != null &&
          !isNaN(Number(this.merchantId.value)) &&
          Number(this.merchantId.value) >= 0;

        if (val && isValidMerchantId) {
          this.merchantName.setValue('');
        }
      });
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
