import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { invalidWebpagePatternError } from '../../../shared/domain/messages-text';
import { webpagePattern } from '../../../shared/domain/patterns';
import {
  MerchantBasicsResponse,
  MerchantBasicsUI,
} from '../../domain/dtos/basics.dto';
import {
  MerchantGeneralInfoResponse,
  MerchantGeneralInfoUI,
} from '../../domain/dtos/general-info.dto';
import { NewMerchantRequest } from '../../domain/dtos/new-merchant-request.dto';
import { AVERAGE_TICKET } from '../../domain/entities/average-tickets';
import { GROSS_SALES } from '../../domain/entities/gross-sales';
import { INDUSTRIES } from '../../domain/entities/industries';
import { INTEGRATION_TYPES } from '../../domain/entities/integration-types';
import { Prospect } from '../../domain/entities/prospect';

export const newMerchantUIToRequestDefaultError =
  'La creación de un nuevo comercio no se pudo completar. Verifique los valores enviados.';
export const invalidIndustryError = 'La industria seleccionada no es válida.';
export const invalidPhoneError = 'El teléfono ingresado no es válido.';
export const invalidAvgTicketError =
  'El ticket promedio ingresado no es válido.';
export const invalidYearlySalesError =
  'Las ventas anuales ingresadas no son válidas.';
export const invalidIntTypeError =
  'El tipo de integración seleccionado no es válido.';
export const basicsNullishDefaultError =
  'La información básica del comercio no se pudo obtener.';
export const generalsNullishDefaultError =
  'La información general del comercio no se pudo obtener.';

export class AdminMerchantMapper {
  static readonly #newMerchantReqKeys = [
    'general-info',
    'legal-representative',
    'business-metrics',
  ];

  static fromUIToRequest(prospect: Prospect): NewMerchantRequest {
    const mainProspectKeysError = this.#newMerchantReqKeys
      .map(key => {
        const guard = Guard.againstNullOrUndefined(prospect, key);

        return guard.succeeded ? null : guard.message;
      })
      .filter(Boolean);

    if (mainProspectKeysError.length > 0) {
      throw new RuntimeMerchantError(
        newMerchantUIToRequestDefaultError,
        'AdminMerchantMapper::fromUIToRequest::' +
          mainProspectKeysError.join(', ')
      );
    }

    const generalInfoKeysError = ['email', 'merchantName', 'webPage', 'address']
      .map(key => {
        const guard = Guard.againstNullOrUndefined(
          prospect['general-info'],
          key
        );

        return guard.succeeded ? null : guard.message;
      })
      .filter(Boolean);

    if (generalInfoKeysError.length > 0) {
      throw new RuntimeMerchantError(
        newMerchantUIToRequestDefaultError,
        'AdminMerchantMapper::fromUIToRequest::general-info::' +
          generalInfoKeysError.join(', ')
      );
    }

    this.validateWebpagePattern(prospect['general-info'].webPage);

    const legalRepKeysError = ['name', 'position', 'phone']
      .map(key => {
        const guard = Guard.againstNullOrUndefined(
          prospect['legal-representative'],
          key
        );

        return guard.succeeded ? null : guard.message;
      })
      .filter(Boolean);

    if (legalRepKeysError.length > 0) {
      throw new RuntimeMerchantError(
        newMerchantUIToRequestDefaultError,
        'AdminMerchantMapper::fromUIToRequest::legal-representative::' +
          legalRepKeysError.join(', ')
      );
    }

    const businessMetricsKeysError = [
      'industry',
      'averageTicket',
      'salesVolume',
      'integrationType',
    ]
      .map(key => {
        const guard = Guard.againstNullOrUndefined(
          prospect['business-metrics'],
          key
        );

        return guard.succeeded ? null : guard.message;
      })
      .filter(Boolean);

    if (businessMetricsKeysError.length > 0) {
      throw new RuntimeMerchantError(
        newMerchantUIToRequestDefaultError,
        'AdminMerchantMapper::fromUIToRequest::business-metrics::' +
          businessMetricsKeysError.join(', ')
      );
    }

    Guard.againstInvalidNumbers(
      Number(prospect['legal-representative'].phone),
      'AdminMerchantMapper::fromUIToRequest::phone'
    );

    const industry = INDUSTRIES.find(
      i => i === prospect['business-metrics'].industry
    );

    if (!industry) {
      throw new RuntimeMerchantError(
        invalidIndustryError,
        'AdminMerchantMapper::fromUIToRequest::industry'
      );
    }

    const avgTicket = AVERAGE_TICKET.find(
      i => i.value === prospect['business-metrics'].averageTicket
    );

    if (!avgTicket) {
      throw new RuntimeMerchantError(
        invalidAvgTicketError,
        'AdminMerchantMapper::fromUIToRequest::avgTicket'
      );
    }

    const yearlySales = GROSS_SALES.find(
      i => i.value === prospect['business-metrics'].salesVolume
    );

    if (!yearlySales) {
      throw new RuntimeMerchantError(
        invalidYearlySalesError,
        'AdminMerchantMapper::fromUIToRequest::yearlySales'
      );
    }

    const intType = INTEGRATION_TYPES.find(
      i => i.value === prospect['business-metrics'].integrationType
    );

    if (!intType) {
      throw new RuntimeMerchantError(
        invalidIntTypeError,
        'AdminMerchantMapper::fromUIToRequest::intType'
      );
    }

    return {
      basic: {
        email: prospect['general-info'].email,
        businessName: prospect['general-info'].merchantName,
        businessWebpage: prospect['general-info'].webPage,
        businessAddress: prospect['general-info'].address,
        contactName: prospect['legal-representative'].name,
        position: prospect['legal-representative'].position,
        phone: `52${prospect['legal-representative'].phone}`,
        industry: industry,
        avgTicket: avgTicket.value,
        yearlySales: yearlySales.value,
        intType: intType.value,
      },
    };
  }

  static fromBasicsResponseToUI(
    args: MerchantBasicsResponse
  ): MerchantBasicsUI {
    const nullishErrors = ['id', 'idMComInfo', 'name', 'status', 'intType']
      .map(key => {
        const guard = Guard.againstNullOrUndefined(args, key);

        return guard.succeeded ? null : guard.message;
      })
      .filter(Boolean);

    let createdAt: Date | null = null;

    if (args.created) {
      createdAt = new Date(args.created);

      Guard.againstInvalidDate({
        date: createdAt,
        errorMessage: 'La fecha de creación no es válida.',
        origin: 'AdminMerchantMapper::fromBasicsResponseToUI',
      });
    }

    let updatedAt: Date | null = null;

    if (args.updated) {
      updatedAt = new Date(args.updated);

      Guard.againstInvalidDate({
        date: updatedAt,
        errorMessage: 'La fecha de actualización no es válida.',
        origin: 'AdminMerchantMapper::fromBasicsResponseToUI',
      });
    }

    return {
      merchantId: args.id,
      mComInfoId: args.idMComInfo,
      merchantName: args.name,
      status: args.status,
      intType: args.intType,
      createdAt,
      updatedAt,
      error: nullishErrors.length > 0 ? nullishErrors : null,
    };
  }

  static fromGeneralsResponseToUI(
    args: MerchantGeneralInfoResponse
  ): MerchantGeneralInfoUI {
    const nullishErrors = [
      'id',
      'idMComInfo',
      'idMQuestionnaire',
      'merchantEmail',
      'merchantAddress',
      'merchantWebsite',
      'merchantIndustry',
      'merchantAov',
      'merchantRevenue',
      'merchantCategory',
    ]
      .map(key => {
        const guard = Guard.againstNullOrUndefined(args, key);

        return guard.succeeded ? null : guard.message;
      })
      .filter(Boolean);

    return {
      merchantId: args.id,
      mComInfoId: args.idMComInfo,
      mQuestionnaireId: args.idMQuestionnaire,
      merchantEmail: args.merchantEmail,
      merchantAddress: args.merchantAddress,
      merchantWebsite: args.merchantWebsite,
      merchantIndustry: args.merchantIndustry,
      merchantAov: args.merchantAov,
      merchantRevenue: args.merchantRevenue,
      merchantCategory: args.merchantCategory,
      error: nullishErrors.length > 0 ? nullishErrors : null,
    };
  }

  static validateWebpagePattern(toValidate: string): void | never {
    const validatedPatterns = webpagePattern.exec(toValidate);

    if (!validatedPatterns) {
      throw new RuntimeMerchantError(
        invalidWebpagePatternError,
        'AdminMerchantMapper::invalidEmailPattern'
      );
    }
  }
}
