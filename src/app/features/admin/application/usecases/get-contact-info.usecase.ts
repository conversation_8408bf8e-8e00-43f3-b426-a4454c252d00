import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { UTCDate } from '@date-fns/utc';
import { catchError, finalize, map, Observable, of } from 'rxjs';
import {
  ContactUI,
  serializeRetrievedContactResponse,
} from '../../domain/dtos/contact.dto';
import { MerchantContactsRepository } from '../../domain/repositories/contact-info.repository';

@Injectable()
export class GetContactInfoUseCase
  implements BaseUsecase<number, Observable<ContactUI[]>>
{
  readonly #repository = inject(MerchantContactsRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(merchantId: number): Observable<ContactUI[]> {
    const idLoader = this.#loader.show();

    try {
      Guard.againstInvalidNumbers(
        merchantId,
        'GetContactInfoUseCase::invalidMerchantId',
        'El id del comercio debe ser un número entero válido.'
      );

      if (merchantId <= 0) {
        throw new RuntimeMerchantError(
          'El id del comercio debe ser un número entero válido mayor de 0.',
          'GetContactInfoUseCase::invalidMerchantId'
        );
      }

      return this.#repository.getInfo(merchantId).pipe(
        map(res => res.map(serializeRetrievedContactResponse)),

        map(contacts => {
          return contacts.map(contact => {
            const updateAt = contact.updateAt
              ? new UTCDate(contact.updateAt)
              : undefined;
            const updateAtIso = updateAt?.toISOString() ?? '';

            return {
              ...contact,
              updateAt: contact.updateAt ? updateAtIso : undefined,
            };
          });
        }),

        map(contacts => {
          const sorted = contacts.slice();

          if (sorted.every(contact => contact.id && contact.updateAt)) {
            sorted.sort((a, b) => {
              const numA = a.updateAt!.toLocaleLowerCase();
              const numB = b.updateAt!.toLocaleLowerCase();

              if (numA > numB) return -1;
              if (numA < numB) return 1;
              return 0;
            });
          }

          return sorted;
        }),

        catchError(err => {
          if (
            err instanceof HttpErrorResponse &&
            err.status >= 500 &&
            err.error.error.startsWith('Unable to find')
          ) {
            throw new RuntimeMerchantError(
              'El comercio con el id proporcionado no fue encontrado.',
              'GetContactInfoUseCase::idNotFound'
            );
          }

          throw err;
        }),

        catchError(err => {
          return this.#errorHandler.handle(err, of([]));
        }),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);
      return this.#errorHandler.handle(error, of([]));
    }
  }
}
