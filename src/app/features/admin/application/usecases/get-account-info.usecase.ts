import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  finalize,
  map,
  Observable,
  of,
  take,
  throwError,
} from 'rxjs';
import { AccountInfoUIResponse } from '../../domain/dtos/account.dto';
import { MerchantAccountRepository } from '../../domain/repositories/account-info.repository';

export const defaultAccountInfo: AccountInfoUIResponse = {
  merchantId: 0,
  token: '',
  email: '',
  bank: '',
  bankAccount: '',
};

@Injectable({ providedIn: 'root' })
export class GetAccountInfoUseCase
  implements BaseUsecase<number, Observable<AccountInfoUIResponse>>
{
  readonly #repository: MerchantAccountRepository = inject(
    MerchantAccountRepository
  );
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(merchantId: number): Observable<AccountInfoUIResponse> {
    const idLoader = this.#loader.show();

    try {
      Guard.againstInvalidNumbers(
        merchantId,
        'GetAccountInfoUseCase::invalidMerchantId',
        'El id del comercio debe ser un número entero válido.'
      );

      if (merchantId <= 0) {
        throw new RuntimeMerchantError(
          'El id del comercio debe ser un número entero válido mayor de 0.',
          'GetAccountInfoUseCase::invalidMerchantId'
        );
      }

      return this.#repository.getInfo(merchantId).pipe(
        map(response => {
          if (!response) {
            return defaultAccountInfo;
          }

          return {
            merchantId: response.merchantId,
            token: response.token ?? '',
            email: response.emailAccount ?? '',
            bank: response.bank ?? '',
            bankAccount: response.bankAccount ?? '',
          };
        }),

        catchError(err => {
          if (
            err instanceof HttpErrorResponse &&
            err.status >= 500 &&
            err.error.error.startsWith('Unable to find')
          ) {
            this.#notifier.warning({
              title: 'Comercio no encontrado',
              message: 'Revise el id del comercio e intente nuevamente.',
            });

            return of(defaultAccountInfo);
          }

          return this.#errorHandler.handle(err, of(defaultAccountInfo));
        }),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);
      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
