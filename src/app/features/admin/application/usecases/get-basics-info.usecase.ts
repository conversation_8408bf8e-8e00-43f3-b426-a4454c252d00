import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, of, take } from 'rxjs';
import { MerchantBasicsUI } from '../../domain/dtos/basics.dto';
import { MerchantBasicInfoRepository } from '../../domain/repositories/basic-info.repository';
import { AdminMerchantMapper } from '../services/admin-mapper';

export const defaultBasicsInfo: MerchantBasicsUI = {
  merchantId: 0,
  mComInfoId: 0,
  merchantName: '',
  status: '',
  intType: '',
  createdAt: null,
  updatedAt: null,
  error: null,
};

@Injectable({ providedIn: 'root' })
export class GetBasicsInfoUseCase
  implements BaseUsecase<number, Observable<MerchantBasicsUI>>
{
  readonly #repository: MerchantBasicInfoRepository = inject(
    MerchantBasicInfoRepository
  );
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(merchantId: number): Observable<MerchantBasicsUI> {
    const idLoader = this.#loader.show();

    try {
      Guard.againstInvalidNumbers(
        merchantId,
        'GetBasicsInfoUseCase::invalidMerchantId',
        'El id del comercio debe ser un número entero válido.'
      );

      if (merchantId <= 0) {
        throw new RuntimeMerchantError(
          'El id del comercio debe ser un número entero válido mayor de 0.',
          'GetBasicsInfoUseCase::invalidMerchantId'
        );
      }

      return this.#repository.getInfo(merchantId).pipe(
        map(AdminMerchantMapper.fromBasicsResponseToUI),

        catchError(err => {
          if (
            err instanceof HttpErrorResponse &&
            err.status >= 500 &&
            err.error.error.startsWith('Unable to find')
          ) {
            throw new RuntimeMerchantError(
              'El comercio con el id proporcionado no fue encontrado.',
              'GetBasicsInfoUseCase::idNotFound'
            );
          }

          throw err;
        }),

        catchError(err =>
          this.#errorHandler.handle(err, of(defaultBasicsInfo))
        ),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(error, of(defaultBasicsInfo));
    }
  }
}
