import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, EMPTY, finalize, map, Observable, take, tap } from 'rxjs';
import {
  fromBasicsRequestUIToRepository,
  MerchantUpdateBasicsRepositoryRequest,
  MerchantUpdateBasicsUIRequest,
} from '../../domain/dtos/basics.dto';
import { MerchantBasicInfoRepository } from '../../domain/repositories/basic-info.repository';

@Injectable({
  providedIn: 'root',
})
export class UpdateMerchantBasicsUsecase
  implements
    BaseUsecase<
      MerchantUpdateBasicsUIRequest,
      Observable<MerchantUpdateBasicsRepositoryRequest>
    >
{
  readonly #repository: MerchantBasicInfoRepository = inject(
    MerchantBasicInfoRepository
  );
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(
    args: MerchantUpdateBasicsUIRequest
  ): Observable<MerchantUpdateBasicsRepositoryRequest> {
    const idLoader = this.#loader.show();

    try {
      const request = fromBasicsRequestUIToRepository(args);

      return this.#repository.updateByMerchantId(request).pipe(
        map(() => request),
        tap(() => {
          this.#notifier.success({
            title: 'Información básica del merchant actualizada correctamente',
          });
        }),

        catchError(err => {
          if (err?.status === 400 && err?.error?.error != null) {
            const error = err.error.error;
            const parsed = error.replace('[', '').replace(']', '').split(', ');

            throw new RuntimeMerchantError(
              parsed.join(', '),
              'UpdateMerchantStatusUsecase::badRequest'
            );
          }

          throw err;
        }),

        catchError(err => this.#errorHandler.handle(err, EMPTY)),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);
      return this.#errorHandler.handle(error, EMPTY);
    }
  }
}
