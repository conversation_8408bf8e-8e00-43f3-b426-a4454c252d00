import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, take, tap, throwError } from 'rxjs';
import {
  OperatorResponse,
  OperatorWithPasswordDto,
  toRepositoryUpdateRequest,
} from '../../domain/dtos/user.dto';
import { MerchantOperatorsRepository } from '../../domain/repositories/users-info.repository';

@Injectable()
export class UpdateOperatorUseCase extends BaseUsecase<
  Partial<OperatorWithPasswordDto>,
  Observable<any>
> {
  readonly #repository = inject(MerchantOperatorsRepository);
  readonly #usecaseErrror = inject(UseCaseErrorHandler);
  readonly #notifier = inject(NotifierService);
  readonly #loader = inject(LoaderService);

  execute(args: OperatorWithPasswordDto): Observable<OperatorResponse> {
    const idLoader = this.#loader.show();

    try {
      const request = toRepositoryUpdateRequest(args);

      return this.#repository.updateOne(request).pipe(
        tap(() => {
          this.#notifier.success({
            title: 'Usuario actualizado exitosamente',
          });
        }),

        catchError(error => {
          if (
            error instanceof HttpErrorResponse &&
            error.status === 400 &&
            error.error?.error.includes('Username already exists')
          ) {
            throw new RuntimeMerchantError(
              'El usuario ya existe',
              'UpdateOperatorUseCase::userAlreadyExists'
            );
          }

          if (
            error instanceof HttpErrorResponse &&
            error.status === 400 &&
            error.error?.error.includes('Email already exists')
          ) {
            throw new RuntimeMerchantError(
              'El correo ya fue registrado',
              'UpdateOperatorUseCase::emailAlreadyRegistered'
            );
          }

          throw error;
        }),

        catchError(error => this.#usecaseErrror.handle<never>(error)),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#usecaseErrror.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
