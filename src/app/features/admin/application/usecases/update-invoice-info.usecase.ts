import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RFC,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  finalize,
  map,
  Observable,
  take,
  tap,
  throwError,
} from 'rxjs';
import { UserEmail } from '../../../login/domain/entities/user-email';
import {
  CFDI,
  INVOICE_REGIME,
  InvoiceRepositoryRequest,
  InvoiceUI,
  PAYMENT_FORM,
  PAYMENT_METHOD,
} from '../../domain/dtos/invoice.dto';
import { MerchantInvoiceInfoRepository } from '../../domain/repositories/invoice-info.repository';

@Injectable()
export class UpdateInvoiceInfoUsecase
  implements BaseUsecase<InvoiceUI, Observable<InvoiceUI>>
{
  readonly #repository = inject(MerchantInvoiceInfoRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: InvoiceUI): Observable<InvoiceUI> {
    const idLoader = this.#loader.show();

    try {
      const keysToValidate = [
        'merchantId',
        'rfc',
        'name',
        'email',
        'paymentMethod',
        'zipCode',
        'invoiceRegime',
        'currency',
      ];

      const errors = keysToValidate
        .map(key => {
          const result = Guard.againstNullOrUndefined(args, key);
          const result2 = Guard.againstEmptyValue(args, key);

          const results: any[] = [];

          if (!result.succeeded) {
            results.push(result.message);
          }

          if (!result2.succeeded) {
            results.push(result2.message);
          }

          return results.length > 0 ? results.join(', ') : null;
        })
        .filter(Boolean);

      if (errors.length) {
        throw new RuntimeMerchantError(
          'Detectamos un error: ' + errors.join(', '),
          'UpdateInvoiceInfoUsecase::invalidArgs'
        );
      }

      Guard.againstInvalidNumbers(
        args.merchantId,
        'UpdateInvoiceInfoUsecase::invalidMerchantId',
        'El id del comercio debe ser un número entero válido.'
      );

      if (args.zipCode.length !== 5) {
        throw new RuntimeMerchantError(
          'El código postal debe tener 5 dígitos.',
          'UpdateInvoiceInfoUsecase::execute::invalidZipCode'
        );
      }

      Guard.againstInvalidNumbers(
        +args.zipCode,
        'UpdateInvoiceInfoUsecase::invalidZipCode',
        'El código postal debe estar conformado solo por números.'
      );

      if (args.name.trim().length < 1) {
        throw new RuntimeMerchantError(
          'El nombre del comercio no puede estar vacío.',
          'UpdateInvoiceInfoUsecase::execute::invalidName'
        );
      }

      const parsedName = args.name.trim().toLocaleUpperCase();

      const email = UserEmail.create(args.email);

      const rfc = RFC.create(args.rfc);

      const invoiceRegimeId = INVOICE_REGIME[args.invoiceRegime] ?? null;

      if (!invoiceRegimeId) {
        throw new RuntimeMerchantError(
          'El régimen fiscal no es válido.',
          'UpdateInvoiceInfoUsecase::execute::invalidInvoiceRegime'
        );
      }

      const paymentMethodId = PAYMENT_METHOD[args.paymentMethod] ?? null;

      if (!paymentMethodId) {
        throw new RuntimeMerchantError(
          'El método de pago no es válido.',
          'UpdateInvoiceInfoUsecase::execute::invalidPaymentMethod'
        );
      }

      const paymentFormId = PAYMENT_FORM[args.paymentForm] ?? null;

      if (!paymentFormId) {
        throw new RuntimeMerchantError(
          'La forma de pago no es válida.',
          'UpdateInvoiceInfoUsecase::execute::invalidPaymentForm'
        );
      }

      const cfdiId = CFDI[args.cfdi] ?? null;

      if (!cfdiId) {
        throw new RuntimeMerchantError(
          'El CFDI no es válido.',
          'UpdateInvoiceInfoUsecase::execute::invalidCfdi'
        );
      }

      const request: InvoiceRepositoryRequest = {
        merchantId: args.merchantId,
        currency: args.currency,
        zipCode: args.zipCode,
        email: email.value,
        rfc: rfc.value,
        name: parsedName,
        cfdiId,
        invoiceRegimeId,
        paymentFormId,
        paymentMethodId,
        facPubGral: args.facPubGral,
      };

      return this.#repository.updateOneBy(request).pipe(
        tap(() => {
          this.#notifier.success({
            title: 'Información actualizada correctamente',
          });
        }),

        map(() => args),

        catchError(error => {
          return this.#errorHandler.handle<never>(error);
        }),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
