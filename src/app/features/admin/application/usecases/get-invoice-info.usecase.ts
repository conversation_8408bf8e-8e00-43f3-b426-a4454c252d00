import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, throwError } from 'rxjs';
import {
  CFDI,
  CfdiCatalogId,
  CfdiLabel,
  INVOICE_REGIME,
  InvoiceRegimeLabel,
  InvoiceUI,
  PAYMENT_FORM,
  PAYMENT_METHOD,
  PaymentFormCatalogId,
  PaymentFormLabel,
  PaymentMethodLabel,
} from '../../domain/dtos/invoice.dto';
import { MerchantInvoiceInfoRepository } from '../../domain/repositories/invoice-info.repository';

export const defaultInvoiceInfo: InvoiceUI = {
  id: 0,
  merchantId: 0,
  cfdi: '' as any,
  currency: 'MXN',
  email: '',
  invoiceRegime: '' as any,
  name: '',
  paymentForm: '' as any,
  paymentMethod: '' as any,
  rfc: '',
  zipCode: '',
  paymentFormCode: 0 as any,
  cfdiCode: 0 as any,
  facPubGral: false,
};

@Injectable({ providedIn: 'root' })
export class GetInvoiceInfoUseCase
  implements BaseUsecase<number, Observable<InvoiceUI>>
{
  readonly #repository: MerchantInvoiceInfoRepository = inject(
    MerchantInvoiceInfoRepository
  );
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(merchantId: number): Observable<InvoiceUI> {
    const idLoader = this.#loader.show();

    try {
      Guard.againstInvalidNumbers(
        merchantId,
        'GetInvoiceInfoUseCase::invalidMerchantId',
        'El id del comercio debe ser un número entero válido.'
      );

      if (merchantId <= 0) {
        throw new RuntimeMerchantError(
          'El id del comercio debe ser un número entero válido mayor de 0.',
          'GetInvoiceInfoUseCase::invalidMerchantId'
        );
      }

      return this.#repository.getInfo(merchantId).pipe(
        map(response => {
          if (!response) {
            return defaultInvoiceInfo;
          }

          const defaultCurrency = 'MXN';

          let cfdiLabel =
            response.catalogCfdi.name && response.catalogCfdi.code
              ? (`${response.catalogCfdi.code} - ${response.catalogCfdi.name}` as CfdiLabel)
              : ('' as CfdiLabel);
          let cfdiCatalogId = (response.catalogCfdi?.id ?? 0) as CfdiCatalogId;

          Object.entries(CFDI).every(([key, value]) => {
            if (cfdiCatalogId === value) {
              cfdiLabel = key as any;
              cfdiCatalogId = value;
              return false;
            }

            return true;
          });

          let paymentFormLabel =
            response.catalogPaymentForm.name && response.catalogPaymentForm.code
              ? `${response.catalogPaymentForm.code} - ${response.catalogPaymentForm.name}`
              : '';
          let paymentFormCatalogId = response.catalogPaymentForm?.id ?? 0;

          Object.entries(PAYMENT_FORM).every(([key, value]) => {
            if (paymentFormCatalogId === value) {
              paymentFormLabel = key;
              paymentFormCatalogId = value;
              return false;
            }

            return true;
          });

          let invoiceRegime = '' as InvoiceRegimeLabel;

          Object.entries(INVOICE_REGIME).every(([key, value]) => {
            if (response.invoiceRegime === value) {
              invoiceRegime = key as InvoiceRegimeLabel;
              return false;
            }

            return true;
          });

          let paymentMethod = '' as PaymentMethodLabel;

          Object.entries(PAYMENT_METHOD).every(([key, value]) => {
            if (response?.paymentMethod === value) {
              paymentMethod = key as PaymentMethodLabel;
              return false;
            }

            return true;
          });

          return {
            id: response.id ?? 0,
            merchantId: response.merchantId ?? 0,
            cfdi: cfdiLabel,
            cfdiCode: cfdiCatalogId,
            currency: response.currency ?? defaultCurrency,
            email: response.email ?? '',
            invoiceRegime: invoiceRegime,
            name: response.name ?? '',
            paymentForm: paymentFormLabel as PaymentFormLabel,
            paymentFormCode: paymentFormCatalogId as PaymentFormCatalogId,
            paymentMethod: paymentMethod,
            rfc: response.rfc ?? '',
            zipCode: response.zipCode ?? '',
            facPubGral: response.facPubGral || false,
          };
        }),

        catchError(err => {
          if (
            err instanceof HttpErrorResponse &&
            err.status >= 400 &&
            err.error.error.startsWith('Merchant Invoice Data not found')
          ) {
            throw new RuntimeMerchantError(
              'Información de facturación no encontrada con el id proporcionado.',
              'GetInvoiceInfoUseCase::idNotFound'
            );
          }

          throw err;
        }),

        catchError(err => {
          return this.#errorHandler.handle<never>(err);
        }),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);
      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
