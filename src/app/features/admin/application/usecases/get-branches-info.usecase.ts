import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  TemporalService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, of, take } from 'rxjs';
import { BranchUI } from '../../domain/dtos/branch.dto';
import { MerchantBranchesRepository } from '../../domain/repositories/branches-info.repository';

@Injectable({ providedIn: 'root' })
export class GetBranchesInfoUseCase
  implements
    BaseUsecase<number, Observable<{ merchantId: number; data: BranchUI[] }>>
{
  readonly #repository: MerchantBranchesRepository = inject(
    MerchantBranchesRepository
  );
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #temporal = inject(TemporalService);

  execute(
    merchantId: number
  ): Observable<{ merchantId: number; data: BranchUI[] }> {
    const idLoader = this.#loader.show();

    try {
      Guard.againstInvalidNumbers(
        merchantId,
        'GetBranchesInfoUseCase::invalidMerchantId',
        'El id del comercio debe ser un número entero válido.'
      );

      if (merchantId <= 0) {
        throw new RuntimeMerchantError(
          'El id del comercio debe ser un número entero válido mayor de 0.',
          'GetBranchesInfoUseCase::invalidMerchantId'
        );
      }

      return this.#repository.getInfo(merchantId).pipe(
        map(branches => {
          return branches.map(branch => {
            const created = this.#parseIsoDate(branch.created);
            const updated = this.#parseIsoDate(branch.updated);
            const deleted = branch.deleted
              ? this.#parseIsoDate(branch.deleted)
              : null;

            return {
              merchantConfigId: branch.merchantConfigId,
              branchId: branch.branchId,
              branchName: branch.branchName,
              banned: branch.banned,
              created,
              updated,
              deleted,
            };
          });
        }),

        map(branches => branches.sort((a, b) => b.branchId - a.branchId)),

        map(data => ({
          merchantId: Number(merchantId),
          data,
        })),

        catchError(err => {
          return this.#errorHandler.handle(
            err,
            of({ merchantId: 0, data: [] })
          );
        }),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);
      return this.#errorHandler.handle(error, of({ merchantId: 0, data: [] }));
    }
  }

  #parseIsoDate = (date: string): Date | null => {
    try {
      return this.#temporal.fromStringToDate(date);
    } catch (error) {
      if (error instanceof RuntimeMerchantError) {
        console.warn(error.code);
      } else {
        console.warn(error);
      }
      return null;
    }
  };
}
