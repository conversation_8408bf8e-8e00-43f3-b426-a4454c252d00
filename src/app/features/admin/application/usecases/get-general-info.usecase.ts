import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, of, take } from 'rxjs';
import { MerchantGeneralInfoUI } from '../../domain/dtos/general-info.dto';
import { MerchantGeneralInfoRepository } from '../../domain/repositories/general-info.repository';
import { AdminMerchantMapper } from '../services/admin-mapper';

export const defaultGeneralsInfo: MerchantGeneralInfoUI = {
  merchantId: 0,
  mComInfoId: 0,
  mQuestionnaireId: 0,
  merchantEmail: '',
  merchantAddress: '',
  merchantWebsite: '',
  merchantIndustry: '',
  merchantAov: '',
  merchantRevenue: '',
  merchantCategory: '',
  error: null,
};

@Injectable({ providedIn: 'root' })
export class GetGeneralInfoUseCase
  implements BaseUsecase<number, Observable<MerchantGeneralInfoUI>>
{
  readonly #repository: MerchantGeneralInfoRepository = inject(
    MerchantGeneralInfoRepository
  );
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(merchantId: number): Observable<MerchantGeneralInfoUI> {
    const idLoader = this.#loader.show();

    try {
      Guard.againstInvalidNumbers(
        merchantId,
        'GetGeneralInfoUseCase::invalidMerchantId',
        'El id del comercio debe ser un número entero válido.'
      );

      if (merchantId <= 0) {
        throw new RuntimeMerchantError(
          'El id del comercio debe ser un número entero válido mayor de 0.',
          'GetGeneralInfoUseCase::invalidMerchantId'
        );
      }
      return this.#repository.getInfo(merchantId).pipe(
        map(AdminMerchantMapper.fromGeneralsResponseToUI),

        catchError(err => {
          if (
            err instanceof HttpErrorResponse &&
            err.status >= 500 &&
            err.error.error.startsWith('Unable to find')
          ) {
            throw new RuntimeMerchantError(
              'El comercio con el id proporcionado no fue encontrado.',
              'GetGeneralInfoUseCase::idNotFound'
            );
          }

          throw err;
        }),

        catchError(err =>
          this.#errorHandler.handle(err, of(defaultGeneralsInfo))
        ),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(error, of(defaultGeneralsInfo));
    }
  }
}
