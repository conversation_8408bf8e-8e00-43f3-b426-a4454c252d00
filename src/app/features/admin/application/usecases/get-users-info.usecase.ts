import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, throwError } from 'rxjs';
import {
  OperatorsUIList,
  serializeUserResponse,
  toOperatorsList,
} from '../../domain/dtos/user.dto';
import { MerchantOperatorsRepository } from '../../domain/repositories/users-info.repository';

export const roleMapper = new Map<string, string>();
roleMapper.set('ROLE_SELL_AGENT', 'Vendedor(a)');
roleMapper.set('ROLE_MANAGER', 'Gerente');

@Injectable({ providedIn: 'root' })
export class GetUsersInfoUseCase
  implements BaseUsecase<number, Observable<OperatorsUIList>>
{
  readonly #repository: MerchantOperatorsRepository = inject(
    MerchantOperatorsRepository
  );
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(merchantId: number): Observable<OperatorsUIList> {
    const idLoader = this.#loader.show();

    try {
      Guard.againstInvalidNumbers(
        merchantId,
        'GetUsersInfoUseCase::invalidMerchantId',
        'El id del comercio debe ser un número entero válido.'
      );

      if (merchantId <= 0) {
        throw new RuntimeMerchantError(
          'El id del comercio debe ser un número entero válido mayor de 0.',
          'GetUsersInfoUseCase::invalidMerchantId'
        );
      }

      return this.#repository.getInfo(merchantId).pipe(
        map(users => users.map(u => serializeUserResponse(merchantId, u))),

        map(users => users.slice().sort((a, b) => a.idAccount - b.idAccount)),

        map(toOperatorsList),

        catchError(err => this.#errorHandler.handle<never>(err)),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
