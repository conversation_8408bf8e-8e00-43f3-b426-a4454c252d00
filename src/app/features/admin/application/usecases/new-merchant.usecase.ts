import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, EMPTY, finalize, Observable, take, tap } from 'rxjs';
import {
  alreadyRegisteredEmailMessage,
  successNewMerchantMessage,
} from '../../../shared/domain/messages-text';
import { NewMerchantRequest } from '../../domain/dtos/new-merchant-request.dto';
import { NewMerchantResponse } from '../../domain/dtos/new-merchant-response.dto';
import { Prospect } from '../../domain/entities/prospect';
import { NewMerchantRespository } from '../../domain/repositories/new-merchant.repository';
import { AdminMerchantMapper } from '../services/admin-mapper';

@Injectable({ providedIn: 'root' })
export class NewMerchantUseCase
  implements BaseUsecase<Prospect, Observable<NewMerchantResponse>>
{
  readonly #repository: NewMerchantRespository<
    NewMerchantRequest,
    Observable<NewMerchantResponse>
  > = inject(NewMerchantRespository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #useCaseError = inject(UseCaseErrorHandler);

  execute(args: Prospect): Observable<NewMerchantResponse> {
    const idLoader = this.#loader.show();

    try {
      const request = AdminMerchantMapper.fromUIToRequest(args);

      return this.#repository.create(request).pipe(
        tap(response => {
          this.#notifier.success({
            title: successNewMerchantMessage,
            message: `Merchant ID: ${response.id}`,
          });
        }),
        catchError((err: unknown) => {
          if (
            err instanceof HttpErrorResponse &&
            err.status >= 400 &&
            err.error &&
            err.error.error &&
            err.error.error.includes(
              'Error: El correo ya se encuentra registrado'
            )
          ) {
            throw new RuntimeMerchantError(
              alreadyRegisteredEmailMessage,
              'NewMerchantUseCase::registeredEmail'
            );
          }

          throw err;
        }),
        catchError(err => {
          return this.#useCaseError.handle(err, EMPTY);
        }),
        take(1),
        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#useCaseError.handle(error, EMPTY);
    }
  }
}
