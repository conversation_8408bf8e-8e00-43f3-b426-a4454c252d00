import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, of, take } from 'rxjs';
import {
  SearchByRepositoryRequest,
  SearchByRepositoryResponse,
} from '../../domain/dtos/search.dto';
import { SearchMerchantByRepository } from '../../domain/repositories/search-merchant-by.repository';

@Injectable({
  providedIn: 'root',
})
export class SearchByIdOrNameUseCase
  implements
    BaseUsecase<
      SearchByRepositoryRequest,
      Observable<SearchByRepositoryResponse[]>
    >
{
  readonly #repository: SearchMerchantByRepository<
    SearchByRepositoryRequest,
    Observable<SearchByRepositoryResponse[]>
  > = inject(SearchMerchantByRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(
    args: SearchByRepositoryRequest
  ): Observable<SearchByRepositoryResponse[]> {
    const idLoader = this.#loader.show();

    try {
      if (!args.id && !args.name) {
        throw new RuntimeMerchantError(
          'Ingrese un id o un nombre para realizar la búsqueda.',
          'SearchByUsecase::invalidSearchRequest'
        );
      }

      if (args?.name && args?.name.length < 3) {
        throw new RuntimeMerchantError(
          'Ingrese al menos 3 caracteres para realizar la búsqueda por nombre.',
          'SearchByUsecase::invalidSearchRequest'
        );
      }

      return this.#repository.searchBy(args).pipe(
        map(results => {
          if (!results || results.length === 0) {
            throw new RuntimeMerchantError(
              'No encontramos merchants que cumplan con los criterios de búsqueda.',
              'SearchByUsecase::searchNotFound',
              'SearchByIdOrNameUseCase::execute'
            );
          }

          return results;
        }),
        map(results =>
          results.splice(0).sort((a, b) => b.merchantId - a.merchantId)
        ),
        catchError(err => {
          return this.#errorHandler.handle(err, of([]));
        }),
        take(1),
        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);
      return this.#errorHandler.handle(error, of([]));
    }
  }
}
