import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  finalize,
  map,
  Observable,
  take,
  tap,
  throwError,
} from 'rxjs';
import { AccountEditionRequest } from '../../domain/dtos/account.dto';
import { MerchantAccountRepository } from '../../domain/repositories/account-info.repository';

@Injectable()
export class UpdateAccountUsecase
  implements
    BaseUsecase<AccountEditionRequest, Observable<AccountEditionRequest>>
{
  readonly #repository = inject(MerchantAccountRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: AccountEditionRequest): Observable<AccountEditionRequest> {
    const idLoader = this.#loader.show();

    try {
      const errors = ['merchantId', 'bankName', 'bankAccount']
        .map(key => {
          const result = Guard.againstNullOrUndefined(args, key);

          return result.succeeded ? null : result.message;
        })
        .filter(Boolean);

      if (errors.length > 0) {
        throw new RuntimeMerchantError(
          `Error detectado: ${errors.join(', ')}`,
          'UpdateAccountUsecase::execute::invalidArgs'
        );
      }

      Guard.againstInvalidNumbers(args.merchantId, 'merchantId');

      if (args.merchantId <= 0) {
        throw new RuntimeMerchantError(
          'El id del comercio debe ser un número entero válido mayor de 0.',
          'UpdateAccountUsecase::execute::invalidMerchantId'
        );
      }

      if (args.bankName.trim().length === 0) {
        throw new RuntimeMerchantError(
          'El nombre del banco no puede estar vacío',
          'UpdateAccount::execute::emptyBankName'
        );
      }

      const bankAccount = args.bankAccount.trim();

      if (bankAccount.length === 0) {
        throw new RuntimeMerchantError(
          'El número de cuenta no puede estar vacío',
          'UpdateAccount::execute::emptyBankAccount'
        );
      }

      if (bankAccount.length > 0 && bankAccount.length !== 18) {
        throw new RuntimeMerchantError(
          'El número de cuenta debe tener 18 dígitos',
          'UpdateAccount::execute::invalidBankAccountLength'
        );
      }

      return this.#repository.updateOneBy(args).pipe(
        tap(() => {
          this.#notifier.success({
            title: 'Cuenta actualizada',
          });
        }),

        map(() => ({ ...args })),

        catchError(err => this.#errorHandler.handle<never>(err)),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
