import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, take, throwError } from 'rxjs';
import { MdrUIDto, MdrUpdateRequest } from '../../domain/dtos/mdr.dto';
import { MerchantMdrInfoRepository } from '../../domain/repositories/mdr-info.repository';

@Injectable()
export class UpdateMdrUsecase
  implements BaseUsecase<Partial<MdrUIDto>, Observable<MdrUIDto>>
{
  readonly #repository = inject(MerchantMdrInfoRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: Partial<MdrUIDto>): Observable<MdrUIDto> {
    const idLoader = this.#loader.show();

    try {
      const request = MdrUpdateRequest.create(args);

      return this.#repository.updateInfo(request).pipe(
        take(1),
        map(() => args as MdrUIDto),
        catchError(error => this.#errorHandler.handle<never>(error)),
        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
