import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, throwError } from 'rxjs';
import { MdrList, serializeMdrResponseDto } from '../../domain/dtos/mdr.dto';
import { MerchantMdrInfoRepository } from '../../domain/repositories/mdr-info.repository';

export const defaultMdrInfo = {
  5: {
    id: 0,
    merchantId: 0,
    feeOps: 0.0,
    feePct: 0.0,
    promoFee: 0.0,
    promoFeeEndDate: null,
    promoFeeEndIsoDate: null,
    updatedAt: null,
    installmentFrequency: 5,
    installmentFrequencyId: 3,
  },
} satisfies MdrList;

@Injectable({ providedIn: 'root' })
export class GetMdrInfoUseCase
  implements BaseUsecase<number, Observable<MdrList>>
{
  readonly #repository: MerchantMdrInfoRepository = inject(
    MerchantMdrInfoRepository
  );
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(merchantId: number): Observable<MdrList> {
    const idLoader = this.#loader.show();

    try {
      Guard.againstInvalidNumbers(
        merchantId,
        'GetMdrInfoUseCase::invalidMerchantId',
        'El id del comercio debe ser un número entero válido.'
      );

      if (merchantId <= 0) {
        throw new RuntimeMerchantError(
          'El id del comercio debe ser un número entero válido mayor de 0.',
          'GetMdrInfoUseCase::invalidMerchantId'
        );
      }

      return this.#repository.getInfo(merchantId).pipe(
        map(serializeMdrResponseDto),

        catchError(err => {
          if (
            err instanceof HttpErrorResponse &&
            err.status >= 500 &&
            err.error.error.startsWith('Unable to find')
          ) {
            throw new RuntimeMerchantError(
              'El comercio con el id proporcionado no fue encontrado.',
              'GetMdrInfoUseCase::idNotFound'
            );
          }

          throw err;
        }),

        catchError(err => {
          return this.#errorHandler.handle<never>(err);
        }),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);
      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
