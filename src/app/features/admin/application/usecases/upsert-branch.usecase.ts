import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, take, tap, throwError } from 'rxjs';
import { BranchResponse } from '../../domain/dtos/branch.dto';
import { MerchantBranchesRepository } from '../../domain/repositories/branches-info.repository';

@Injectable()
export class UpsertBranchUseCase extends BaseUsecase<
  {
    merchantId: number;
    name: string;
    id?: number;
    active?: boolean;
  },
  Observable<any>
> {
  readonly #repository = inject(MerchantBranchesRepository);
  readonly #usecaseErrror = inject(UseCaseErrorHandler);
  readonly #notifier = inject(NotifierService);
  readonly #loader = inject(LoaderService);

  execute(args: {
    merchantId: number;
    name: string;
    id?: number;
    active?: boolean;
  }): Observable<BranchResponse | null> {
    const idLoader = this.#loader.show();

    try {
      if (!Guard.againstNullOrUndefined(args, 'name').succeeded) {
        throw new RuntimeMerchantError(
          'El nombre de la storefront es requerido',
          this.#entityName() + '::execute::nullName'
        );
      }

      if (!Guard.againstNullOrUndefined(args, 'merchantId').succeeded) {
        throw new RuntimeMerchantError(
          'El merchantId es requerido',
          this.#entityName() + '::execute::nullMerchantId'
        );
      }

      const sanitizedName = args.name.trim();

      if (sanitizedName.length < 3) {
        throw new RuntimeMerchantError(
          'El nombre de la storefront debe tener al menos 3 caracteres',
          this.#entityName() + '::execute::shortName'
        );
      }

      const request: {
        merchantId: number;
        name: string;
        id?: number;
        active?: boolean;
      } = {
        name: sanitizedName,
        merchantId: args.merchantId,
      };

      const hasId = Guard.againstNullOrUndefined(args, 'id').succeeded;

      if (hasId) {
        request.id = args.id;
      }

      if (hasId && Guard.againstNullOrUndefined(args, 'active').succeeded) {
        request.active = args.active;
      }

      return this.#repository.upsertOne(request).pipe(
        tap(() => {
          this.#notifier.success({
            title: `Storefront ${request.id ? 'actualizada' : 'creada'} exitosamente`,
          });
        }),

        catchError(error => {
          if (
            error instanceof HttpErrorResponse &&
            error.error?.error === 'Merchant not found' &&
            error.status === 400
          ) {
            this.#notifier.error({
              title: 'Detectamos un error',
              message: 'Revise la configuración del merchant',
            });
            throw error;
          }

          return this.#usecaseErrror.handle<any>(error);
        }),

        finalize(() => {
          this.#loader.hide(idLoader);
        }),

        take(1)
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#usecaseErrror.handle(
        error,
        throwError(() => error)
      );
    }
  }

  #entityName(): string {
    return this.constructor.name ?? 'UpsertBranchUseCase';
  }
}
