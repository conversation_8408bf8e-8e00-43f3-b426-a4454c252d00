import { Observable } from 'rxjs';
import {
  OperatorRepositoryRequestDto,
  OperatorResponse,
} from '../dtos/user.dto';

export abstract class MerchantOperatorsRepository {
  abstract getInfo(merchantId: number): Observable<OperatorResponse[]>;
  abstract createOne(
    user: OperatorRepositoryRequestDto
  ): Observable<OperatorResponse>;
  abstract updateOne(
    user: Partial<OperatorRepositoryRequestDto>
  ): Observable<OperatorResponse>;
}
