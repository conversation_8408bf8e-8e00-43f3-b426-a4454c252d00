import { Observable } from 'rxjs';
import { MerchantBasicsResponse } from '../dtos/basics.dto';

export abstract class MerchantBasicInfoRepository {
  abstract getInfo(merchantId: number): Observable<MerchantBasicsResponse>;
  abstract updateByMerchantId(request: {
    merchantId: number;
    merchantName?: string;
    merchantStatus?: string;
    catIntegrationType?: string;
  }): Observable<void>;
}
