import { Observable } from 'rxjs';
import {
  NewContactDto,
  RetrievedContactResponse,
  UpdateContactDto,
} from '../dtos/contact.dto';

export abstract class MerchantContactsRepository {
  abstract getInfo(merchantId: number): Observable<RetrievedContactResponse[]>;
  abstract createOne(
    request: NewContactDto
  ): Observable<RetrievedContactResponse>;
  abstract updateOne(
    request: UpdateContactDto
  ): Observable<RetrievedContactResponse>;
}
