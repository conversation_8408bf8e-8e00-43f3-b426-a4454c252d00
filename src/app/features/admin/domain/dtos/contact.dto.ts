import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { UserEmail } from '../../../login/domain/entities/user-email';

export const BUSINESS_AREA = {
  Finance: 1,
  Marketing: 2,
  Support: 3,
  IT: 4,
  Commercial: 5,
} as const;

export type BusinessArea = keyof typeof BUSINESS_AREA;
export type BusinessAreaId = (typeof BUSINESS_AREA)[BusinessArea];

export interface RetrievedContactResponse {
  id: number;
  email: string;
  role: string;
  businessArea: string;
  phone: string | null;
  merchantId: number;
  businessAreaId: number;
  createdAt: string; // date string
  updateAt: string; // date string
}

export interface NewContactDto {
  email: string;
  role: string;
  phone: string;
  businessAreaId: BusinessAreaId;
  merchantId: number;
  updateAt?: string; // date string
}

export interface CreateContactDto extends Partial<NewContactDto> {
  id: number;
}

export interface UpdateContactDto extends Partial<NewContactDto> {
  id: number;
}

export interface ContactUI {
  id?: number;
  phone: string;
  email: string;
  role: string;
  businessArea: BusinessArea;
  merchantId: number;
  updateAt?: string; // date string
}

export const serializeRetrievedContactResponse = (
  args: RetrievedContactResponse
): ContactUI => {
  const email = UserEmail.create(args.email);
  const businessArea = args.businessArea as BusinessArea;

  return {
    id: args.id,
    email: email.value,
    phone: args.phone ?? '',
    role: args.role,
    businessArea: businessArea || '',
    merchantId: args.merchantId,
    updateAt: args.updateAt,
  };
};

export const fromRepositoryToUI = (args: NewContactDto): ContactUI => {
  const businessArea = Object.entries(BUSINESS_AREA).find(
    ([, val]) => val === args.businessAreaId
  );

  return {
    email: args.email,
    role: args.role,
    phone: args.phone,
    businessArea: businessArea
      ? (businessArea[0] as BusinessArea)
      : ('' as any),
    merchantId: args.merchantId,
    updateAt: args.updateAt,
  };
};

export const fromUIToRepository = (args: ContactUI): NewContactDto => {
  const errors = ['merchantId', 'email', 'role', 'businessArea']
    .map(key => {
      const result = Guard.againstNullOrUndefined(args, key);

      return result.succeeded ? null : result.message;
    })
    .filter(Boolean);

  if (errors.length > 0) {
    throw new RuntimeMerchantError(
      'Detectamos errores en la creacion de un nuevo contacto:: ' +
        errors.join(', '),
      'Contact::fromUIToRepository::emptyFields'
    );
  }

  const email = UserEmail.create(args.email);
  const businessAreaId = BUSINESS_AREA[args.businessArea];

  if (!businessAreaId) {
    throw new RuntimeMerchantError(
      'El área de negocio no es válida',
      'Contact::fromUIToRepository::emptyBusinessAreaId'
    );
  }

  if (args.merchantId <= 0) {
    throw new RuntimeMerchantError(
      'El id del comercio no es válido',
      'Contact::fromUIToRepository::emptyMerchantId'
    );
  }

  return {
    email: email.value,
    role: args.role,
    phone: args.phone,
    businessAreaId: BUSINESS_AREA[args.businessArea],
    merchantId: args.merchantId,
    updateAt: args.updateAt,
  };
};

export const fromUIToUpdateRepository = (args: ContactUI): UpdateContactDto => {
  Guard.againstInvalidNumbers(
    args.merchantId,
    'Contact::fromUIToUpdateRepository',
    'El id del comercio debe ser un número entero válido.'
  );

  let email = '';

  if (args.email) {
    email = UserEmail.create(args.email).value;
  }
  const businessAreaId = BUSINESS_AREA[args.businessArea];

  if (!businessAreaId) {
    throw new RuntimeMerchantError(
      'El área de negocio no es válida',
      'Contact::fromUIToUpdateRepository::emptyBusinessAreaId'
    );
  }

  if (args.merchantId <= 0) {
    throw new RuntimeMerchantError(
      'El id del comercio no es válido',
      'Contact::fromUIToUpdateRepository::emptyMerchantId'
    );
  }

  Guard.againstInvalidNumbers(
    args.id,
    'Contact::fromUIToUpdateRepository',
    'El id del contacto debe ser un número entero válido.'
  );

  const final: UpdateContactDto = {
    id: args.id!,
    email,
    role: args.role,
    businessAreaId: BUSINESS_AREA[args.businessArea],
    merchantId: args.merchantId,
    updateAt: args.updateAt,
  };

  if (args.phone && args.phone.length === 10) {
    final.phone = args.phone;
  }

  return final;
};
