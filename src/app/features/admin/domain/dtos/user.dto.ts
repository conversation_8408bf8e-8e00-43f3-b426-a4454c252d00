import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { parseISO } from 'date-fns';
import { UserEmail } from '../../../login/domain/entities/user-email';

export const POS_ROLES = {
  'Vendedor(a)': 'ROLE_SELL_AGENT',
  Gerente: 'ROLE_MANAGER',
} as const;

export type PosRoleLabel = keyof typeof POS_ROLES;
export type PosRole = (typeof POS_ROLES)[PosRoleLabel];

export const DASH_ROLES = {
  PANEL_ADMIN: 'ROLE_PANEL_ADMIN',
  PANEL_SUPPORT: 'ROLE_PANEL_SUPPORT',
  PANEL_FINANCE: 'ROLE_PANEL_FINANCE',
  PANEL_MARKETING: 'ROLE_PANEL_MARKETING',
  PANEL_MANAGER: 'ROLE_PANEL_MANAGER',
} as const;

export type DashRoleLabel = keyof typeof DASH_ROLES;
export type DashRole = (typeof DASH_ROLES)[DashRoleLabel];

export interface AssignedBranch {
  id: number;
  name: string;
}

export interface OperatorResponse {
  idAccount: number;
  username: string;
  role: string;
  userStatus: string;
  userEmail: null | string;
  platform: string;
  branches: null | AssignedBranch[];
  createdAt: string | null;
  updatedAt: string | null;
  deletedAt: string | null;
}

export interface OperatorUIDto {
  merchantId: number;
  branches: null | AssignedBranch[];
  idAccount: number;
  role: PosRoleLabel | DashRoleLabel;
  username: string;
  platform: string;
  userStatus: string;
  userEmail: string | null;
  createdAt: Date | null;
  updatedAt: Date | null;
  deletedAt: Date | null;
}

export interface OperatorWithPasswordDto {
  merchantId: number;
  role: PosRoleLabel | DashRoleLabel;
  userEmail: string | null;
  username: string;
  password: string;
  branchesId?: number[] | null;
  idAccount?: number;
  active?: boolean;
}

export interface OperatorWithPasswordUIDto
  extends Partial<OperatorWithPasswordDto> {
  hasConfirmation: boolean;
  platform?: 'POSUI' | 'Panel';
  active?: boolean;
  idAccount?: number;
}

export interface OperatorRepositoryRequestDto {
  merchantId: number;
  username: string;
  password: string;
  role: PosRole | DashRole;
  email?: string;
  branchesId?: number[] | null; // mandatory only for PosRole
  id?: number;
  status?: 'ACTIVE' | 'BANNED';
}

export type OperatorsUIList = {
  data: {
    [key: number]: OperatorUIDto;
  };
  merchantId?: number;
  length: number;
};

export const parseIsoDate = (date: string | null): Date | null => {
  if (!date) {
    return null;
  }

  const parsed = parseISO(date);

  try {
    Guard.againstInvalidDate({
      date: parsed,
      errorMessage: 'La fecha proporcionada no es válida.',
      origin: 'GetUsersInfoUseCase::parseIsoDate',
    });

    return parsed;
  } catch (error) {
    if (error instanceof RuntimeMerchantError) {
      console.warn(error.code);
    } else {
      console.warn(error);
    }
    return null;
  }
};

export const serializeUserResponse = (
  merchantId: number,
  user: OperatorResponse
): OperatorUIDto => {
  const created = parseIsoDate(user.createdAt);
  const updated = parseIsoDate(user.updatedAt);
  const deleted = parseIsoDate(user.deletedAt);

  const role =
    (Object.keys(POS_ROLES).find(
      key => user.role === POS_ROLES[key as PosRoleLabel]
    ) as PosRoleLabel) ??
    (Object.keys(DASH_ROLES).find(
      key => user.role === DASH_ROLES[key as DashRoleLabel]
    ) as DashRoleLabel) ??
    '';

  return {
    merchantId,
    idAccount: user.idAccount,
    username: user.username,
    role,
    userStatus: user.userStatus,
    userEmail: user.userEmail,
    platform: user.platform,
    branches: user.branches,
    createdAt: created,
    updatedAt: updated,
    deletedAt: deleted,
  };
};

export const toOperatorsList = (users: OperatorUIDto[]): OperatorsUIList => {
  const data = users.reduce(
    (acc, user) => {
      acc[user.idAccount] = user;
      return acc;
    },
    {} as { [key: number]: OperatorUIDto }
  );

  const parsed: OperatorsUIList = {
    data,
    length: users.length,
  };

  if (users.length > 0) {
    parsed.merchantId = users[0].merchantId;
  }

  return parsed;
};

export const toRepositoryUpdateRequest = (
  args: OperatorWithPasswordDto
): Partial<OperatorRepositoryRequestDto> => {
  const fieldsToValidate = ['merchantId', 'idAccount'];

  const errors = fieldsToValidate
    .map(key => {
      const result = Guard.againstNullOrUndefined(args, key);

      return result.succeeded ? null : result.message;
    })
    .filter(Boolean);

  if (errors.length > 0) {
    throw new RuntimeMerchantError(
      'Detectamos errores:: ' + errors.join(', '),
      'toRepositoryRequest::emptyArgs'
    );
  }
  const merchantId = args.merchantId as number;

  Guard.againstInvalidNumbers(
    merchantId,
    'toRepositoryRequest::merchantId',
    'El id del comercio debe ser un número entero válido.'
  );

  if (merchantId <= 0) {
    throw new RuntimeMerchantError(
      'El id del comercio debe ser un número entero válido mayor de 0.',
      'toRepositoryRequest::merchantId'
    );
  }

  const idAccount = args.idAccount as number;

  Guard.againstInvalidNumbers(
    idAccount,
    'toRepositoryRequest::idAccount',
    'El id de la cuenta debe ser un número entero válido.'
  );

  if (idAccount <= 0) {
    throw new RuntimeMerchantError(
      'El id de la cuenta debe ser un número entero válido mayor de 0.',
      'toRepositoryRequest::idAccount'
    );
  }

  const request: Partial<OperatorRepositoryRequestDto> = {
    merchantId,
    id: idAccount,
  };

  const hasPassword = Object.prototype.hasOwnProperty.call(args, 'password');
  const emptyPassword = !Guard.againstEmptyValue(
    args,
    'password',
    'toRepositoryRequest::password'
  ).succeeded;

  if (hasPassword && !emptyPassword) {
    request.password = args.password.trim();
  }

  const hasUsername = Object.prototype.hasOwnProperty.call(args, 'username');
  const emptyUsername = !Guard.againstEmptyValue(
    args,
    'username',
    'toRepositoryRequest::username'
  ).succeeded;

  if (hasUsername && !emptyUsername) {
    request.username = args.username;
  }

  const hasUserEmail = Object.prototype.hasOwnProperty.call(args, 'userEmail');
  const emptyUserEmail = !Guard.againstEmptyValue(
    args,
    'userEmail',
    'toRepositoryRequest::userEmail'
  ).succeeded;
  let email = '';

  try {
    email = UserEmail.create(args.userEmail).value;
  } catch (error) {
    email = '';
  }

  if (hasUserEmail && !emptyUserEmail && email) {
    request.email = email;
  }

  const hasActive = Object.prototype.hasOwnProperty.call(args, 'active');

  if (hasActive) {
    request.status = args.active ? 'ACTIVE' : 'BANNED';
  }

  const hasRole = Object.prototype.hasOwnProperty.call(args, 'role');
  const emptyRole = !Guard.againstEmptyValue(
    args,
    'role',
    'toRepositoryRequest::role'
  ).succeeded;

  if (hasRole && !emptyRole) {
    const roleLabel = args.role;
    let role: PosRole | DashRole | null = null;

    if (POS_ROLES[roleLabel as PosRoleLabel]) {
      role = POS_ROLES[roleLabel as PosRoleLabel];
    } else if (DASH_ROLES[roleLabel as DashRoleLabel]) {
      role = DASH_ROLES[roleLabel as DashRoleLabel];
    }

    if (role) {
      request.role = role;
    }
  }

  const hasBranches =
    Object.prototype.hasOwnProperty.call(args, 'branchesId') &&
    Array.isArray(args.branchesId) &&
    args.branchesId.length > 0;

  if (hasBranches) {
    request.branchesId = args.branchesId;
  }

  return request;
};

export const toRepositoryCreateRequest = (
  args: OperatorWithPasswordDto
): OperatorRepositoryRequestDto => {
  const fieldsToValidate = ['merchantId', 'username', 'role', 'password'];

  const errors = fieldsToValidate
    .map(key => {
      const result = Guard.againstNullOrUndefined(args, key);

      return result.succeeded ? null : result.message;
    })
    .filter(Boolean);

  if (errors.length > 0) {
    throw new RuntimeMerchantError(
      'Detectamos errores:: ' + errors.join(', '),
      'toRepositoryRequest::emptyArgs'
    );
  }

  if (
    !Guard.againstEmptyValue(args, 'password', 'toRepositoryRequest::password')
      .succeeded
  ) {
    throw new RuntimeMerchantError(
      'El password es requerido',
      'toRepositoryRequest::emptyPassword'
    );
  }

  if (
    !Guard.againstEmptyValue(args, 'username', 'toRepositoryRequest::username')
      .succeeded
  ) {
    throw new RuntimeMerchantError(
      'El username es requerido',
      'toRepositoryRequest::emptyUsername'
    );
  }

  const merchantId = args.merchantId as number;

  Guard.againstInvalidNumbers(
    merchantId,
    'toRepositoryRequest::merchantId',
    'El id del comercio debe ser un número entero válido.'
  );

  if (merchantId <= 0) {
    throw new RuntimeMerchantError(
      'El id del comercio debe ser un número entero válido mayor de 0.',
      'toRepositoryRequest::merchantId'
    );
  }

  let email: string | undefined;
  if (args.userEmail) {
    email = UserEmail.create(args.userEmail as string).value;
  }

  const roleLabel = args.role;
  let platform: 'POSUI' | 'Panel' | null = null;
  let role: PosRole | DashRole | null = null;

  if (POS_ROLES[roleLabel as PosRoleLabel]) {
    platform = 'POSUI';
    role = POS_ROLES[roleLabel as PosRoleLabel];
  } else if (DASH_ROLES[roleLabel as DashRoleLabel]) {
    platform = 'Panel';
    role = DASH_ROLES[roleLabel as DashRoleLabel];
  }

  if (!role) {
    throw new RuntimeMerchantError(
      'El rol proporcionado no es válido',
      'toRepositoryRequest::invalidRole'
    );
  }

  if (
    platform === 'POSUI' &&
    (!Object.prototype.hasOwnProperty.call(args, 'branchesId') ||
      !Array.isArray(args.branchesId) ||
      args.branchesId.length < 0)
  ) {
    throw new RuntimeMerchantError(
      'Una selección de tiendas es requerida para el rol que esta intentando crear',
      'toRepositoryRequest::emptyBranchesId::pos'
    );
  }

  const request: OperatorRepositoryRequestDto = {
    role,
    merchantId,
    email,
    password: args.password as string,
    username: args.username as string,
    id: args.idAccount,
  };

  if (platform === 'POSUI') {
    request.branchesId = args.branchesId;
  }

  return request;
};
