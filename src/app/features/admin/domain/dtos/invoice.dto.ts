export const PAYMENT_FORM = {
  '03: Transferencia electrónica de fondos': 3,
  '17: Compensación': 12,
  '27: A Satisfacción del Acredor': 17,
} as const;

export type PaymentFormLabel = keyof typeof PAYMENT_FORM;
export type PaymentFormCatalogId = (typeof PAYMENT_FORM)[PaymentFormLabel];

export const CFDI = {
  'G03: Gastos en General': 15,
  'S01: Sin Obligaciones Fiscales': 16,
} as const;

export type CfdiLabel = keyof typeof CFDI;
export type CfdiCatalogId = (typeof CFDI)[CfdiLabel];

export const PAYMENT_METHOD = {
  'Pago en Una Exhibición (PUE)': 'PUE',
  'Pago por Definir (PPD)': 'PPD',
} as const;

export type PaymentMethodLabel = keyof typeof PAYMENT_METHOD;
export type PaymentMethodId = (typeof PAYMENT_METHOD)[PaymentMethodLabel];

export const INVOICE_REGIME = {
  '601 - General de Ley <PERSON>as <PERSON>': '601',
  '606 - Arrendamiento': '606',
  '607 - Régimen de Enajenación o Adquisición de Bienes': '607',
  '608 - Demás ingresos': '608',
  '612 - Personas Físicas con Actividades Empresariales y Profesionales': '612',
  '621 - Incorporación Fiscal': '621',
  '625 - Régimen de las Actividades Empresariales con ingresos a través de Plataformas Tecnológicas':
    '625',
  '626 - Régimen Simplificado de Confianza': '626',
  '603 - Personas Morales con Fines no Lucrativos': '603',
  '605 - Sueldos y Salarios e Ingresos Asimilados a Salarios': '605',
  '609 - Consolidación': '609',
  '610 - Residentes en el Extranjero sin Establecimiento Permanente en México':
    '610',
  '611 - Ingresos por Dividendos (socios y accionistas)': '611',
  '614 - Ingresos por intereses': '614',
  '615 - Régimen de los ingresos por obtención de premios': '615',
  '616 - Sin obligaciones fiscales': '616',
  '620 - Sociedades Cooperativas de Producción que optan por diferir sus ingresos':
    '620',
  '622 - Actividades Agrícolas, Ganaderas, Silvícolas y Pesqueras': '622',
  '623 - Opcional para Grupos de Sociedades': '623',
  '624 - Coordinados': '624',
  '628 - Hidrocarburos': '628',
  '629 - De los Regímenes Fiscales Preferentes y de las Empresas Multinacionales':
    '629',
  '630 - Enajenación de acciones en bolsa de valores': '630',
} as const;

export type InvoiceRegimeLabel = keyof typeof INVOICE_REGIME;
export type InvoiceRegimeCatalogId =
  (typeof INVOICE_REGIME)[InvoiceRegimeLabel];

// Las interfaces se mantienen igual ya que su estructura no cambia
export interface MerchantInvoiceResponse {
  id: number;
  merchantId: number;
  rfc?: string;
  name?: string;
  email?: string;
  paymentMethod?: PaymentMethodId;
  zipCode?: string;
  invoiceRegime?: InvoiceRegimeCatalogId;
  currency?: string;
  catalogPaymentForm: Catalog;
  catalogCfdi: Catalog;
  facPubGral: boolean;
}

export interface InvoiceUI {
  merchantId: number;
  id: number;
  rfc: string;
  email: string;
  name: string;
  paymentMethod: PaymentMethodLabel;
  zipCode: string;
  invoiceRegime: InvoiceRegimeLabel;
  currency: string;
  paymentForm: PaymentFormLabel;
  paymentFormCode: PaymentFormCatalogId;
  cfdi: CfdiLabel;
  cfdiCode: CfdiCatalogId;
  facPubGral: boolean;
}

export interface InvoiceRepositoryRequest {
  merchantId: number;
  paymentFormId: PaymentFormCatalogId;
  cfdiId: CfdiCatalogId;
  currency: string;
  email: string;
  paymentMethodId: PaymentMethodId;
  zipCode: string;
  invoiceRegimeId: InvoiceRegimeCatalogId;
  rfc: string;
  name: string;
  facPubGral: boolean;
}

export interface Catalog {
  id: number;
  code: string | null;
  name: string | null;
}
