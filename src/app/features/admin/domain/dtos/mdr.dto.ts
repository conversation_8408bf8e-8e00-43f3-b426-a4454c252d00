import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';

export const INSTALLMENTS = {
  1: 1,
  2: 3,
  3: 5,
  4: 8,
} as const;

export type InstallmentId = keyof typeof INSTALLMENTS;
export type InstallmentFrequency = (typeof INSTALLMENTS)[InstallmentId];

export class Mdr {
  id: number;
  feeOps: number; //decimal number
  feePct: number; //decimal number
  merchantId: number;
  promoFee: number; //decimal number
  promoFeeEndDate: string; // string date
  updated: string; // string date
  installmentFrequencyId: InstallmentId;
}

export class MdrUpdateRequest {
  readonly id: number;
  readonly feeOps: number | null;
  readonly feePct: number | null; // decimal between 0 and 1
  readonly merchantId: number;
  readonly promoFee: number | null; // decimal between 0 and 1
  readonly promoFeeEndDate: string | null; // iso date
  readonly installmentFrequencyId: InstallmentId;

  private constructor(data: MdrUpdateRequest) {
    this.id = data.id;
    this.feeOps = data.feeOps;
    this.feePct = data.feePct;
    this.merchantId = data.merchantId;
    this.promoFee = data.promoFee;
    this.promoFeeEndDate = data.promoFeeEndDate;
    this.installmentFrequencyId = data.installmentFrequencyId;
  }

  static create(data: Partial<MdrUIDto>): MdrUpdateRequest {
    const feeOps = data.feeOps ? data.feeOps : 0;
    // undo the percentage conversion
    const feePct = data.feePct ? data.feePct / 100 : 0;
    // undo the percentage conversion
    const promoFee = data.promoFee ? data.promoFee / 100 : 0;
    // take the iso date because the request is expecting it in that format
    const promoFeeEndDate = data.promoFeeEndIsoDate
      ? data.promoFeeEndIsoDate
      : null;

    if (!data.installmentFrequencyId) {
      throw new RuntimeMerchantError(
        'No se encontró la frecuencia de pagos.',
        'MdrUpdateRequest::installmentFrequencyId'
      );
    }

    if (INSTALLMENTS[data.installmentFrequencyId] == null) {
      throw new RuntimeMerchantError(
        'La frecuencia de pagos no es válida.',
        'MdrUpdateRequest::installmentFrequencyId'
      );
    }

    if (!Guard.againstNullOrUndefined(data, 'id').succeeded) {
      throw new RuntimeMerchantError(
        'El id del MDR no puede ser nulo o indefinido.',
        'MdrUpdateRequest::nullId'
      );
    }

    if (!Guard.againstNullOrUndefined(data, 'merchantId').succeeded) {
      throw new RuntimeMerchantError(
        'El id del comercio no puede ser nulo o indefinido.',
        'MdrUpdateRequest::nullMerchantId'
      );
    }

    Guard.againstInvalidNumbers(
      data.id,
      'MdrUpdateRequest::invalidId',
      'El id del MDR debe ser un número entero válido.'
    );

    Guard.againstInvalidNumbers(
      data.merchantId,
      'MdrUpdateRequest::invalidMerchantId',
      'El id del comercio debe ser un número entero válido.'
    );

    const installmentFrequencyId = data.installmentFrequencyId;
    const merchantId = data.merchantId as number;
    const id = data.id as number;

    const partialUIDto = {
      id,
      feeOps,
      feePct,
      merchantId,
      promoFee,
      promoFeeEndDate,
      installmentFrequencyId,
    };

    return new MdrUpdateRequest(partialUIDto);
  }
}

export type MdrResponseDto = Mdr[];

export interface MdrUIDto {
  id: number;
  merchantId: number;
  feeOps: number | null; //decimal number
  feePct: number | null; //decimal number
  promoFee: number | null; //decimal number
  promoFeeEndDate: string | null; // string date
  promoFeeEndIsoDate: string | null; // iso date
  updatedAt: string | null; // string date
  installmentFrequency: InstallmentFrequency;
  installmentFrequencyId: InstallmentId;
}

export type MdrList = {
  [K in InstallmentFrequency]?: MdrUIDto;
};

export const serializeMdrResponseDto = (response: MdrResponseDto): MdrList => {
  if (!response) {
    throw new RuntimeMerchantError(
      'No se encontraron datos para el comercio proporcionado.',
      'GetMdrInfoUseCase::emptyResponse'
    );
  }

  const result: MdrList = {};

  response.forEach(r => {
    const feePct = Number(r.feePct);
    const promoFee = Number(r.promoFee);
    // do the percentage conversion to show it in the UI
    const feePctPercentage = isNaN(feePct) ? 0 : feePct * 100;
    // do the percentage conversion to show it in the UI
    const promoFeePercentage = isNaN(promoFee) ? 0 : promoFee * 100;
    const isoDate = r.promoFeeEndDate
      ? new Date(r.promoFeeEndDate).toISOString()
      : null;

    result[INSTALLMENTS[r.installmentFrequencyId]] = {
      id: r.id,
      merchantId: r.merchantId,
      feeOps: r.feeOps ?? 0.0,
      feePct: feePctPercentage,
      promoFee: promoFeePercentage,
      promoFeeEndDate: r.promoFeeEndDate ?? null,
      promoFeeEndIsoDate: isoDate,
      updatedAt: r.updated ?? null,
      installmentFrequency: INSTALLMENTS[r.installmentFrequencyId],
      installmentFrequencyId: r.installmentFrequencyId,
    };
  });

  return result;
};
