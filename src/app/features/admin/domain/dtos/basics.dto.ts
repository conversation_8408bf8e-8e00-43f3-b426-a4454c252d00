import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { invalidMerchantUpdateStatusError } from '../../../shared/domain/messages-text';
import { INTEGRATION_TYPES } from '../entities/integration-types';

export interface MerchantBasicsResponse {
  id: number;
  idMComInfo: number;
  name: string;
  status: string;
  intType: string;
  created: string; // Date;
  updated: string; // Date;
}

export interface MerchantBasicsUI {
  merchantId: number;
  mComInfoId: number;
  merchantName: string;
  status: string;
  intType: string;
  createdAt: Date | null;
  updatedAt: Date | null;
  error: unknown;
}

export interface MerchantUpdateBasicsUIRequest {
  merchantId: number;
  merchantName: string | null;
  status: string | null;
  intType: string | null;
}

export interface MerchantUpdateBasicsRepositoryRequest {
  merchantId: number;
  merchantName?: string;
  merchantStatus?: string;
  catIntegrationType?: string;
}

export const validStatusToUpdateMerchant = Object.seal(
  Object.freeze([
    'PENDING_INFO',
    'FREEZE',
    'CREATED',
    'MIGRATED',
    'TRIAL_PERIOD',
    'PENDING_APPROVAL',
    'BLOCKED',
    'REJECTED',
    'CONFIGURATION',
    'CHURN',
    'APPROVED',
  ])
);

export function fromBasicsRequestUIToRepository(
  request: MerchantUpdateBasicsUIRequest
): MerchantUpdateBasicsRepositoryRequest {
  if (!Guard.againstNullOrUndefined(request, 'merchantId').succeeded) {
    throw new RuntimeMerchantError(
      'El id del comercio es requerido para actualizar la información básica.',
      'fromBasicsRequestUIToRepository::emptyMerchantId'
    );
  }

  if (!request.merchantName && !request.status && !request.intType) {
    throw new RuntimeMerchantError(
      'No hay información básica para actualizar.',
      'fromBasicsRequestUIToRepository::emptyFields'
    );
  }

  if (
    request.intType &&
    INTEGRATION_TYPES.findIndex(type => type.value === request.intType) < 0
  ) {
    throw new RuntimeMerchantError(
      'Tipo de integración inválido.',
      'fromBasicsRequestUIToRepository::invalidIntegrationType'
    );
  }

  if (request.status && !validStatusToUpdateMerchant.includes(request.status)) {
    throw new RuntimeMerchantError(
      invalidMerchantUpdateStatusError,
      'fromBasicsRequestUIToRepository::invalidStatus'
    );
  }

  const repoReq: MerchantUpdateBasicsRepositoryRequest = {
    merchantId: request.merchantId,
  };

  if (request.merchantName) {
    repoReq.merchantName = request.merchantName;
  }

  if (request.status) {
    repoReq.merchantStatus = request.status;
  }

  if (request.intType) {
    repoReq.catIntegrationType = request.intType;
  }

  return repoReq;
}
