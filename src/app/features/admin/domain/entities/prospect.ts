export interface GeneralInfo {
  email: string;
  merchantName: string;
  webPage: string;
  address: string;
}

export interface LegalRepresentative {
  name: string;
  position: string;
  phone: string;
}

export interface BusinessMetrics {
  industry: string;
  averageTicket: string;
  salesVolume: string;
  integrationType: string;
}

export type Prospect = {
  'general-info': GeneralInfo;
  'legal-representative': LegalRepresentative;
  'business-metrics': BusinessMetrics;
};
