export const INTEGRATION_TYPES = [
  { label: '<PERSON><PERSON><PERSON>', value: '<PERSON><PERSON><PERSON>' },
  { label: '<PERSON>scai', value: 'PROSCAI' },
  { label: 'Shopify', value: 'SHOPIV2' },
  { label: 'VTEX', value: 'VTEX' },
  { label: 'Magento', value: 'MGT' },
  { label: 'Magento V2', value: 'MGTV2' },
  { label: 'TiendaNube', value: 'TDN' },
  { label: 'Woocommerce', value: 'WOO' },
  { label: 'Prestashop', value: 'PRESTA' },
  { label: 'Online Desarrollo Propio', value: 'API' },
  { label: 'Offline Desarrollo Propio', value: 'API_OFFLINE' },
  { label: 'Tarjeta Virtual', value: 'VIRTUAL_CARD' },
  { label: 'Desconocido', value: 'UNKNOWN' },
] as const;

export type IntegrationTypeLabel = (typeof INTEGRATION_TYPES)[number]['label'];

export type IntegrationType = (typeof INTEGRATION_TYPES)[number]['value'];

export const VALID_INTEGRATION_TYPES_TO_CREATE_STORE = [
  'POSUI',
  'PROSCAI',
  'API_OFFLINE',
] as const;

export type ValidToCreateStore =
  (typeof VALID_INTEGRATION_TYPES_TO_CREATE_STORE)[number];
