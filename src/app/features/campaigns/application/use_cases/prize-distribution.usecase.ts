import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { Observable, throwError } from 'rxjs';
import { PrizeDistribution } from '../../domain/entities';

@Injectable()
export class PrizeDistributionUsecase
  implements
    BaseUsecase<
      {
        numberOfWinners: number | undefined;
        prizes: PrizeDistribution[] | undefined;
      },
      Observable<void>
    >
{
  constructor(private readonly errorHandler: UseCaseErrorHandler) {}

  execute(args: {
    numberOfWinners: number;
    prizes: PrizeDistribution[];
  }): Observable<void> {
    try {
      // Validación básica
      if (!args.prizes?.length) {
        throw new RuntimeMerchantError(
          'Se requiere al menos una distribución de premios',
          'PrizeDistributionUsecase::execute::emptyPrizes'
        );
      }

      // Ordenar premios por rango inicial para facilitar validación
      const sortedPrizes = [...args.prizes].sort(
        (a, b) => (a.fromRank ?? 0) - (b.fromRank ?? 0)
      );

      // Validar secuencia y solapamiento
      let expectedStartRank = 1;

      for (let i = 0; i < sortedPrizes.length; i++) {
        const currentPrize = sortedPrizes[i];

        // Validar que el rango inicial sea el esperado
        if (currentPrize.fromRank !== expectedStartRank) {
          throw new RuntimeMerchantError(
            `El rango debe comenzar en ${expectedStartRank}`,
            'PrizeDistributionUsecase::execute::invalidStartRank'
          );
        }

        // Validar que el rango final sea mayor que el inicial
        if (currentPrize.fromRank > currentPrize.toRank) {
          throw new RuntimeMerchantError(
            'El rango final debe ser mayor que el inicial',
            'PrizeDistributionUsecase::execute::invalidRange'
          );
        }

        // Validar que no exceda el número total de ganadores
        if (currentPrize.toRank > args.numberOfWinners) {
          throw new RuntimeMerchantError(
            'El rango excede el número total de ganadores',
            'PrizeDistributionUsecase::execute::exceedsWinners'
          );
        }

        // Validar solapamiento con el siguiente premio
        if (i < sortedPrizes.length - 1) {
          const nextPrize = sortedPrizes[i + 1];
          if (nextPrize.fromRank <= currentPrize.toRank) {
            throw new RuntimeMerchantError(
              'Los rangos de premios no pueden solaparse',
              'PrizeDistributionUsecase::execute::overlappingRanges'
            );
          }
        }

        // Actualizar el rango inicial esperado para el siguiente premio
        expectedStartRank = currentPrize.toRank + 1;
      }

      // Validar que se cubran todos los lugares
      const lastPrize = sortedPrizes[sortedPrizes.length - 1];
      if (lastPrize.toRank !== args.numberOfWinners) {
        throw new RuntimeMerchantError(
          'La distribución debe cubrir todos los lugares',
          'PrizeDistributionUsecase::execute::incompleteCoverage'
        );
      }

      return new Observable<void>(subscriber => {
        subscriber.next();
        subscriber.complete();
      });
    } catch (error) {
      return this.errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
