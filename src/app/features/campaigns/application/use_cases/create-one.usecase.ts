import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  TemporalService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, take, tap, throwError } from 'rxjs';
import {
  PremiosAplazoCampaignUIDto,
  PremiosAplazoCampaignUpsertDto,
} from '../../domain/entities';
import { CampaignsRepository } from '../../domain/repositories/premios-aplazo';
import { prizeDistributionGuard } from '../../infra/services/prize-distribution';

@Injectable()
export class CreateOneCampaignUsecase
  implements BaseUsecase<PremiosAplazoCampaignUIDto, Observable<void>>
{
  readonly #repository = inject(CampaignsRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #dateService = inject(TemporalService);
  readonly #notifier = inject(NotifierService);

  execute(args: PremiosAplazoCampaignUIDto): Observable<void> {
    const idLoader = this.#loader.show();

    try {
      if (!args.name) {
        throw new RuntimeMerchantError(
          'El nombre de la campaña es requerido',
          'CreateOneCampaignUsecase::execute::emptyName'
        );
      }

      if (args.numberOfWinners < 1) {
        throw new RuntimeMerchantError(
          'El número de ganadores debe ser mayor que 0',
          'CreateOneCampaignUsecase::execute::invalidNumberOfWinners'
        );
      }

      Guard.againstInvalidDate({
        date: args.startDate,
        errorMessage: 'Fecha de inicio inválida',
        origin: 'CreateOneCampaignUsecase::execute::startDate',
      });

      Guard.againstInvalidDate({
        date: args.endDate,
        errorMessage: 'Fecha de fin inválida',
        origin: 'CreateOneCampaignUsecase::execute::endDate',
      });

      Guard.againstInvalidNumbers(
        args.numberOfWinners,
        'CreateOneCampaignUsecase::execute::invalidNumberOfWinners',
        'El número de ganadores debe ser mayor que 0'
      );

      prizeDistributionGuard({
        numberOfWinners: args.numberOfWinners,
        prizes: args.prizes,
      });

      const startDate = this.#dateService.formatRawDateDayFirst(args.startDate);
      const endDate = this.#dateService.formatRawDateDayFirst(args.endDate);

      const req = {
        name: args.name,
        numberOfWinners: args.numberOfWinners,
        termsConditions: args.tyc,
        startDate,
        endDate,
        prizes: args.prizes,
      } satisfies PremiosAplazoCampaignUpsertDto;

      if (args.tyc) {
        req.termsConditions = args.tyc;
      }

      return this.#repository.createOne(req).pipe(
        tap(() => {
          this.#notifier.success({
            title: 'Campaña creada con éxito',
          });
        }),

        catchError(e => {
          return this.#errorHandler.handle<never>(e);
        }),

        finalize(() => {
          this.#loader.hide(idLoader);
        }),

        take(1)
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
