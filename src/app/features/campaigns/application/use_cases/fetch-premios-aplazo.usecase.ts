import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, take, throwError } from 'rxjs';
import {
  CampaignListResponse,
  CampaignsRepository,
} from '../../domain/repositories/premios-aplazo';

@Injectable()
export class FetchAPremiosAplazoUsecase extends BaseUsecase<
  { pageNum?: number },
  Observable<CampaignListResponse>
> {
  readonly #repository = inject(CampaignsRepository);
  readonly #usecaseErrror = inject(UseCaseErrorHandler);
  readonly #loader = inject(LoaderService);

  execute(args: { pageNum?: number }): Observable<CampaignListResponse> {
    const idLoader = this.#loader.show();

    try {
      const pageNum = isNaN(Number(args.pageNum)) ? 0 : Number(args.pageNum);

      return this.#repository
        .findCampaigns({
          pageNum,
        })
        .pipe(
          catchError(error => {
            if (error instanceof HttpErrorResponse && error.status === 400) {
              throw new RuntimeMerchantError(
                'Error cargando datos',
                'No se pudo obtener datos'
              );
            }

            throw error;
          }),

          catchError(error => this.#usecaseErrror.handle<never>(error)),

          take(1),

          finalize(() => {
            this.#loader.hide(idLoader);
          })
        );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#usecaseErrror.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
