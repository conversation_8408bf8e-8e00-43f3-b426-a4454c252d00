import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, of, take, throwError } from 'rxjs';
import { CampaignWinner } from '../../domain/entities';
import { CampaignsRepository } from '../../domain/repositories/premios-aplazo';

@Injectable()
export class GetWinnersByCampaign extends BaseUsecase<
  { campaignId: number },
  Observable<CampaignWinner[]>
> {
  readonly #repository = inject(CampaignsRepository);
  readonly #usecaseError = inject(UseCaseErrorHandler);
  readonly #loader = inject(LoaderService);

  execute(args: { campaignId: number }): Observable<CampaignWinner[]> {
    const idLoader = this.#loader.show();

    try {
      return this.#repository
        .getWinnersBy({
          campaignId: args.campaignId,
        })
        .pipe(
          catchError(error => {
            if (error instanceof HttpErrorResponse) {
              if (
                error.status === 404 ||
                (error.error?.code === 'APZRCS012' &&
                  error.error?.error === 'Winners not found')
              ) {
                return of([]);
              }

              throw new RuntimeMerchantError(
                'Error cargando ganadores',
                'No se pudo obtener los ganadores de la campaña'
              );
            }

            throw error;
          }),

          catchError(error => this.#usecaseError.handle<never>(error)),

          take(1),

          finalize(() => {
            this.#loader.hide(idLoader);
          })
        );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#usecaseError.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
