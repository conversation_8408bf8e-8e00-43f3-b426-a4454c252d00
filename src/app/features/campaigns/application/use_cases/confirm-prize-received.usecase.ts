import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, take, tap, throwError } from 'rxjs';
import { CampaignsRepository } from '../../domain/repositories/premios-aplazo';

@Injectable()
export class ConfirmPrizeReceivedUsecase extends BaseUsecase<
  { campaignId: number; winnerId: number; prizeReceived: boolean },
  Observable<void>
> {
  readonly #repository: CampaignsRepository = inject(CampaignsRepository);
  readonly #usecaseError: UseCaseErrorHandler = inject(UseCaseErrorHandler);
  readonly #loader: LoaderService = inject(LoaderService);
  readonly #notifier = inject(NotifierService);

  execute(args: {
    campaignId: number;
    winnerId: number;
    prizeReceived: boolean;
  }): Observable<void> {
    const idLoader = this.#loader.show();

    try {
      return this.#repository
        .confirmPrizeReceived({
          campaignId: args.campaignId,
          winnerId: args.winnerId,
          prizeReceived: args.prizeReceived,
        })
        .pipe(
          tap(() => {
            this.#notifier.success({
              title: 'Ganador confirmado',
              message: 'El ganador se actualizada exitosamente',
            });
          }),
          catchError(error => {
            if (error instanceof HttpErrorResponse) {
              throw new RuntimeMerchantError(
                'Error confirmando premio',
                'No se pudo confirmar la recepción del premio'
              );
            }

            throw error;
          }),

          catchError(error => this.#usecaseError.handle<never>(error)),

          take(1),

          finalize(() => {
            this.#loader.hide(idLoader);
          })
        );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#usecaseError.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
