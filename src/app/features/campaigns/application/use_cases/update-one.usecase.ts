import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  TemporalService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, take, tap, throwError } from 'rxjs';
import {
  PremiosAplazoCampaignUIDto,
  PremiosAplazoCampaignUpsertDto,
} from '../../domain/entities';
import { CampaignsRepository } from '../../domain/repositories/premios-aplazo';
import { prizeDistributionGuard } from '../../infra/services/prize-distribution';

@Injectable()
export class UpdateOneCampaignUsecase
  implements BaseUsecase<PremiosAplazoCampaignUIDto, Observable<void>>
{
  readonly #repository = inject(CampaignsRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #dateService = inject(TemporalService);
  readonly #notifier = inject(NotifierService);

  execute(args: PremiosAplazoCampaignUIDto): Observable<void> {
    const idLoader = this.#loader.show();

    try {
      const id = args.id;
      if (!id) {
        throw new RuntimeMerchantError(
          'El ID de la campaña es requerido',
          'EditOneCampaignUsecase::execute::emptyId'
        );
      }

      if (!args.name) {
        throw new RuntimeMerchantError(
          'El nombre de la campaña es requerido',
          'EditOneCampaignUsecase::execute::emptyName'
        );
      }

      if (args.numberOfWinners < 1) {
        throw new RuntimeMerchantError(
          'El número de ganadores debe ser mayor que 0',
          'EditOneCampaignUsecase::execute::invalidNumberOfWinners'
        );
      }

      Guard.againstInvalidDate({
        date: args.startDate,
        errorMessage: 'Fecha de inicio inválida',
        origin: 'EditOneCampaignUsecase::execute::startDate',
      });

      Guard.againstInvalidDate({
        date: args.endDate,
        errorMessage: 'Fecha de fin inválida',
        origin: 'EditOneCampaignUsecase::execute::endDate',
      });

      Guard.againstInvalidNumbers(
        args.numberOfWinners,
        'EditOneCampaignUsecase::execute::invalidNumberOfWinners',
        'El número de ganadores debe ser mayor que 0'
      );

      prizeDistributionGuard({
        numberOfWinners: args.numberOfWinners,
        prizes: args.prizes,
      });

      const startDate = this.#dateService.formatRawDateDayFirst(args.startDate);
      const endDate = this.#dateService.formatRawDateDayFirst(args.endDate);

      const req: PremiosAplazoCampaignUpsertDto = {
        id: id,
        name: args.name,
        numberOfWinners: args.numberOfWinners,
        startDate,
        endDate,
        prizes: args.prizes,
        termsConditions: args.tyc ?? undefined,
      };

      if (args.tyc) {
        req.termsConditions = args.tyc;
      }

      if (args.numberOfWinners) {
        req.numberOfWinners = args.numberOfWinners;
      }

      return this.#repository.updateOne(req).pipe(
        tap(() => {
          this.#notifier.success({
            title: 'Campaña actualizada exitosamente',
          });
        }),
        catchError(error => {
          return this.#errorHandler.handle<never>(error);
        }),
        take(1),
        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
