import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, of, take } from 'rxjs';
import { CampaignsRepository } from '../../domain/repositories/premios-aplazo';

export interface CampaignSelectItem {
  id: number;
  name: string;
}

@Injectable()
export class GetLastCampaignsUseCase extends BaseUsecase<
  void,
  Observable<CampaignSelectItem[]>
> {
  readonly #repository = inject(CampaignsRepository);
  readonly #usecaseError = inject(UseCaseErrorHandler);
  readonly #loader = inject(LoaderService);

  execute(): Observable<CampaignSelectItem[]> {
    const idLoader = this.#loader.show();

    return this.#repository.getLastOnes().pipe(
      map(response => response.campaigns),
      catchError(error => {
        if (error instanceof HttpErrorResponse && error.status === 404) {
          return of([]);
        }
        if (error instanceof HttpErrorResponse) {
          throw new RuntimeMerchantError(
            'Error cargando campañas',
            'No se pudo obtener la lista de campañas'
          );
        }
        throw error;
      }),
      catchError(error => this.#usecaseError.handle<never>(error)),
      take(1),
      finalize(() => this.#loader.hide(idLoader))
    );
  }
}
