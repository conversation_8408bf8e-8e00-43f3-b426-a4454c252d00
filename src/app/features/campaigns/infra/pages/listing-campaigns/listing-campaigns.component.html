@if (data$ | async; as data) {
  <div class="p-4 flex gap-4 flex-col">
    <aplz-ui-card>
      <div class="w-full flex gap-4 justify-between items-baseline">
        <button
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          class="flex-grow-0 flex-shrink-0 mb-8"
          (click)="createOneCampaign()">
          Crear camp<PERSON>
        </button>
      </div>
    </aplz-ui-card>
    <app-aplazo-rewards-table
      [data]="data.content"
      (editClick)="editOneCampaign($event)"></app-aplazo-rewards-table>
    @if (data.totalPages > 1) {
      <aplz-ui-pagination
        [maxPagesToShow]="maxPagesByView"
        [totalPages]="data.totalPages"
        [currentPage]="currentPage$ | async"
        (selectedPage)="changePage($event)">
      </aplz-ui-pagination>
    }
  </div>
}
