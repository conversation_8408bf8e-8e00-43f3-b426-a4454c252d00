import { AsyncPipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { TemporalService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { DialogRef, DialogService } from '@ngneat/dialog';
import {
  BehaviorSubject,
  combineLatest,
  distinctUntilChanged,
  startWith,
  Subject,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { ROUTES_CONFIG } from '../../../../core/domain/route-names';
import { FetchAPremiosAplazoUsecase } from '../../../application/use_cases/fetch-premios-aplazo.usecase';
import { PremiosAplazoCampaign } from '../../../domain/entities';
import { AplazoRewardsTableComponent } from '../../components/aplazo-rewards-table/aplazo-rewards-table.component';
import { CampaignFormComponent } from '../../components/campaign-form.component';

@Component({
  selector: 'app-listing-campaigns',
  imports: [
    AplazoRewardsTableComponent,
    AplazoFormFieldDirectives,
    AplazoCardComponent,
    AplazoButtonComponent,
    AsyncPipe,
    AplazoPaginationComponent,
    ReactiveFormsModule,
  ],
  templateUrl: './listing-campaigns.component.html',
})
export class ListingCampaignsComponent {
  readonly #dialog = inject(DialogService);
  readonly #getAllUseCase = inject(FetchAPremiosAplazoUsecase);
  readonly #router = inject(Router);
  readonly #temporal = inject(TemporalService);

  readonly #refresher$ = new Subject<void>();
  readonly currentPage$ = new BehaviorSubject<number>(0);

  readonly maxPagesByView = 10;

  public data$ = combineLatest({
    refresher: this.#refresher$.pipe(startWith(void 0)),
    pageNum: this.currentPage$.pipe(distinctUntilChanged()),
  }).pipe(
    switchMap(({ pageNum }) => {
      return this.#getAllUseCase.execute({
        pageNum,
      });
    })
  );

  changePage(page: number) {
    this.currentPage$.next(page);
  }

  createOneCampaign() {
    const dialog: DialogRef<any, any> = this.#dialog.open(
      CampaignFormComponent,
      {
        enableClose: false,
      }
    );

    dialog.afterClosed$
      .pipe(
        take(1),
        tap(result => {
          if (result?.hasConfirmation) {
            this.#router.navigate([
              '/',
              ROUTES_CONFIG.board,
              ROUTES_CONFIG.campaigns,
              ROUTES_CONFIG.merchantCampaignsAdmin,
            ]);
          }
        })
      )
      .subscribe();
  }

  editOneCampaign(campaign: PremiosAplazoCampaign) {
    const startDate = this.#temporal.fromStringToDate(campaign.startDate);
    const endDate = this.#temporal.fromStringToDate(campaign.endDate);

    const dialog: DialogRef<any, any> = this.#dialog.open(
      CampaignFormComponent,
      {
        enableClose: false,
        data: {
          ...campaign,
          startDate,
          endDate,
        },
      }
    );

    dialog.afterClosed$
      .pipe(
        take(1),
        tap(result => {
          if (result?.hasConfirmation) {
            this.#refresher$.next(void 0);
          }
        })
      )
      .subscribe();
  }
}
