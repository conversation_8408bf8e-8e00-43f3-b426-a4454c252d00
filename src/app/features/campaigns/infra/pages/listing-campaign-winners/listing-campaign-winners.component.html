<div class="p-4 flex gap-4 flex-col">
  <aplz-ui-card>
    <div class="w-full flex items-center mt-4">
      <aplz-ui-form-field>
        <aplz-ui-form-label>Seleccionar Campaña</aplz-ui-form-label>

        <select
          class="p-4 min-w-[200px]"
          aplzFormSelect
          [formControl]="selectedCampaignControl">
          @for (campaign of (campaigns$ | async) ?? []; track campaign) {
            <option [value]="campaign.id">{{ campaign.name }}</option>
          }
        </select>
      </aplz-ui-form-field>
    </div>
  </aplz-ui-card>
  @if (winners$ | async; as winnersWithLastUpdate) {
    @if (winnersWithLastUpdate.winners.length > 0) {
      <app-campaign-winners-table
        [data]="winnersWithLastUpdate.winners"
        (prizeReceivedChange)="onPrizeReceivedChange($event)">
      </app-campaign-winners-table>

      <div
        class="text-right text-gray-500 mt-2 pr-2"
        data-testid="last-update-date">
        Última actualización:
        {{ winnersWithLastUpdate.lastUpdateDate | aplzDynamicPipe: 'date' }}
      </div>
    } @else {
      <aplz-ui-card>
        <aplz-ui-common-message
          imgName="emptyLoans"
          [i18Text]="{
            title: 'No hay ganadores para mostrar',
            description:
              'La campaña sigue activa, aún no se han seleccionado a los ganadores.',
          }">
        </aplz-ui-common-message>
      </aplz-ui-card>
    }
  }
</div>
