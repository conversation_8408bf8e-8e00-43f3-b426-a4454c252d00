import { AsyncPipe } from '@angular/common';
import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoCommonMessageComponents } from '@aplazo/shared-ui/merchant';
import {
  filter,
  map,
  merge,
  scan,
  shareReplay,
  Subject,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { ConfirmPrizeReceivedUsecase } from '../../../application/use_cases/confirm-prize-received.usecase';
import { GetLastCampaignsUseCase } from '../../../application/use_cases/get-last-campaings.usecase';
import { GetWinnersByCampaign } from '../../../application/use_cases/get-winners.usecase';
import { CampaignWinnersTableComponent } from '../../components/aplazo-winners-table/aplazo-winners-table.component';

@Component({
  selector: 'app-winners',
  imports: [
    AplazoCommonMessageComponents,
    AplazoCardComponent,
    CampaignWinnersTableComponent,
    AplazoFormFieldDirectives,
    ReactiveFormsModule,
    AsyncPipe,
    AplazoDynamicPipe,
  ],
  templateUrl: './listing-campaign-winners.component.html',
})
export class ListingCampaignsWinnersComponent implements OnInit, OnDestroy {
  readonly #getLastCampaigns = inject(GetLastCampaignsUseCase);
  readonly #getWinners = inject(GetWinnersByCampaign);
  private readonly confirmPrizeReceivedUsecase = inject(
    ConfirmPrizeReceivedUsecase
  );

  readonly #destroy$ = new Subject<void>();
  readonly #refresh$ = new Subject<void>();

  readonly selectedCampaignControl = new FormControl<number | null>(null);

  readonly campaigns$ = this.#getLastCampaigns
    .execute()
    .pipe(takeUntil(this.#destroy$), shareReplay(1));

  readonly winners$ = merge(
    this.selectedCampaignControl.valueChanges,
    this.#refresh$
  ).pipe(
    scan((acc, selected) => {
      if (!selected) {
        return acc;
      }

      return selected;
    }, 0),
    filter(Boolean),
    switchMap(selected => {
      return this.#getWinners.execute({ campaignId: selected ?? 0 }).pipe(
        map(winners => {
          return {
            winners,
            lastUpdateDate: new Date(),
          };
        })
      );
    })
  );

  onPrizeReceivedChange(event: {
    winnerId: number;
    prizeReceived: boolean;
  }): void {
    this.confirmPrizeReceivedUsecase
      .execute({
        campaignId: this.selectedCampaignControl.value ?? 0,
        winnerId: event.winnerId,
        prizeReceived: event.prizeReceived,
      })
      .subscribe({
        next: () => {
          this.#refresh$.next();
        },
        error: () => {
          this.#refresh$.next();
        },
      });
  }

  ngOnInit(): void {
    this.campaigns$
      .pipe(
        take(1),

        tap(c => {
          if ((c?.length ?? 0) > 0) {
            this.selectedCampaignControl.setValue(c[0].id);
          }
        })
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
