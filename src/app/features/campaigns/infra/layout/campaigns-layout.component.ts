import { Component, computed, inject, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoTabsComponents } from '@aplazo/shared-ui/tabs';
import { ROUTES_CONFIG } from '../../../core/domain/route-names';

export const CAMPAIGN_TABS = Object.freeze(
  [
    {
      label: 'Campañas',
      route: ROUTES_CONFIG.merchantCampaignsAdmin,
    },
    {
      label: 'Ganadores',
      route: ROUTES_CONFIG.merchantCampaignsWinners,
    },
  ].map(i => Object.freeze(i))
);

@Component({
  selector: 'app-campaigns-layout',
  template: `
    <section
      class="flex flex-col gap-4 md:gap-8 px-4 md:px-8 pt-4 md:pt-8 w-full">
      <aplz-ui-card>
        <aplz-ui-tab-group
          (tabSelectionChange)="changeTab($event)"
          [selectedIndex]="currentTabIdx()">
          @for (tab of tabs; track tab) {
            <aplz-ui-tab [label]="tab.label"></aplz-ui-tab>
          }
        </aplz-ui-tab-group>
      </aplz-ui-card>
    </section>

    <router-outlet></router-outlet>
  `,
  imports: [AplazoTabsComponents, AplazoCardComponent, RouterOutlet],
})
export class CampaignsLayoutComponent {
  readonly #router = inject(Router);
  readonly route = inject(ActivatedRoute);

  readonly #currentTabIdx = signal<number>(0);
  readonly currentTabIdx = computed(() => this.#currentTabIdx());
  readonly tabs = CAMPAIGN_TABS;

  changeTab(event: { index: number }): void {
    const tab = this.tabs[event.index];

    this.#currentTabIdx.set(event.index);
    this.#router.navigate([tab.route], {
      relativeTo: this.route,
    });
  }
}
