import { Component, input, output } from '@angular/core';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoCommonMessageComponents } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { CampaignWinner } from '../../../domain/entities';

@Component({
  selector: 'app-campaign-winners-table',
  imports: [
    AplazoCardComponent,
    AplazoSimpleTableComponents,
    AplazoCommonMessageComponents,
  ],
  templateUrl: './aplazo-winners-table.component.html',
})
export class CampaignWinnersTableComponent {
  data = input<CampaignWinner[]>([] as CampaignWinner[]);
  isLoading = input<boolean>(false);

  // Nuevo evento para manejar cambios en prizeReceived
  prizeReceivedChange = output<{ winnerId: number; prizeReceived: boolean }>();

  onPrizeReceivedChange(winner: CampaignWinner, event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.prizeReceivedChange.emit({
      winnerId: winner.id,
      prizeReceived: checkbox.checked,
    });
  }
}
