<aplz-ui-card class="max-h-[450px] overflow-y-auto">
  @if (data().length > 0) {
    <table aplzSimpleTable aria-label="Campaign Winners List">
      <tr aplzSimpleTableHeaderRow>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          ID
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          ID Participante
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Número de Teléfono
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Posición
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Nombre del Participante
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Usuarios Registrados
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Dirección
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Premio Recibido
        </th>
      </tr>

      @for (item of data(); track item.id) {
        <tr aplzSimpleTableBodyRow [striped]="true">
          <td aplzSimpleTableBodyCell class="font-semibold text-center">
            {{ item.id }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.participantId }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.phoneNumber }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.position }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.participantName }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.usersRegistered }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.address }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.prize }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            <label>
              <input
                type="checkbox"
                [checked]="item.prizeReceived"
                (change)="onPrizeReceivedChange(item, $event)"
                title="Premio Recibido" />
            </label>
          </td>
        </tr>
      }
    </table>
  } @else if (isLoading()) {
    <div class="flex justify-center items-center p-8">
      <p>Cargando ganadores...</p>
    </div>
  } @else {
    <aplz-ui-common-message
      imgName="emptyLoans"
      [i18Text]="{
        title: 'No hay ganadores',
        description: 'No se encontraron ganadores para esta campaña.',
      }">
    </aplz-ui-common-message>
  }
</aplz-ui-card>
