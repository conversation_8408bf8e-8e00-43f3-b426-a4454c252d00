import { AsyncPipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  forwardRef,
  inject,
  OnD<PERSON>roy,
  ViewEncapsulation,
} from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormArray,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import { OnlyNumbersDirective } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconXMark } from '@aplazo/ui-icons';
import {
  asyncScheduler,
  BehaviorSubject,
  combineLatest,
  debounceTime,
  delay,
  distinctUntilChanged,
  map,
  shareReplay,
  startWith,
  Subject,
  takeUntil,
  tap,
} from 'rxjs';
import { PrizeDistribution } from '../../../domain/entities';
import { Ranges } from '../../../domain/entities/range-as-linked-list';

export interface PrizeDistributionValue {
  numberOfWinners: number;
  prizes: PrizeDistribution[] | null;
}

export type PrizeUI = Record<
  string | number,
  {
    end: string;
    start: string;
    name: string;
  }
>;

export type DistributionUI = {
  winners: number | null;
  prizes: PrizeUI | null;
};

@Component({
  selector: 'app-prize-distribution',
  templateUrl: './prize-distribution.component.html',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ReactiveFormsModule,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
    OnlyNumbersDirective,
    AplazoIconComponent,
    AsyncPipe,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PrizeDistributionComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PrizeDistributionComponent),
      multi: true,
    },
  ],
})
export class PrizeDistributionComponent
  implements OnDestroy, ControlValueAccessor, Validator
{
  readonly #iconRegistry = inject(AplazoIconRegistryService);
  readonly #cdr = inject(ChangeDetectorRef);

  #ranges: Ranges | null = null;

  readonly DELAY_UPDATE_BY_END = 450;

  readonly #destroy$ = new Subject<void>();

  readonly #isControlDisabled$ = new BehaviorSubject<boolean>(false);

  onChange = (_: PrizeDistributionValue | null) => {};
  onTouched = () => {};
  private validatorOnChange: () => void = () => {};

  readonly winnersControl = new FormControl<number | null>(null, {
    nonNullable: false,
    validators: [Validators.required, Validators.pattern('^[1-9][0-9]*$')],
  });
  readonly prizesControl = new FormArray<
    FormGroup<{
      start: FormControl<string>;
      end: FormControl<string>;
      name: FormControl<string>;
    }>
  >([]);
  readonly form = new FormGroup({
    winners: this.winnersControl,
    prizes: this.prizesControl,
  });

  readonly winnersPrizesValues$ = combineLatest({
    winners: this.winnersControl.valueChanges.pipe(
      startWith(this.winnersControl.value),
      takeUntil(this.#destroy$)
    ),
    prizes: this.prizesControl.valueChanges.pipe(
      startWith(this.prizesControl.value),
      takeUntil(this.#destroy$)
    ),
  }).pipe(
    distinctUntilChanged(
      (prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)
    ),
    delay(0, asyncScheduler),
    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  readonly isWinnersGreaterThanPrizes$ = this.winnersPrizesValues$.pipe(
    map(({ winners, prizes }) => {
      const numOfWinners = isNaN(Number(winners)) ? 0 : Number(winners);
      return (prizes?.length ?? 0) >= numOfWinners;
    })
  );

  get prizesLength(): number {
    return this.#ranges?.getValue().length ?? 0;
  }

  readonly vm$ = combineLatest({
    distribution: this.winnersPrizesValues$.pipe(
      map(() => {
        const rawValue = this.form.getRawValue();
        return {
          winners: rawValue.winners,
          prizes: this.#formArrayPrizesToPrizeUI(rawValue.prizes),
        };
      })
    ),
    isWinnersGreaterThanPrizes: this.isWinnersGreaterThanPrizes$,
    isControlDisabled: this.#isControlDisabled$.pipe(distinctUntilChanged()),
  }).pipe();

  constructor() {
    this.#iconRegistry.registerIcons([iconXMark]);

    this.winnersPrizesValues$.pipe().subscribe(() => {
      if (this.form.pristine && this.form.untouched) {
        return;
      }

      const value = this.form.getRawValue();

      const outputValue = {
        numberOfWinners: Number(value.winners),
        prizes: this.#deserializePrizeDistribution(value.prizes),
      };

      this.onChange(outputValue);
      this.onTouched();
    });

    combineLatest({
      winners: this.winnersControl.statusChanges,
      prizes: this.prizesControl.statusChanges,
    })
      .pipe(takeUntil(this.#destroy$))
      .subscribe(() => {
        this.validatorOnChange();
      });

    this.winnersControl.valueChanges
      .pipe(
        distinctUntilChanged(),
        tap(v => {
          this.#changeWinners(v);
        }),
        takeUntil(this.#destroy$)
      )
      .subscribe();

    this.prizesControl.valueChanges
      .pipe(
        debounceTime(this.DELAY_UPDATE_BY_END),
        tap(current => {
          const ranges: {
            end: number;
            start: number;
            name: string;
          }[] = this.#ranges?.getValue() ?? [];

          for (let range = 0; range < ranges.length; range++) {
            const endPrize = current[range].end;
            const endRange = ranges[range].end;

            const namePrize = current[range].name;
            const nameRange = ranges[range].name;

            if (
              endPrize != null &&
              endRange != null &&
              !isNaN(+endPrize) &&
              !isNaN(+endRange) &&
              Number(endPrize) !== Number(endRange)
            ) {
              this.#changePrizeByIndex({
                prizeIndex: range,
                newEndValue: String(endPrize),
              });

              return;
            }

            if (namePrize != nameRange) {
              this.#changePrizeByIndex({
                prizeIndex: range,
                newNameValue: namePrize,
              });

              return;
            }
          }
        }),
        takeUntil(this.#destroy$)
      )
      .subscribe();
  }

  writeValue(value: PrizeDistributionValue | null): void {
    const { numberOfWinners = 0, prizes = null } = value ?? {
      numberOfWinners: 0,
      prizes: null,
    };

    this.winnersControl.setValue(numberOfWinners > 0 ? numberOfWinners : null, {
      emitEvent: false,
    });

    if (numberOfWinners > 0 && prizes) {
      this.#ranges = new Ranges();
      const mappedPrizesForRanges = prizes.map(p => ({
        end: p.toRank,
        name: p.prizeName,
      }));
      this.#ranges.addMany(mappedPrizesForRanges);
    } else if (numberOfWinners > 0) {
      this.#ranges = new Ranges(Number(numberOfWinners));
    } else {
      this.#ranges = null;
    }

    this.#syncPrizesFormArrayWithRanges(false);
    this.form.markAsPristine();
    this.form.markAsUntouched();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = () => {
      fn();
    };
  }
  registerOnValidatorChange?(fn: () => void): void {
    this.validatorOnChange = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.#isControlDisabled$.next(isDisabled ?? false);
    if (isDisabled) {
      this.form.disable({ emitEvent: false });
    } else {
      this.form.enable({ emitEvent: false });
    }
  }

  validate(control: AbstractControl): ValidationErrors | null {
    if (control.touched || this.form.touched) {
      this.form.markAllAsTouched();
      this.#cdr.markForCheck();
    }

    if (!this.form.valid) {
      const errors: ValidationErrors = { prizeDistributionError: true };
      if (this.winnersControl.errors) {
        errors['winners'] = this.winnersControl.errors;
      }

      const prizeGroupErrors: Record<string, any>[] = [];
      this.prizesControl.controls.forEach((prizeControl, index) => {
        if (prizeControl.errors) {
          prizeGroupErrors.push({ index, errors: prizeControl.errors });
        }
      });
      if (prizeGroupErrors.length > 0) {
        errors['prizes'] = prizeGroupErrors;
      } else if (this.prizesControl.errors) {
        errors['prizesArray'] = this.prizesControl.errors;
      }
      return errors;
    }
    return null;
  }

  addPrize(): void {
    const winners =
      this.winnersControl.value != null && !isNaN(+this.winnersControl.value)
        ? Number(this.winnersControl.value)
        : null;

    if (winners == null) {
      return;
    }

    if (!this.#ranges && winners > 0) {
      this.#ranges = new Ranges(winners);
    }

    this.#ranges?.addOne();
    this.#syncPrizesFormArrayWithRanges();
    this.form.markAsDirty();
  }

  deletePrizeByIndex(index: number): void {
    if (!this.#ranges) {
      return;
    }

    const rangesSnapshot = this.#ranges.getValue();

    if (index < rangesSnapshot.length) {
      const endValueToDeleteFromRanges = rangesSnapshot[index].end;
      this.#ranges.deleteOneByEnd(Number(endValueToDeleteFromRanges));

      this.#syncPrizesFormArrayWithRanges();
      this.form.markAsDirty();
    }

    if (
      this.prizesControl.length === 0 &&
      this.#ranges?.getValue().length === 0
    ) {
      this.winnersControl.setValue(null, { emitEvent: true });
      this.#ranges = null;
    }
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  readonly #changePrizeByIndex = (args: {
    prizeIndex: number;
    newEndValue?: string;
    newNameValue?: string;
  }): void => {
    if (!this.#ranges) {
      return;
    }

    const currentRanges = this.#ranges.getValue();

    if ((args.prizeIndex ?? 0) >= currentRanges.length) {
      return;
    }

    const originalEndForKey = currentRanges[args.prizeIndex].end;

    const prizeFg = this.prizesControl.at(args.prizeIndex) as FormGroup;

    try {
      if (args.newEndValue) {
        prizeFg.get('end')?.setValue(args.newEndValue, { emitEvent: false });
      }

      if (args.newNameValue) {
        prizeFg.get('name')?.setValue(args.newNameValue, { emitEvent: false });
      }

      this.#ranges.updateOneByEnd(
        Number(originalEndForKey),
        Number(args.newEndValue ?? originalEndForKey),
        args.newNameValue ?? currentRanges[args.prizeIndex].name
      );

      this.#syncPrizesFormArrayWithRanges();
      this.form.markAsDirty();
    } catch (error) {
      console.error('Error updating prize end:', error);
      this.#syncPrizesFormArrayWithRanges();
    }
  };

  readonly #changeWinners = (eventValue: number | string | null): void => {
    const newWinners =
      eventValue && !isNaN(+eventValue) ? Number(eventValue) : null;

    if (newWinners == null || newWinners <= 0) {
      this.winnersControl.setValue(null, { emitEvent: false });
      this.#ranges = null;
    } else {
      this.winnersControl.setValue(newWinners, { emitEvent: false });
      this.#ranges = new Ranges(newWinners);
    }
    this.#syncPrizesFormArrayWithRanges(true);
    this.form.markAsDirty();
  };

  readonly #formArrayPrizesToPrizeUI = (
    formArrayValue:
      | { start: string; end: string; name: string }[]
      | undefined
      | null
  ): PrizeUI | null => {
    if (!formArrayValue || formArrayValue.length === 0) return null;
    return formArrayValue.reduce((acc, prize, index) => {
      acc[index] = {
        start: prize.start,
        end: prize.end,
        name: prize.name,
      };
      return acc;
    }, {} as PrizeUI);
  };

  readonly #deserializePrizeDistribution = (
    prizesFromForm:
      | {
          start: string;
          end: string;
          name: string;
        }[]
      | undefined
      | null
  ): PrizeDistribution[] => {
    return (
      prizesFromForm?.map(p => ({
        fromRank: Number(p.start),
        toRank: Number(p.end),
        prizeName: p.name,
      })) ?? []
    );
  };

  readonly #createPrizeFormGroup = (prize?: {
    start: string;
    end: string;
    name: string;
  }): FormGroup => {
    return new FormGroup({
      start: new FormControl(
        { value: prize?.start ?? '', disabled: true },
        {
          validators: [Validators.required],
        }
      ),
      end: new FormControl(
        { value: prize?.end ?? '', disabled: false },
        {
          validators: [Validators.required],
        }
      ),
      name: new FormControl(
        { value: prize?.name ?? '', disabled: false },
        {
          validators: [Validators.required, Validators.minLength(3)],
        }
      ),
    });
  };

  readonly #syncPrizesFormArrayWithRanges = (emitEvent = true): void => {
    const currentPrizesData = this.#ranges?.getValue() ?? [];

    while (this.prizesControl.length !== currentPrizesData.length) {
      if (this.prizesControl.length < currentPrizesData.length) {
        const newPrizeData = currentPrizesData[this.prizesControl.length];
        const newFG = this.#createPrizeFormGroup({
          start: String(newPrizeData.start),
          end: String(newPrizeData.end),
          name: newPrizeData.name,
        });
        this.prizesControl.push(newFG, { emitEvent: false });
      } else {
        this.prizesControl.removeAt(this.prizesControl.length - 1, {
          emitEvent: false,
        });
      }
    }

    currentPrizesData.forEach((prizeData, index) => {
      this.prizesControl.at(index).patchValue(
        {
          start: String(prizeData.start),
          end: String(prizeData.end),
          name: prizeData.name,
        },
        { emitEvent: false }
      );
    });

    if (emitEvent) {
      this.prizesControl.updateValueAndValidity({ onlySelf: false });
    }

    this.#cdr.markForCheck();
  };
}
