<div
  class="mt-6 transition-all duration-300 ease-in-out animate-in fade-in slide-in-from-bottom-5">
  @if (vm$ | async; as ctx) {
    <form [formGroup]="form">
      <aplz-ui-form-field>
        <aplz-ui-form-label> Número de ganadores </aplz-ui-form-label>
        <input
          type="text"
          inputmode="numeric"
          aplzFormInput
          aplazoOnlyNumbers
          id="prize-distribution-winners--input"
          formControlName="winners" />
        <ng-container aplzFormError>
          @if (
            (winnersControl.touched || winnersControl.dirty) &&
            winnersControl.hasError('required')
          ) {
            <p>El campo 'Número de ganadores' es obligatorio.</p>
          }
          @if (
            winnersControl.hasError('pattern') &&
            (winnersControl.touched || winnersControl.dirty)
          ) {
            <p>
              El campo 'Número de ganadores' debe ser un número entero positivo.
            </p>
          }
        </ng-container>
      </aplz-ui-form-field>

      @if (prizesLength > 0) {
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium">Distribución de premios</h3>
          <div class="add-button-container">
            <button
              type="button"
              aplzButton
              aplzAppearance="stroked"
              aplzColor="info"
              size="sm"
              [rounded]="true"
              (click)="addPrize()"
              [disabled]="
                ctx.isControlDisabled || ctx.isWinnersGreaterThanPrizes
              ">
              Agregar Premio
            </button>
          </div>
        </div>

        <section formArrayName="prizes" class="grid grid-cols-1 gap-y-10">
          @for (
            prizeControl of prizesControl.controls;
            track prizeControl;
            let i = $index;
            let first = $first
          ) {
            <article
              [formGroupName]="i"
              data-test="prize-distribution-component"
              class="grid grid-cols-9 gap-2 justify-items-center">
              <aplz-ui-form-field class="col-span-2">
                <aplz-ui-form-label>Desde</aplz-ui-form-label>
                <input
                  type="text"
                  inputmode="numeric"
                  placeholder="0"
                  aplzFormInput
                  aplazoOnlyNumbers
                  formControlName="start" />
                <ng-container aplzFormError>
                  @if (
                    (prizeControl.get('start')?.touched ||
                      prizeControl.get('start')?.dirty) &&
                    prizeControl.get('start')?.hasError('required')
                  ) {
                    <p>El campo 'Desde' es obligatorio.</p>
                  }
                </ng-container>
              </aplz-ui-form-field>

              <aplz-ui-form-field class="col-span-2">
                <aplz-ui-form-label>Hasta</aplz-ui-form-label>
                <input
                  type="text"
                  inputmode="numeric"
                  placeholder="0"
                  aplzFormInput
                  aplazoOnlyNumbers
                  formControlName="end" />
                <ng-container aplzFormError>
                  @if (
                    prizeControl.get('end')?.hasError('required') &&
                    (prizeControl.get('end')?.touched ||
                      prizeControl.get('end')?.dirty)
                  ) {
                    <p>El campo 'Hasta' es obligatorio.</p>
                  }
                </ng-container>
              </aplz-ui-form-field>

              <aplz-ui-form-field class="col-span-4">
                <aplz-ui-form-label>Premio</aplz-ui-form-label>
                <input
                  type="text"
                  placeholder="Playstation 5"
                  aplzFormInput
                  formControlName="name" />
                <ng-container aplzFormError>
                  @if (
                    prizeControl.get('name')?.hasError('required') &&
                    (prizeControl.get('name')?.touched ||
                      prizeControl.get('name')?.dirty)
                  ) {
                    <p>El campo 'Premio' es obligatorio.</p>
                  }
                  @if (
                    prizeControl.get('name')?.hasError('minlength') &&
                    (prizeControl.get('name')?.touched ||
                      prizeControl.get('name')?.dirty)
                  ) {
                    <p>
                      El campo 'Premio' debe tener al menos
                      {{
                        prizeControl.get('name')?.errors?.['minlength']
                          .requiredLength
                      }}
                      caracteres.
                    </p>
                  }
                </ng-container>
              </aplz-ui-form-field>
              <button
                class="col-span-1 self-center -mt-8"
                title="Eliminar premio"
                type="button"
                aplzButton
                aplzAppearance="stroked"
                aplzColor="danger"
                size="xs"
                [rounded]="true"
                (click)="deletePrizeByIndex(i)"
                [disabled]="
                  (prizesControl.controls.length > 1 && first) ||
                  ctx.isControlDisabled
                ">
                <aplz-ui-icon name="x-mark" size="sm"></aplz-ui-icon>
                <span class="sr-only">Eliminar premio</span>
              </button>
            </article>
          }
        </section>
      }
    </form>
  }
</div>
