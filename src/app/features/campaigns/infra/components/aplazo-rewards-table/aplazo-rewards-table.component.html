<aplz-ui-card class="max-h-[450px] overflow-y-auto">
  @if (data().length > 0) {
    <table aplzSimpleTable aria-label="Campaign List">
      <tr aplzSimpleTableHeaderRow>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          ID
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Nombre de la Campaña
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Fecha de Creación
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Fecha Inicio
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Fecha Fin
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Terminos y Condiciones
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Creado por
        </th>
        <th aplzSimpleTableHeaderCell scope="col" class="text-center px-2">
          Estatus
        </th>
        <th scope="col" aplzSimpleTableHeaderCell class="text-center px-2">
          Acciones
        </th>
      </tr>

      @for (item of data(); track item.id) {
        <tr aplzSimpleTableBodyRow [striped]="true">
          <td aplzSimpleTableBodyCell class="font-semibold text-center">
            {{ item.id }}
          </td>
          <td aplzSimpleTableBodyCell class="text-left">
            <div class="grid grid-cols-4 gap-2 items-center min-w-64">
              @if (!item.termsConditions) {
                <span
                  class="col-span-1 text-special-danger"
                  aplzTooltip="Esta campaña no tiene términos y condiciones cargados.">
                  <aplz-ui-icon
                    size="lg"
                    name="exclamation-triangle"></aplz-ui-icon>
                </span>
              }
              <span class="col-span-3 truncate" aplzTooltip="{{ item.name }}">
                {{ item.name }}
              </span>
            </div>
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.createdAt | aplzDynamicPipe: 'date' }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.startDate }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.endDate }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.termsConditions || 'Sin información' }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            {{ item.createdBy }}
          </td>
          <td
            aplzSimpleTableBodyCell
            class="text-center"
            [class.text-special-success]="item.active"
            [class.text-special-danger]="!item.active">
            {{ item.active ? 'Active' : 'Inactive' }}
          </td>
          <td aplzSimpleTableBodyCell class="text-center">
            <button
              aplzButton
              aplzAppearance="stroked"
              aplzColor="light"
              size="sm"
              [rounded]="true"
              (click)="editClick.emit(item)">
              Edit
            </button>
          </td>
        </tr>
      }
    </table>
  } @else {
    <aplz-ui-common-message
      imgName="emptyLoans"
      [i18Text]="{
        title: 'No hay campañas',
        description: 'No se encontraron campañas para el filtro seleccionado.',
      }">
    </aplz-ui-common-message>
  }
</aplz-ui-card>
