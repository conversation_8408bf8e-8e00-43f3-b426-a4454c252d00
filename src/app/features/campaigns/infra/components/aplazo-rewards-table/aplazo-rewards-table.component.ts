import { Component, inject, input, output } from '@angular/core';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoCommonMessageComponents } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import { iconExclamationTriangle } from '@aplazo/ui-icons';
import { PremiosAplazoCampaign } from '../../../domain/entities';

@Component({
  selector: 'app-aplazo-rewards-table',
  imports: [
    AplazoCardComponent,
    AplazoButtonComponent,
    AplazoSimpleTableComponents,
    AplazoCommonMessageComponents,
    AplazoDynamicPipe,
    AplazoIconComponent,
    AplazoTooltipDirective,
  ],
  templateUrl: './aplazo-rewards-table.component.html',
})
export class AplazoRewardsTableComponent {
  readonly #iconRegister = inject(AplazoIconRegistryService);

  data = input<PremiosAplazoCampaign[]>([] as PremiosAplazoCampaign[]);

  editClick = output<PremiosAplazoCampaign>();

  constructor() {
    this.#iconRegister.registerIcons([iconExclamationTriangle]);
  }
}
