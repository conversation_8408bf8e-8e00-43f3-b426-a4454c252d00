import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NotifierService, RawDateDayFirst } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
} from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { catchError, of, take, tap } from 'rxjs';
import { CreateOneCampaignUsecase } from '../../application/use_cases/create-one.usecase';
import { UpdateOneCampaignUsecase } from '../../application/use_cases/update-one.usecase';
import {
  PremiosAplazoCampaignUIDto,
  PrizeDistribution,
} from '../../domain/entities';
import { PrizeDistributionComponent } from './distribution/prize-distribution.component';

const today = new Date();
const config = { locale: es };
const todayFormated = format(today, 'dd/MM/yyyy', config) as RawDateDayFirst;
const TYC_REGEXP = new RegExp(/^https:\/\/cdn\.aplazo\.mx\/reglas\/.+\.pdf$/);

@Component({
  selector: 'app-campaign-form',
  template: `
    <aplz-ui-card>
      <h1 class="text-2xl font-medium mt-8 mx-6">
        <span>{{ data?.id ? 'Editar' : 'Nueva' }}</span>
        <span> campaña </span>
      </h1>
      <form [formGroup]="form" (ngSubmit)="finish()" class="mx-6 mt-12">
        <!-- Name field -->
        <aplz-ui-form-field>
          <aplz-ui-form-label>Nombre de la campaña</aplz-ui-form-label>
          <input type="text" aplzFormInput formControlName="name" />
          <ng-container aplzFormError>
            @if (nameControl.touched && nameControl.hasError('required')) {
              <p>El nombre es requerido</p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <aplz-ui-form-datepicker
          [minDate]="today"
          [formControl]="dateControl"
          [centerText]="true"
          [rangeEnabled]="true"
          legend="Seleccione fecha inicial y final *">
        </aplz-ui-form-datepicker>

        <aplz-ui-form-field>
          <aplz-ui-form-label>Terminos y condiciones Link</aplz-ui-form-label>
          <input type="text" aplzFormInput formControlName="tyc" />
          <ng-container aplzFormHint>
            <p>Link válido. Ej. https://cdn.aplazo.mx/reglas/enero25.pdf</p>
          </ng-container>
          <ng-container aplzFormError>
            @if (tycControl.touched && tycControl.hasError('pattern')) {
              <p>
                El link no es válido. Por favor introduce un formato valido. Ej.
                https://cdn.aplazo.mx/reglas/enero25.pdf
              </p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <app-prize-distribution formControlName="prizeDistribution">
        </app-prize-distribution>

        <div class="flex justify-end mb-8 mt-10 gap-4">
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="close(false)">
            Cancelar
          </button>
          <button
            aplzButton
            type="submit"
            aplzAppearance="solid"
            aplzColor="dark"
            size="md"
            [disabled]="form.disabled">
            <span class="mr-2">{{ data?.id ? 'Actualizar' : 'Crear' }}</span>
            campaña
          </button>
        </div>
      </form>
    </aplz-ui-card>
  `,
  imports: [
    ReactiveFormsModule,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
    AplazoCardComponent,
    AplazoFormDatepickerComponent,
    PrizeDistributionComponent,
  ],
})
export class CampaignFormComponent {
  readonly #dialogRef: DialogRef<any, any> = inject(DialogRef);
  readonly #notifier = inject(NotifierService);
  readonly #createOne = inject(CreateOneCampaignUsecase);
  readonly #updateOne = inject(UpdateOneCampaignUsecase);

  readonly data = this.#dialogRef.data;
  readonly today = todayFormated;

  readonly nameControl = new FormControl<string>(
    {
      value: this.data?.name ?? '',
      disabled: this._isCampaignExpired(),
    },
    {
      nonNullable: true,
      validators: [Validators.required, Validators.minLength(3)],
    }
  );

  readonly dateControl = new FormControl<Date[] | null>(
    {
      value:
        this.data?.startDate && this.data?.endDate
          ? [this.data.startDate, this.data.endDate]
          : [],
      disabled: this._isCampaignExpired(),
    },
    {
      nonNullable: false,
      validators: [Validators.required],
    }
  );

  readonly tycControl = new FormControl<string>(
    {
      value: this.data?.termsConditions ?? '',
      disabled: this._isCampaignExpired(),
    },
    {
      nonNullable: false,
      validators: [Validators.pattern(TYC_REGEXP)],
    }
  );

  readonly prizeDistributionControl = new FormControl<{
    numberOfWinners: number;
    prizes: PrizeDistribution[] | null;
  }>(
    {
      value: {
        numberOfWinners: this.data?.numberOfWinners ?? 0,
        prizes: this.data?.prizes ?? null,
      },
      disabled: this._isCampaignExpired(),
    },
    {
      nonNullable: true,
    }
  );

  readonly form = new FormGroup({
    name: this.nameControl,
    date: this.dateControl,
    tyc: this.tycControl,
    prizeDistribution: this.prizeDistributionControl,
  });

  close(confirmation: boolean) {
    this.#dialogRef.close({
      hasConfirmation: confirmation,
      data: confirmation
        ? {
            name: this.nameControl.value,
            dateRange: this.dateControl.value,
            tyc: this.tycControl.value,
            ...this.prizeDistributionControl.value,
          }
        : null,
    });
  }

  finish(): void {
    this.form.markAllAsTouched();
    this.prizeDistributionControl.markAllAsTouched();
    this.prizeDistributionControl.markAsTouched();
    this.prizeDistributionControl.updateValueAndValidity();

    if (this.form.invalid) {
      this.#notifier.warning({
        title:
          'Por favor, completa los campos requeridos o corrije los errores indicados.',
      });
      return;
    }

    const name = this.nameControl.value;
    const [startDate, endDate] = this.dateControl.value ?? [];
    const tyc = this.tycControl.value ?? '';
    const prizeDistribution = this.prizeDistributionControl.value ?? {
      numberOfWinners: 0,
      prizes: null,
    };
    const { numberOfWinners, prizes } = prizeDistribution;
    const request: PremiosAplazoCampaignUIDto = {
      name,
      startDate,
      endDate,
      tyc,
      numberOfWinners: numberOfWinners ?? 0,
      prizes,
      id: this.data?.id ?? undefined,
    };

    const usecase$ =
      (this.data?.id ?? null) != null
        ? this.#updateOne.execute(request)
        : this.#createOne.execute(request);

    usecase$
      .pipe(
        take(1),
        tap(() => {
          this.close(true);
        }),
        catchError(() => of(void 0))
      )
      .subscribe();
  }

  private _isCampaignExpired(): boolean {
    return (
      this.data?.endDate &&
      this._getEndOfADay(this.data.endDate).getTime() < new Date().getTime()
    );
  }

  private _getEndOfADay(entry: Date): Date {
    const date = new Date(entry);
    date.setHours(23, 59, 59, 999);
    return date;
  }
}
