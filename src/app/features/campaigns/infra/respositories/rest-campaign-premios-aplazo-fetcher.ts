import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import {
  CampaignWinner,
  PremiosAplazoCampaignUpsertDto,
} from '../../domain/entities';
import {
  CampaignListResponse,
  CampaignsRepository,
  SimplifiedCampaignResponse,
} from '../../domain/repositories/premios-aplazo';

@Injectable()
export class CampaignsWithHttp extends CampaignsRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);

  readonly #baseUrl = this.#environment.gatewayUrl;

  createOne(req: PremiosAplazoCampaignUpsertDto): Observable<void> {
    return this.#http.post<void>(
      `${this.#baseUrl}/api/v1/merchant-rewards/campaign`,
      {
        ...req,
      }
    );
  }

  findCampaigns(filters: {
    pageNum: number;
  }): Observable<CampaignListResponse> {
    const url = `${this.#baseUrl}/api/v1/merchant-rewards/campaign/page`;

    return this.#http.get<CampaignListResponse>(url, {
      params: {
        pageNum: filters.pageNum.toString(),
        pageSize: '10',
        sortBy: 'id',
        sortOrder: 'desc',
      },
    });
  }

  updateOne(args: PremiosAplazoCampaignUpsertDto): Observable<void> {
    return this.#http.put<void>(
      `${this.#baseUrl}/api/v1/merchant-rewards/campaign/${args.id}`,
      {
        name: args.name,
        startDate: args.startDate,
        endDate: args.endDate,
        termsConditions: args.termsConditions,
        numberOfWinners: args.numberOfWinners,
        prizes: args.prizes,
      }
    );
  }

  getWinnersBy(filters: { campaignId: number }): Observable<CampaignWinner[]> {
    return this.#http.get<CampaignWinner[]>(
      `${this.#baseUrl}/api/v1/merchant-rewards/campaigns/${filters.campaignId}/winners`
    );
  }

  confirmPrizeReceived(args: {
    campaignId: number;
    winnerId: number;
    prizeReceived: boolean;
  }): Observable<void> {
    return this.#http.patch<void>(
      `${this.#baseUrl}/api/v1/merchant-rewards/campaigns/${args.campaignId}/confirm/${args.winnerId}`,
      {
        prizeReceived: args.prizeReceived,
      }
    );
  }
  getLastOnes(): Observable<SimplifiedCampaignResponse> {
    return this.#http.get<SimplifiedCampaignResponse>(
      `${this.#baseUrl}/api/v1/merchant-rewards/campaign/retrieve`
    );
  }
}
