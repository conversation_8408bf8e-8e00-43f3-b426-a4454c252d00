import { Route } from '@angular/router';
import { ROUTES_CONFIG } from '../../../core/domain/route-names';
import { CampaignsLayoutComponent } from '../layout/campaigns-layout.component';

export default [
  {
    path: '',
    redirectTo: ROUTES_CONFIG.campaigns,
    pathMatch: 'full',
  },

  {
    path: ROUTES_CONFIG.campaigns,
    component: CampaignsLayoutComponent,
    children: [
      {
        path: '',
        redirectTo: ROUTES_CONFIG.merchantCampaignsAdmin,
        pathMatch: 'full',
      },

      {
        path: ROUTES_CONFIG.merchantCampaignsAdmin,
        loadComponent: () =>
          import('../pages/listing-campaigns/listing-campaigns.component').then(
            stl => stl.ListingCampaignsComponent
          ),
        data: {
          title: 'Campañas Admin',
        },
      },
      {
        path: ROUTES_CONFIG.merchantCampaignsWinners,
        loadComponent: () =>
          import(
            '../pages/listing-campaign-winners/listing-campaign-winners.component'
          ).then(stl => stl.ListingCampaignsWinnersComponent),
        data: {
          title: 'Ganadores Admin',
        },
      },
    ],
  },
] satisfies Route[];
