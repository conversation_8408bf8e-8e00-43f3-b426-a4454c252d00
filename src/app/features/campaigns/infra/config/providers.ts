import {
  EnvironmentProviders,
  makeEnvironmentProviders,
  Provider,
} from '@angular/core';
import { LoaderService, LoaderServiceWithQueue } from '@aplazo/merchant/shared';
import { ConfirmPrizeReceivedUsecase } from '../../application/use_cases/confirm-prize-received.usecase';
import { CreateOneCampaignUsecase } from '../../application/use_cases/create-one.usecase';
import { FetchAPremiosAplazoUsecase } from '../../application/use_cases/fetch-premios-aplazo.usecase';
import { GetLastCampaignsUseCase } from '../../application/use_cases/get-last-campaings.usecase';
import { GetWinnersByCampaign } from '../../application/use_cases/get-winners.usecase';
import { PrizeDistributionUsecase } from '../../application/use_cases/prize-distribution.usecase';
import { UpdateOneCampaignUsecase } from '../../application/use_cases/update-one.usecase';
import { CampaignsRepository } from '../../domain/repositories/premios-aplazo';
import { CampaignsWithHttp } from '../respositories/rest-campaign-premios-aplazo-fetcher';

export function provideCampaignsRepositories(
  opt?: () => Provider
): EnvironmentProviders {
  const providers: Provider[] = [
    CreateOneCampaignUsecase,
    UpdateOneCampaignUsecase,
    PrizeDistributionUsecase,
    FetchAPremiosAplazoUsecase,
    {
      provide: CampaignsRepository,
      useClass: CampaignsWithHttp,
    },
    GetWinnersByCampaign,
    ConfirmPrizeReceivedUsecase, // Añadir esta línea
    GetLastCampaignsUseCase, // Add the new usecase
  ];

  if (opt != null && typeof opt === 'function') {
    providers.push(opt());
  }

  return makeEnvironmentProviders([...providers]);
}

export function withScopedLoader(): () => Provider {
  return () => ({
    provide: LoaderService,
    useClass: LoaderServiceWithQueue,
  });
}
