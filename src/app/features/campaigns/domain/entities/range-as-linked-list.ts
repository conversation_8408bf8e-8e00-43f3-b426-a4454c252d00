export class RangeNode {
  private end: number;
  private name: string;
  private next: RangeNode | null = null;

  constructor(end: number, name = '') {
    this.end = end;
    this.name = name;
  }

  getValue(): {
    end: number;
    start: number;
    name: string;
  } {
    const nextNode = this.next;
    const start = nextNode ? nextNode.end + 1 : 1;

    return {
      end: this.end,
      start,
      name: this.name,
    };
  }

  setValue(end: number, name = '') {
    this.end = end;
    this.name = name;
  }

  setNext(node: RangeNode | null) {
    this.next = node;
  }

  setName(name: string) {
    this.name = name;
  }

  getNext(): RangeNode | null {
    return this.next;
  }
}

export class Ranges {
  head: RangeNode | null = null;

  constructor(initialValue?: number) {
    this.head = initialValue ? new RangeNode(initialValue) : null;
  }

  addMany(values: { end: number; name: string }[]) {
    const mapped = values.slice().sort((a, b) => b.end - a.end);

    if (mapped.length === 0) {
      return;
    }

    if (!this.head) {
      this.head = new RangeNode(mapped[0].end, mapped[0].name);
    }

    let current: RangeNode | null = this.head;

    for (let i = 1; i < mapped.length; i++) {
      const { end, name } = mapped[i];

      current?.setNext(new RangeNode(end, name));
      current = current?.getNext() ?? null;
    }
  }

  addOne(name = '', current: RangeNode | null = this.head) {
    if (!current) {
      throw new Error('Cannot add a range to an empty list');
    }

    const { end: currentEnd, start: currentStart } = current.getValue();
    const next = current.getNext();

    if (currentEnd === currentStart && !next) {
      throw new Error('Cannot add a range to a single node');
    }

    if (currentEnd === currentStart && next) {
      this.addOne(name, next);
      return;
    }

    const middle = Math.floor((currentEnd + currentStart) / 2);

    const newNode = new RangeNode(middle, name);

    if (next) {
      newNode.setNext(next);
      current.setNext(newNode);
      return;
    }

    current.setNext(newNode);
  }

  deleteOneByEnd(end: number) {
    if (!end) {
      throw new Error('Cannot delete a range with an empty end');
    }

    if (!this.head) {
      throw new Error('Cannot delete a range from an empty list');
    }

    if (end > this.head.getValue().end || end < 1) {
      throw new Error('Cannot delete a range that does not exist');
    }

    let current: RangeNode | null = this.head;

    if (
      current != null &&
      current.getNext() == null &&
      current.getValue().end === end
    ) {
      this.head = null;
      return;
    }

    while (current != null && current?.getNext() != null) {
      const { end: currentEnd } = current.getValue();

      if (currentEnd === end) {
        current.setNext(current.getNext()?.getNext() ?? null);
      }
      current = current.getNext();
    }
  }

  updateOneByEnd(endToFind: number, newEnd: number, name = ''): void | never {
    if (!this.head) {
      throw new Error('Cannot update a range in an empty list');
    }

    let current: RangeNode | null = this.head;

    while (current != null && current.getNext() != null) {
      const { start: currentStart, end: currentEnd } = current.getValue();
      const { start: nextStart, end: nextEnd } =
        current.getNext()!.getValue() ?? {};

      if (
        endToFind === nextEnd &&
        newEnd < currentStart &&
        newEnd >= nextStart
      ) {
        current.getNext()?.setValue(newEnd, name);
      } else if (
        endToFind === nextEnd &&
        newEnd >= currentStart &&
        newEnd < currentEnd
      ) {
        current.getNext()?.setValue(newEnd, name);
      } else if (
        endToFind === nextEnd &&
        newEnd >= currentStart &&
        newEnd === currentEnd
      ) {
        current.setNext(current.getNext()?.getNext() ?? null);
        current.setName(name);
      } else if (
        endToFind === currentEnd &&
        newEnd === nextStart &&
        newEnd <= nextEnd
      ) {
        current.setNext(current.getNext()?.getNext() ?? null);
        current.setName(name);
      } else if (endToFind === currentEnd && endToFind === newEnd) {
        current.setName(name);
        return;
      }

      current = current.getNext();
    }

    if (
      current != null &&
      current.getNext() == null &&
      current.getValue().end === endToFind &&
      endToFind === newEnd
    ) {
      current.setName(name);
    }
  }

  display(): void {
    let current: RangeNode | null = this.head;

    while (current?.getNext() != null) {
      const { end, start } = current.getValue();
      console.log(`${end}-${start} -> `);
      current = current.getNext() ?? null;
    }

    const { end, start } = current?.getValue() ?? {};
    console.log(`${end}-${start} -> end`);
  }

  getValueDescending(): { end: number; start: number; name: string }[] {
    let current: RangeNode | null = this.head;
    const serialized = [];

    while (current?.getNext() != null) {
      const { end, start, name } = current.getValue();

      serialized.push({ end, start, name });

      current = current.getNext() ?? null;
    }

    const lastNode = current;
    if (lastNode) {
      const { end, start, name } = lastNode.getValue();
      serialized.push({ end, start, name });
    }

    return serialized;
  }

  getValue(): { end: number; start: number; name: string }[] {
    let current: RangeNode | null = this.head;
    const serialized = [];

    while (current?.getNext() != null) {
      const { end, start, name } = current.getValue();

      serialized.unshift({ end, start, name });

      current = current.getNext() ?? null;
    }

    const lastNode = current;
    if (lastNode) {
      const { end, start, name } = lastNode.getValue();
      serialized.unshift({ end, start, name });
    }

    return serialized;
  }
}
