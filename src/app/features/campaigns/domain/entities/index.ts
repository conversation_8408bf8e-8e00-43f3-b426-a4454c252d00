import {
  RawDateDayFirst,
  RawDateYearFirstWithHyphen,
} from '@aplazo/merchant/shared';

export interface PremiosAplazoCampaign {
  createdAt: string;
  id: number;
  name: string;
  startDate: RawDateYearFirstWithHyphen;
  endDate: RawDateYearFirstWithHyphen;
  active: boolean;
  createdBy: string;
  termsConditions: string | null;
  numberOfWinners: number;
  prizes?: PrizeDistribution[];
}

export interface PremiosAplazoCampaignUIDto {
  name: string;
  startDate: Date;
  endDate: Date;
  tyc: string;
  numberOfWinners: number;
  prizes: PrizeDistribution[] | null;
  id?: number;
}

export interface PremiosAplazoCampaignUpsertDto
  extends Omit<PremiosAplazoCampaignUIDto, 'startDate' | 'endDate' | 'tyc'> {
  startDate: RawDateDayFirst;
  endDate: RawDateDayFirst;
  termsConditions: string;
}

export interface PremiosAplazoCampaignUpdate {
  id: number;
  name: string;
  startDate: string;
  endDate: string;
}

export interface UpdateCampaigntDto
  extends Partial<PremiosAplazoCampaignUpdate> {
  id: number;
}

export interface PrizeDistribution {
  fromRank: number;
  toRank: number;
  prizeName: string;
}

export interface CampaignWinner {
  id: number;
  position: number;
  participantName: string;
  usersRegistered: number;
  address: string;
  prize: string;
  prizeReceived: boolean;
}

export interface CampaignWinnerErrorResponse {
  code: string;
  data: Record<string, unknown>;
  error: string;
  timestamp: number;
  message: string;
  path: string;
}
