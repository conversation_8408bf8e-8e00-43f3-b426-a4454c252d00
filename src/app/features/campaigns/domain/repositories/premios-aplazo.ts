import { Observable } from 'rxjs';
import {
  CampaignWinner,
  PremiosAplazoCampaign,
  PremiosAplazoCampaignUpsertDto,
} from '../entities';

export interface ErrorResponse {
  code: string;
  error: string;
  timestamp: number;
  message: string;
  path: string;
}

export interface CampaignListResponse {
  totalPages: number;
  totalElements: number;
  content: PremiosAplazoCampaign[];
}

export interface SimplifiedCampaignResponse {
  campaigns: {
    id: number;
    name: string;
  }[];
}

export abstract class CampaignsRepository {
  abstract findCampaigns(filters: {
    pageNum: number;
  }): Observable<CampaignListResponse>;

  abstract createOne(args: PremiosAplazoCampaignUpsertDto): Observable<void>;

  abstract updateOne(args: PremiosAplazoCampaignUpsertDto): Observable<void>;

  abstract getWinnersBy(filters: {
    campaignId: number;
  }): Observable<CampaignWinner[]>;

  abstract confirmPrizeReceived(args: {
    campaignId: number;
    winnerId: number;
    prizeReceived: boolean;
  }): Observable<void>;

  abstract getLastOnes(): Observable<SimplifiedCampaignResponse>;
}
