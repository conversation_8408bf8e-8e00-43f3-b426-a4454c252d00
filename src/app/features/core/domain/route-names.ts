export const ROUTES_CONFIG = {
  login: 'login',
  board: 'app',
  merchantCreation: 'creation',
  merchantAdmin: 'admin',
  merchantSettingsSearch: 'search',
  merchantSettingsBasics: 'basics',
  merchantSettingsInfo: 'info',
  merchantSettingsAccount: 'account',
  merchantSettingsContact: 'contact',
  merchantSettingsStatus: 'status',
  merchantSettingsMdr: 'mdr',
  merchantSettingsBilling: 'billing',
  merchantSettingsBranches: 'branches',
  merchantSettingsUsers: 'users-settings',

  merchantPaymentsSummary: 'summary',
  merchantCampaignsAdmin: 'admin-campaigns',
  merchantCampaignsWinners: 'admin-winners',

  infraSquad: 'infra-squad',
  providerOrder: 'provider-order',

  // below are the keys defined by the backend
  payments: 'merchant_payment',
  campaigns: 'merchant_premios_aplazo_admin',

  receipts: 'merchant_payment_receipt',
  onboarding: 'merchant_onboarding',
  unavailable: 'unavailable',

  // New routes for Tarjetas section
  cards: 'cards',
  cardsLocations: 'cards_locations',
  cardsBulkOrders: 'cards_bulk_orders',
} as const;

export type HelicarrierRouteKey = keyof typeof ROUTES_CONFIG;
export type HelicarrierRoute = (typeof ROUTES_CONFIG)[HelicarrierRouteKey];
