import {
  EnvironmentProviders,
  InjectionToken,
  makeEnvironmentProviders,
} from '@angular/core';

export type ShieldEnvironments = Readonly<{
  googleClientId: string;
  apiBaseUrl: string;
  bifrostUrl: string;
  i18nUrl: string;
  landingUrl: string;
  gatewayUrl: string;
  pcustomCardUrl: string;
  featureFlagsApiKey: string;
  featureFlagsEnv: string;
}>;

const googleClientId = import.meta.env.NG_APP_GOOGLE_CLIENT_ID;
const apiBaseUrl = import.meta.env.NG_APP_API_BASE_URL;
const bifrostUrl = import.meta.env.NG_APP_BIFROST_URL;
const i18nUrl = import.meta.env.NG_APP_I18N_URL;
const landingUrl = import.meta.env.NG_APP_LANDING_URL;
const gatewayUrl = import.meta.env.NG_APP_GATEWAY_URL;
const pcustomCardUrl = import.meta.env.NG_APP_PCUSTOM_CARD_URL;
const featureFlagsApiKey = import.meta.env.NG_APP_FEATURE_FLAGS_API_KEY;
const featureFlagsEnv = import.meta.env.NG_APP_FEATURE_FLAGS_ENV;

export const shieldEnvs: ShieldEnvironments = {
  googleClientId,
  apiBaseUrl,
  bifrostUrl,
  i18nUrl,
  landingUrl,
  gatewayUrl,
  pcustomCardUrl,
  featureFlagsApiKey,
  featureFlagsEnv,
} as const;

export const SHIELD_ENVIRONMENT = new InjectionToken<ShieldEnvironments>(
  'SHIELD_ENVIRONMENT'
);

export function provideEnvironmentVariables(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: SHIELD_ENVIRONMENT,
      useValue: shieldEnvs,
    },
  ]);
}
