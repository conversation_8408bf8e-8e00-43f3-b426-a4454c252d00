import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { StatsigService } from '@statsig/angular-bindings';
import { ROUTES_CONFIG } from '../../domain/route-names';

export const isUnderMaintenanceGuard: CanActivateFn = (_, state) => {
  const service = inject(StatsigService);
  const router = inject(Router);

  const isMaintenanceEnabled = service.checkGate(
    'b2b_front_maintenance_screen_enabled'
  );

  const isMaintenanceScreen = state.url.includes(ROUTES_CONFIG.unavailable);

  if (isMaintenanceEnabled && !isMaintenanceScreen) {
    return router.parseUrl(`/${ROUTES_CONFIG.unavailable}`);
  }

  if (!isMaintenanceEnabled && isMaintenanceScreen) {
    return router.parseUrl(`/${ROUTES_CONFIG.login}`);
  }

  return true;
};
