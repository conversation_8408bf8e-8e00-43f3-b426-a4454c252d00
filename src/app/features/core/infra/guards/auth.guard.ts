import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { lastValueFrom, take } from 'rxjs';
import { UserStore } from '../../../login/application/services/user.store';
import { ROUTES_CONFIG } from '../../domain/route-names';

export const loggedInUsersOnly: CanActivateFn = async () => {
  const userStore = inject(UserStore);
  const router = inject(Router);

  const isLoggedIn = await lastValueFrom(userStore.isLoggedIn$.pipe(take(1)));

  if (!isLoggedIn) {
    userStore.clearUser();

    return router.parseUrl('/');
  }

  return isLoggedIn;
};

export const loggedOffUsersOnly: CanActivateFn = async () => {
  const userStore = inject(UserStore);
  const router = inject(Router);

  const isLoggedIn = await lastValueFrom(userStore.isLoggedIn$.pipe(take(1)));

  if (isLoggedIn) {
    return router.parseUrl(`/${ROUTES_CONFIG.board}`);
  }

  return !isLoggedIn;
};
