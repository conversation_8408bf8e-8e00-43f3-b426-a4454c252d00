import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { map, mergeMap, withLatestFrom } from 'rxjs';
import { UserStore } from '../../../login/application/services/user.store';

export const tokenInterceptor: HttpInterceptorFn = (request, next) => {
  const store = inject(UserStore);

  const isFileSkippedByExtension = RegExp(/\.svg$|\.json$/).exec(request.url);

  if (isFileSkippedByExtension) {
    return next(request);
  }

  return store.isLoggedIn$.pipe(
    withLatestFrom(store.tokenSession$),
    map(([isLoggedIn, token]) =>
      isLoggedIn
        ? request.clone({
            headers: request.headers.set(
              'Authorization',
              `Bearer ${token.replace('Bearer ', '')}`
            ),
          })
        : request
    ),
    mergeMap(newReq => next(newReq))
  );
};
