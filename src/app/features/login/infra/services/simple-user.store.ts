import { Injectable } from '@angular/core';
import { BehaviorSubject, map, shareReplay, skip } from 'rxjs';
import { UserStorePersistanceDto } from '../../application/dtos/user-store-persistance.dto';
import { UserStore } from '../../application/services/user.store';
import { User } from '../../domain/entities/user';

export const emptyUser: UserStorePersistanceDto = {
  email: '',
  roles: [],
  isLoggedIn: false,
  accessToken: '',
  id: '',
  lastLogin: new Date('January 1, 1970 00:00:00 UTC'),
};

@Injectable({
  providedIn: 'root',
})
export class SimpleUserStore implements UserStore {
  readonly #user$ = new BehaviorSubject<UserStorePersistanceDto>(emptyUser);

  user$ = this.#user$.pipe(shareReplay(1), skip(1));
  email$ = this.#user$.pipe(map(u => u.email));
  roles$ = this.#user$.pipe(map(u => u.roles));
  isLoggedIn$ = this.#user$.pipe(map(u => u.isLoggedIn));
  tokenSession$ = this.#user$.pipe(map(u => u.accessToken));

  setUser(user: Required<Omit<User, 'setEmail' | 'setAccessToken'>>): void {
    this.#user$.next({
      email: user.email,
      roles: user.roles,
      isLoggedIn: true,
      accessToken: user.accessToken,
      id: user.id,
      lastLogin: user.lastLogin,
    });
  }
  clearUser(): void {
    this.#user$.next(emptyUser);
  }
}
