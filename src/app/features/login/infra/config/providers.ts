import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { UserStore } from '../../application/services/user.store';
import { LoginRepository } from '../../domain/login.repository';
import { LoginWithHttpRepository } from '../repositories/login-with-http.repository';
import { SimpleUserStore } from '../services/simple-user.store';

export function provideUserStore(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: UserStore,
      useClass: SimpleUserStore,
    },
  ]);
}

export function provideLoginRepository(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: LoginRepository,
      useClass: LoginWithHttpRepository,
    },
  ]);
}
