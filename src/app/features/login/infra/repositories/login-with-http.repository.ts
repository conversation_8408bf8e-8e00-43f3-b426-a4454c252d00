import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import { LoginRequest, LoginResponse } from '../../application/dtos/login.dto';
import { LoginRepository } from '../../domain/login.repository';

@Injectable({
  providedIn: 'root',
})
export class LoginWithHttpRepository
  implements LoginRepository<LoginRequest, Observable<LoginResponse>>
{
  readonly #http = inject(HttpClient);
  readonly #environment = inject(SHIELD_ENVIRONMENT);

  execute(request: LoginRequest): Observable<LoginResponse> {
    return this.#http.post<LoginResponse>(
      this.#environment.apiBaseUrl + '/api/v1/account/login',
      request
    );
  }
}
