import {
  AfterViewInit,
  Component,
  ElementRef,
  inject,
  Ng<PERSON><PERSON>,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { BaseSSOProvider } from '@aplazo/front-social-sso';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { StatsigService } from '@statsig/angular-bindings';
import { ROUTES_CONFIG } from '../../../core/domain/route-names';
import { SingleSSOResponse } from '../../application/dtos/single-sso-response.dto';
import { LoginUsecase } from '../../application/usecases/login.usecase';

@Component({
  selector: 'app-login',
  imports: [AplazoLogoComponent],
  template: `
    <section class="w-full md:max-w-sm mx-auto pt-8">
      <aplz-ui-logo></aplz-ui-logo>

      <h1 class="max-w-sm text-center font-medium text-lg my-6">
        Inicia sesión con tu correo electrónico de Aplazo
      </h1>

      <article class="flex items-center justify-center my-7">
        <div #googleGSI></div>
      </article>
    </section>
  `,
})
export class LoginComponent implements AfterViewInit {
  @ViewChild('googleGSI', { static: false, read: ElementRef<HTMLDivElement> })
  googleGSI!: ElementRef<HTMLDivElement>;
  readonly #googleSSO = inject(BaseSSOProvider);
  readonly #loginUsecase = inject(LoginUsecase);
  readonly #router = inject(Router);
  readonly #zone = inject(NgZone);
  readonly #featureFlags = inject(StatsigService);

  ngAfterViewInit(): void {
    this.#googleSSO.init({
      elementRef: this.googleGSI,
      responseHandler: this.onSuccessSSO,
    });
  }

  onSuccessSSO = (response: SingleSSOResponse) => {
    /**
     * The explicit invocation of zone.run is because
     * this method is a callback that is executed inside
     * the scope of Google GSI object so, in the execution
     * the "this" object is referenced to the GSI object
     * and Angular is not aware of it.
     */
    this.#zone.run(() => {
      this.#loginUsecase.execute(response.credential).subscribe(user => {
        this.#router.navigate([ROUTES_CONFIG.board]);

        this.#featureFlags.updateUserAsync({
          ...this.#featureFlags.getClient()?.getContext().user,
          custom: {
            ...this.#featureFlags.getClient()?.getContext().user.custom,
            roles: user?.roles.join(','),
            last_login: user?.lastLogin.getTime().toString(),
          },
          email: user?.email ?? undefined,
          userID: user ? `${user.id}-${user.email}` : undefined,
        });

        this.#featureFlags.logEvent(
          'shield_front_login',
          user?.email ?? 'unknown',
          {
            email: user?.email ?? 'unhandled-email',
            last_login:
              user?.lastLogin.getTime().toString() ?? 'unhandled-last-login',
            roles: user?.roles.join(',') ?? 'unhandled-roles',
            user_id: user?.id ?? 'unhandled-user-id',
          }
        );
      });
    });
  };
}
