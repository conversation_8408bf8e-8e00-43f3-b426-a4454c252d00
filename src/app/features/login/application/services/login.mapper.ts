import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { User } from '../../domain/entities/user';
import { LoginRequest } from '../dtos/login.dto';
import { UserStorePersistanceDto } from '../dtos/user-store-persistance.dto';

export const fromDomainToPersistence = (
  user: User
): UserStorePersistanceDto => {
  return {
    id: user.id,
    email: user.email,
    roles: user.roles,
    accessToken: user.accessToken,
    lastLogin: user.lastLogin,
    isLoggedIn: user.isLoggedIn,
  };
};

export const fromPersistenceToDomain = (
  userPersisted: UserStorePersistanceDto
): User => {
  const newUser = User.create({
    id: userPersisted.id,
    email: userPersisted.email,
    roles: userPersisted.roles,
  });

  newUser.setAccessToken(userPersisted.accessToken);

  return newUser;
};

export const nullOrUndefinedLoginRequestMessage =
  'Intente más tarde o contacte a soporte';

export const emptyLoginRequestMessage =
  'Token de Google no ha sido proporcionado';

export const fromTokenToRepositoryRequest = (token: string): LoginRequest => {
  if (!Guard.againstNullOrUndefined({ token }, 'token').succeeded) {
    throw new RuntimeMerchantError(
      nullOrUndefinedLoginRequestMessage,
      'loginRequestMapper::nullToken'
    );
  }

  if (!Guard.againstEmptyValue({ token }, 'token').succeeded) {
    throw new RuntimeMerchantError(
      emptyLoginRequestMessage,
      'loginRequestMapper::emptyToken'
    );
  }

  return {
    googleToken: token,
  };
};
