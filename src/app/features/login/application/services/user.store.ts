import { Observable } from 'rxjs';
import { UserStorePersistanceDto } from '../dtos/user-store-persistance.dto';

export abstract class UserStore {
  abstract email$: Observable<string>;
  abstract roles$: Observable<string[]>;
  abstract isLoggedIn$: Observable<boolean>;
  abstract tokenSession$: Observable<string>;
  abstract setUser(user: UserStorePersistanceDto): void;
  abstract clearUser(): void;
}
