import { inject, Injectable } from '@angular/core';
import {
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  EMPTY,
  exhaustMap,
  map,
  MonoTypeOperatorFunction,
  Observable,
  of,
  OperatorFunction,
  pipe,
  take,
  tap,
} from 'rxjs';
import { MenuStore } from '../../../shared/application/services/menu.store';
import { User } from '../../domain/entities/user';
import { VALID_SHIELD_ROLES } from '../../domain/entities/valid-roles';
import { LoginRepository } from '../../domain/login.repository';
import { LoginRequest, LoginResponse } from '../dtos/login.dto';
import {
  fromDomainToPersistence,
  fromTokenToRepositoryRequest,
} from '../services/login.mapper';
import { UserStore } from '../services/user.store';

export const defaultControlledErrorTitle = 'Hemos detectado un error';
export const defaultUncontrolledErrorTitle = 'Parece que algo salio mal';
export const unauthorizedControlledErrorTitle = 'Contacte a su administrador';

@Injectable({ providedIn: 'any' })
export class LoginUsecase {
  readonly #repository: LoginRepository<
    LoginRequest,
    Observable<LoginResponse>
  > = inject(LoginRepository);
  readonly #loader = inject(LoaderService);
  readonly #userStore = inject(UserStore);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #menuStore = inject(MenuStore);

  execute(token: string): Observable<User> {
    const idLoader = this.#loader.show();

    try {
      const repoParams = fromTokenToRepositoryRequest(token);

      return this.#repository.execute(repoParams).pipe(
        this.#fromTokenToUser(),
        tap(() => {
          this.#loader.hide(idLoader);
        }),
        catchError(err => {
          this.#loader.hide(idLoader);

          return this.#errorHandler.handle(err, EMPTY);
        }),
        take(1)
      );
    } catch (error: unknown) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(error, EMPTY);
    }
  }

  #fromTokenToUser(): OperatorFunction<LoginResponse, User> {
    return pipe(
      this.#validateRoles(),

      this.#mapFromServiceToDomain(),

      this.#hydrateMenu(),

      this.#persistUser()
    );
  }

  #validateRoles(): MonoTypeOperatorFunction<LoginResponse> {
    return pipe(
      map(response => {
        if (!response || !response.account) {
          throw new RuntimeMerchantError(
            (response as any).error ??
              'Error en el servicio. Intente más tarde o contacte a soporte',
            'Login::emptyResponse',
            'UserLogin'
          );
        }

        const roles = response.account.roles;

        if (!Array.isArray(roles)) {
          throw new RuntimeMerchantError(
            'El usuario no tiene privilegios para iniciar sesión',
            'Login::invalidRoles',
            'UserLogin'
          );
        }

        if (roles.length === 0) {
          throw new RuntimeMerchantError(
            'El usuario no tiene privilegios para iniciar sesión',
            'Login::emptyRoles',
            'UserLogin'
          );
        }

        if (roles.length === 1 && roles[0] === VALID_SHIELD_ROLES.generic) {
          throw new RuntimeMerchantError(
            'El usuario no tiene privilegios para iniciar sesión',
            'Login::genericRole',
            'UserLogin'
          );
        }

        return response;
      })
    );
  }

  #mapFromServiceToDomain(): OperatorFunction<
    LoginResponse,
    { user: User; response: LoginResponse }
  > {
    return pipe(
      map(response => {
        const newUser = User.create({
          id: response.account.id,
          email: response.account.username,
          roles: response.account.roles,
        });

        newUser.setAccessToken(response.authorization);

        return { user: newUser, response };
      })
    );
  }

  #hydrateMenu(): MonoTypeOperatorFunction<{
    user: User;
    response: LoginResponse;
  }> {
    return pipe(
      tap(({ response }) => {
        this.#menuStore.setMenu(response.menu);
      })
    );
  }

  #persistUser(): OperatorFunction<
    { user: User; response: LoginResponse },
    User
  > {
    return pipe(
      exhaustMap(({ user }) =>
        of(this.#userStore.setUser(fromDomainToPersistence(user))).pipe(
          map(() => user)
        )
      )
    );
  }
}
