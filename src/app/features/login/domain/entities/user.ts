import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { UserEmail } from './user-email';

export interface UserProps {
  id: string;
  roles: string[];
  email: string;
  accessToken?: string;
  lastLogin?: Date;
}
export const userNullishIdDefaultErrorMessage =
  'El id del usuario no puede ser nulo';
export const userNullishEmailDefaultErrorMessage =
  'El email del usuario no puede ser nulo';
export const userNullishRoleDefaultErrorMessage =
  'El usuario necesita tener un rol activo';

export class User {
  readonly #epochDate = new Date('January 1, 1970 00:00:00 UTC');
  readonly id: string;
  readonly roles: string[];
  #email: UserEmail;
  #accessToken: string | undefined;
  #lastLogin: Date | undefined;

  static create(user: UserProps): User {
    if (!user.id) {
      throw new RuntimeMerchantError(
        userNullishIdDefaultErrorMessage,
        'NewUser::createUser::nullishId'
      );
    }

    if (!user.email) {
      throw new RuntimeMerchantError(
        userNullishEmailDefaultErrorMessage,
        'NewUser::createUser::nullishUsername'
      );
    }

    if (!user.roles || !Array.isArray(user.roles) || user.roles.length === 0) {
      throw new RuntimeMerchantError(
        userNullishRoleDefaultErrorMessage,
        'NewUser::createUser::emptyRole'
      );
    }

    const userEmail = UserEmail.create(user.email);

    return new User({ ...user, email: userEmail });
  }

  private constructor(user: Omit<UserProps, 'email'> & { email: UserEmail }) {
    this.id = user.id;
    this.roles = user.roles;
    this.#email = user.email;
    this.#accessToken = user.accessToken;
    this.#lastLogin = user.lastLogin;
  }

  get isLoggedIn(): boolean {
    return Boolean(this.#accessToken?.trim());
  }

  get accessToken(): string {
    return this.#accessToken ?? '';
  }

  get lastLogin(): Date {
    return this.#lastLogin ?? this.#epochDate;
  }

  get email(): string {
    return this.#email?.value ?? '';
  }

  setEmail(email: UserEmail): void {
    this.#email = email;
  }

  setAccessToken(token: string): void {
    this.#accessToken = token;
    this.#lastLogin = new Date();
  }
}
