import {
  Directive,
  inject,
  Input,
  OnInit,
  TemplateRef,
  ViewContainerRef,
} from '@angular/core';
import { HelicarrierRoute } from '../../../core/domain/route-names';
import { MenuStore } from '../../../shared/application/services/menu.store';

@Directive({
  standalone: true,
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: '[renderByMenu]',
})
export class RenderByMenuDirective implements OnInit {
  readonly #viewContainerRef = inject(ViewContainerRef);
  readonly #templateRef = inject(TemplateRef);
  readonly #menuStore = inject(MenuStore);

  @Input({ required: true })
  renderByMenu!: HelicarrierRoute[];

  ngOnInit(): void {
    const menu = this.#menuStore.getMenu();

    const menuHasSomeConcidence = menu.some(item =>
      this.renderByMenu.includes(item)
    );

    this.#viewContainerRef.clear();

    if (menuHasSomeConcidence) {
      this.#viewContainerRef.createEmbeddedView(this.#templateRef);
    }
  }
}
