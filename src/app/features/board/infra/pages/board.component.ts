import { AsyncPipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterLink,
  RouterOutlet,
} from '@angular/router';
import { GoogleSSOProvider } from '@aplazo/front-social-sso/google';
import { RedirectionService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoDashboardComponents } from '@aplazo/shared-ui/dashboard';
import { AplazoDetailsComponents } from '@aplazo/shared-ui/details';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoSidenavLinkComponent } from '@aplazo/shared-ui/sidenav';
import { iconBankNotes, iconChevronDown, iconSettings } from '@aplazo/ui-icons';
import { EMPTY, filter, map, startWith, switchMap } from 'rxjs';
import { ROUTES_CONFIG } from '../../../core/domain/route-names';
import { SHIELD_ENVIRONMENT } from '../../../core/infra/config/environments';
import { UserStore } from '../../../login/application/services/user.store';
import { RenderByMenuDirective } from '../directives/render-by-menu.directive';

@Component({
  selector: 'app-board',
  imports: [
    AsyncPipe,
    RouterLink,
    RouterOutlet,
    AplazoSidenavLinkComponent,
    AplazoDashboardComponents,
    AplazoButtonComponent,
    AplazoDropdownComponents,
    AplazoIconComponent,
    AplazoDetailsComponents,
    RenderByMenuDirective,
  ],
  templateUrl: './board.component.html',
})
export class BoardComponent {
  readonly #redirecter = inject(RedirectionService);
  readonly #router = inject(Router);
  readonly #route = inject(ActivatedRoute);
  readonly #userStore = inject(UserStore);
  readonly #googleSSO = inject(GoogleSSOProvider);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #environment = inject(SHIELD_ENVIRONMENT);
  readonly appRoutes = ROUTES_CONFIG;

  readonly title$ = this.#router.events.pipe(
    filter(event => event instanceof NavigationEnd),
    startWith(this.#getRoute(this.#route).snapshot?.data?.title || ''),
    switchMap(() => {
      const finalRoute = this.#getRoute(this.#route);

      return finalRoute.data.pipe(map(data => data['title'])) ?? EMPTY;
    })
  );

  readonly email$ = this.#userStore.email$;

  constructor() {
    this.#iconRegister.registerIcons([
      iconChevronDown,
      iconBankNotes,
      iconSettings,
    ]);
  }

  logout(): void {
    this.#googleSSO.preventAutoStart();
    this.#userStore.clearUser();
    this.#redirecter.internalNavigation(['/']);
  }

  landing() {
    if (this.#environment.landingUrl) {
      this.#redirecter.externalNavigation(
        this.#environment.landingUrl,
        '_blank'
      );
    }
  }

  #getRoute(route: ActivatedRoute): ActivatedRoute {
    let result = route;

    while (result.firstChild) {
      result = result.firstChild;
    }

    return result;
  }
}
