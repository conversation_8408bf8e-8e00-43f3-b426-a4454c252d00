<aplz-ui-dashboard (logoClick)="landing()">
  <aplz-ui-dash-header>
    <nav
      class="flex flex-grow flex-shrink-0 justify-between items-center w-full">
      <h2 class="font-medium text-2xl">
        {{ title$ | async }}
      </h2>

      <button
        aplzButton
        aplzAppearance="basic"
        size="md"
        class="items-center"
        [aplzDropdownTriggerFor]="logoutMenu">
        <span class="lowercase font-light">
          {{ email$ | async }}
        </span>
        <span class="ml-2">
          <aplz-ui-icon name="chevron-down" size="xs"></aplz-ui-icon>
        </span>
      </button>
      <aplz-ui-dropdown #logoutMenu>
        <aplz-ui-dropdown-item>
          <button (click)="logout()" class="px-4 py-2">Cerrar sesión</button>
        </aplz-ui-dropdown-item>
      </aplz-ui-dropdown>
    </nav>
  </aplz-ui-dash-header>

  <aplz-ui-dash-sidebar>
    <aplz-ui-details *renderByMenu="[appRoutes.payments, appRoutes.receipts]">
      <summary aplzDetailsHeader>
        <div class="flex items-center">
          <aplz-ui-icon name="bank-notes" size="sm"></aplz-ui-icon>
          <span class="ml-7">Pagos</span>
        </div>
      </summary>
      <a
        aplzSidenavLink
        *renderByMenu="[appRoutes.payments]"
        [routerLink]="[appRoutes.payments, appRoutes.merchantPaymentsSummary]">
        Historial de pagos
      </a>

      <a
        aplzSidenavLink
        *renderByMenu="[appRoutes.receipts]"
        [routerLink]="[appRoutes.payments, appRoutes.receipts]">
        Generar Recibo de Pago
      </a>
    </aplz-ui-details>

    <aplz-ui-details *renderByMenu="[appRoutes.onboarding]">
      <summary aplzDetailsHeader>
        <div class="flex items-center">
          <aplz-ui-icon name="settings" size="sm"></aplz-ui-icon>
          <span class="ml-7">Merchants</span>
        </div>
      </summary>
      <a
        aplzSidenavLink
        [routerLink]="[appRoutes.onboarding, appRoutes.merchantCreation]">
        Creación de Comercio
      </a>
      <a
        aplzSidenavLink
        [routerLink]="[appRoutes.onboarding, appRoutes.merchantAdmin]">
        Merchant Admin
      </a>
    </aplz-ui-details>

    <aplz-ui-details *renderByMenu="[appRoutes.campaigns]">
      <summary aplzDetailsHeader>
        <div class="flex items-center">
          <aplz-ui-icon name="settings" size="sm"></aplz-ui-icon>
          <span class="ml-7">Campañas</span>
        </div>
      </summary>
      <a aplzSidenavLink [routerLink]="[appRoutes.campaigns]">
        Premios Aplazo
      </a>
    </aplz-ui-details>

    <aplz-ui-details *renderByMenu="[appRoutes.infraSquad]">
      <summary aplzDetailsHeader>
        <div class="flex items-center">
          <aplz-ui-icon name="settings" size="sm"></aplz-ui-icon>
          <span class="ml-7">Infra Squad</span>
        </div>
      </summary>
      <a
        aplzSidenavLink
        [routerLink]="[appRoutes.infraSquad, appRoutes.providerOrder]">
        Orden de Proveedores
      </a>
    </aplz-ui-details>

    <!-- New Tarjetas section -->
    <aplz-ui-details
      *renderByMenu="[
        appRoutes.cards,
        appRoutes.cardsLocations,
        appRoutes.cardsBulkOrders,
      ]">
      <summary aplzDetailsHeader>
        <div class="flex items-center">
          <aplz-ui-icon name="settings" size="sm"></aplz-ui-icon>
          <span class="ml-7">Tarjetas</span>
        </div>
      </summary>
      <a
        aplzSidenavLink
        *renderByMenu="[appRoutes.cardsLocations]"
        [routerLink]="[appRoutes.cards, appRoutes.cardsLocations]">
        Ubicaciones
      </a>
      <a
        aplzSidenavLink
        *renderByMenu="[appRoutes.cardsBulkOrders]"
        [routerLink]="[appRoutes.cards, appRoutes.cardsBulkOrders]">
        Órdenes Masivas
      </a>
    </aplz-ui-details>
  </aplz-ui-dash-sidebar>

  <section class="w-full min-h-screen pb-36 bg-dark-background">
    <router-outlet></router-outlet>
  </section>
</aplz-ui-dashboard>
