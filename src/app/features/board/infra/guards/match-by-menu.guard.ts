import { inject } from '@angular/core';
import { CanMatchFn } from '@angular/router';
import { lastValueFrom, take } from 'rxjs';
import { HelicarrierRoute } from '../../../core/domain/route-names';
import { MenuStore } from '../../../shared/application/services/menu.store';

export const matchByMenuGuard: (route: HelicarrierRoute) => CanMatchFn =
  route => async () => {
    const menuStore = inject(MenuStore);

    const menu = await lastValueFrom(menuStore.menu$.pipe(take(1)));

    const hasValidRoute = menu.find(item => item === route) != null;

    return hasValidRoute;
  };
