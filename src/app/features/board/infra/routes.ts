import { Route } from '@angular/router';
import { ROUTES_CONFIG } from '../../core/domain/route-names';
import { matchByMenuGuard } from './guards/match-by-menu.guard';

export default [
  {
    path: '',
    loadComponent: () =>
      import('./pages/board.component').then(stl => stl.BoardComponent),
    children: [
      {
        path: '',
        canMatch: [matchByMenuGuard(ROUTES_CONFIG.payments)],
        loadChildren: () =>
          import('../../payments/infra/routes/payment.routes'),
      },
      {
        path: '',
        canMatch: [matchByMenuGuard(ROUTES_CONFIG.campaigns)],
        loadChildren: () =>
          import('../../campaigns/infra/routes/campaigns.routes'),
      },
      {
        path: '',
        canMatch: [matchByMenuGuard(ROUTES_CONFIG.onboarding)],
        loadChildren: () => import('../../admin/infra/routes/admin.routes'),
      },
      {
        path: '',
        canMatch: [matchByMenuGuard(ROUTES_CONFIG.infraSquad)],
        loadChildren: () => import('../../infraSquad/routes/infraSquad.routes'),
      },
      {
        path: '',
        canMatch: [matchByMenuGuard(ROUTES_CONFIG.cards)],
        loadChildren: () => import('../../cards/routes/cards.routes'),
      },
    ],
  },
] satisfies Route[];
