import { Route } from '@angular/router';
import { ROUTES_CONFIG } from '../../core/domain/route-names';

export default [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: ROUTES_CONFIG.infraSquad,
  },
  {
    path: ROUTES_CONFIG.infraSquad,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: ROUTES_CONFIG.providerOrder,
      },
      {
        path: ROUTES_CONFIG.providerOrder,
        loadComponent: () =>
          import('../pages/provider-order.component').then(
            stl => stl.ProviderOrderComponent
          ),
        data: {
          title: 'Orden de Proveedores',
        },
      },
    ],
  },
] satisfies Route[];
