import { HttpClient, HttpHeaders } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SHIELD_ENVIRONMENT } from '../../core/infra/config/environments';

interface ProviderData {
  name: string;
  order_execution: number;
}

interface ProviderOrderResponse {
  name: string;
  order_execution: number;
}

@Injectable({
  providedIn: 'root',
})
export class ProviderService {
  readonly #environment = inject(SHIELD_ENVIRONMENT);

  private readonly setPspOrder =
    this.#environment.pcustomCardUrl + '/providers/sort';
  private readonly getPspOrder =
    this.#environment.pcustomCardUrl + '/providers';
  private readonly http = inject(HttpClient);

  sendProviderOrder(data: ProviderData[]): Observable<ProviderOrderResponse[]> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });

    // Filter out ARCUS and STP if user-selected them (avoid duplication)
    let filteredData = data.filter(p => !['ARCUS', 'STP'].includes(p.name));

    // Recalculate order_execution so user selections are ordered first
    filteredData = filteredData.map((item, index) => ({
      ...item,
      order_execution: index + 1, // Start from 1 dynamically
    }));

    // Append ARCUS and STP at the END of the list with the last available order_execution values
    const requiredProviders: ProviderData[] = [
      { name: 'ARCUS', order_execution: filteredData.length + 1 },
    ];

    // Final payload: user-selected providers first, then ARCUS
    const finalPayload = [...filteredData, ...requiredProviders];

    return this.http.post<ProviderOrderResponse[]>(
      this.setPspOrder,
      finalPayload,
      {
        headers,
      }
    );
  }

  getProviderOrder(): Observable<ProviderOrderResponse[]> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });

    return this.http.get<ProviderOrderResponse[]>(this.getPspOrder, {
      headers,
    });
  }
}
