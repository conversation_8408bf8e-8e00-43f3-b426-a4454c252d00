table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

th, td {
  padding: 10px;
  text-align: center;
}

th {
  background-color: #3f51b5;
  color: white;
}

button {
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  background-color: #3f51b5;
  color: white;
  cursor: pointer;
}

button:disabled {
  background-color: #b0bec5;
  cursor: not-allowed;
}

textarea {
  width: 100%;
  margin-top: 10px;
}

h3 {
  margin-top: 20px;
  color: #333;
}

.margin-10 {
  margin: 10px 0;
}

.margin-20 {
  margin: 10px 0;
}

.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.button-container button {
  padding: 10px 20px;
}