@if (currentProviderOrder.length > 0) {
  <div>
    <h3>Current Provider Order</h3>
    <table border="1">
      <thead>
        <tr>
          <th>Provider</th>
          <th>Order Execution</th>
        </tr>
      </thead>
      <tbody>
        @for (order of currentProviderOrder; track order) {
          <tr>
            <td>{{ order.name }}</td>
            <td>{{ order.order_execution }}</td>
          </tr>
        }
      </tbody>
    </table>
  </div>
}

<div class="button-container">
  <button (click)="fetchProviderOrder()">Recargar orden Proveedores</button>
</div>

<h3>Select Providers</h3>
<table border="1">
  <thead>
    <tr>
      <th>Provider</th>
      <th>Action</th>
    </tr>
  </thead>
  <tbody>
    @for (provider of providerNames; track provider; let i = $index) {
      <tr>
        <td>{{ provider }}</td>
        <td>
          <button
            [disabled]="isButtonDisabled[i]"
            (click)="selectProvider(provider, i)">
            {{ isButtonDisabled[i] ? 'Selected' : 'Select' }}
          </button>
        </td>
      </tr>
    }
  </tbody>
</table>

<h3>Request Body:</h3>
<textarea rows="10" cols="50" readonly>{{ requestBodyString }}</textarea>

<div class="button-container">
  <button (click)="onSubmit()">Submit</button>
  <button (click)="onReset()">Reset</button>
</div>
