import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NotifierService } from '@aplazo/merchant/shared';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ProviderName } from '../models/provider.enum';
import { ProviderService } from '../services/provider.service';

interface ProviderRequest {
  name: string;
  order_execution: number;
}

interface ProviderOrderResponse {
  name: string;
  order_execution: number;
}

@Component({
  selector: 'app-provider-order',
  imports: [ReactiveFormsModule],
  templateUrl: './provider-order.component.html',
  styleUrls: ['./provider-order.component.css'],
})
export class ProviderOrderComponent implements OnInit, OnDestroy {
  providerNames = Object.values(ProviderName).filter(
    name => !['ARCUS', 'STP', 'FEMSA'].includes(name)
  );

  requestBody: ProviderRequest[] = [];
  requestBodyString: string = '';
  isButtonDisabled: boolean[] = [];
  orderCounter: number = 1;
  currentProviderOrder: ProviderOrderResponse[] = [];
  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly providerService: ProviderService,
    private readonly notifierService: NotifierService
  ) {
    this.resetFormState();
  }

  ngOnInit() {
    this.fetchProviderOrder();
  }

  fetchProviderOrder() {
    this.providerService
      .getProviderOrder()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: response => {
          this.currentProviderOrder = response;
          console.log('Current Provider Order:', response);
        },
        error: error => {
          console.error('Error fetching provider order:', error);
          this.notifierService.error({
            title: 'Error fetching provider order',
            message: error.message,
          });
        },
      });
  }

  selectProvider(providerName: string, index: number) {
    if (this.requestBody.some(req => req.name === providerName)) return;

    this.requestBody.push({
      name: providerName,
      order_execution: this.orderCounter++,
    });

    this.isButtonDisabled = [...this.isButtonDisabled];
    this.isButtonDisabled[index] = true;
    this.requestBodyString = JSON.stringify(this.requestBody, null, 2);
  }

  onSubmit() {
    console.log('Submitting Request:', this.requestBody);
    this.providerService
      .sendProviderOrder(this.requestBody)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: response => {
          console.log('Response:', response);
          this.notifierService.success({ title: 'Request Accepted' });
        },
        error: error => {
          console.error('Error:', error);
          this.notifierService.error({
            title: 'Error submitting request',
            message: error.message,
          });
        },
      });
  }

  onReset() {
    this.resetFormState();
    this.requestBody.length = 0;
    this.requestBodyString = '';
  }

  resetFormState() {
    this.isButtonDisabled = Array(this.providerNames.length).fill(false);
    this.orderCounter = 1;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
