import { Routes } from '@angular/router';
import { ROUTES_CONFIG } from './features/core/domain/route-names';
import {
  loggedInUsersOnly,
  loggedOffUsersOnly,
} from './features/core/infra/guards/auth.guard';
import { isUnderMaintenanceGuard } from './features/core/infra/guards/maintenance.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: ROUTES_CONFIG.login,
    pathMatch: 'full',
  },
  {
    path: ROUTES_CONFIG.login,
    canActivate: [isUnderMaintenanceGuard, loggedOffUsersOnly],
    loadChildren: () => import('./features/login/infra/routes'),
  },
  {
    path: ROUTES_CONFIG.board,
    canActivate: [isUnderMaintenanceGuard, loggedInUsersOnly],
    loadChildren: () => import('./features/board/infra/routes'),
  },
  {
    path: ROUTES_CONFIG.unavailable,
    canActivate: [isUnderMaintenanceGuard],
    loadComponent: () =>
      import('./features/maintenance/maintenance-message.component').then(
        m => m.MaintenanceMessageComponent
      ),
  },
  {
    path: '**',
    redirectTo: ROUTES_CONFIG.board,
    pathMatch: 'full',
  },
];
