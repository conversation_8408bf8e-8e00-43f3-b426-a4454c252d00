import { AsyncPipe } from '@angular/common';
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { RouterOutlet } from '@angular/router';
import { LoaderService, provideLoader } from '@aplazo/merchant/shared';
import { AplazoPillLoaderComponent } from '@aplazo/shared-ui/loader';
import { AppComponent } from '../app/app.component';

describe('AppComponent', () => {
  let fixture: ComponentFixture<AppComponent>;
  let component: AppComponent;
  let loaderService: LoaderService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        AppComponent,
        RouterOutlet,
        AplazoPillLoaderComponent,
        AsyncPipe,
      ],
      providers: [provideLoader()],
    });

    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    loaderService = TestBed.inject(LoaderService);
  });

  it('should create the app', () => {
    expect(component).toBeTruthy();
  });

  it('should show/hide the loader', fakeAsync(() => {
    const wrapper: HTMLElement = fixture.nativeElement;

    const loaderCmp = wrapper.querySelector('aplz-ui-pill-loader');
    const backdropCmp = loaderCmp?.querySelector(
      '.aplazo-ui-pill-loader__backdrop'
    );

    expect(loaderCmp).toBeTruthy();
    expect(
      backdropCmp?.classList.contains('aplazo-ui-pill-loader__backdrop-hidden')
    ).toBeTrue();

    const idLoader = loaderService.show();
    tick();
    fixture.detectChanges();

    expect(
      backdropCmp?.classList.contains('aplazo-ui-pill-loader__backdrop-hidden')
    ).toBeFalse();

    loaderService.hide(idLoader);

    // The delay below is because the loader service
    // is using some kind of delay to hide the loader
    tick(1000);
    fixture.detectChanges();

    expect(
      backdropCmp?.classList.contains('aplazo-ui-pill-loader__backdrop-hidden')
    ).toBeTrue();
  }));
});
