import { TestBed } from '@angular/core/testing';
import { MenuStore } from '../../../../../app/features/shared/application/services/menu.store';
import { provideMenuStore } from '../../../../../app/features/shared/infra/config/providers';
import { SimpleMenuStore } from '../../../../../app/features/shared/infra/services/simple-menu.store';

describe('Shared Providers', () => {
  it('should provide Menu Store', () => {
    TestBed.configureTestingModule({
      providers: [provideMenuStore()],
    });

    const menuStore = TestBed.inject(MenuStore);

    expect(menuStore).toBeDefined();
    expect(menuStore).toBeInstanceOf(SimpleMenuStore);
  });
});
