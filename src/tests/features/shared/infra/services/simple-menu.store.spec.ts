import { HelicarrierRoute } from '../../../../../app/features/core/domain/route-names';
import { SimpleMenuStore } from '../../../../../app/features/shared/infra/services/simple-menu.store';

describe('SimpleMenuStore', () => {
  let store: SimpleMenuStore;

  beforeEach(() => {
    store = new SimpleMenuStore();
  });

  it('should be created', () => {
    expect(store).toBeTruthy();
    expect(store).toBeInstanceOf(SimpleMenuStore);
  });

  it('should set menu', done => {
    const menu: HelicarrierRoute[] = [
      'merchant_onboarding',
      'merchant_payment',
    ];

    store.setMenu(menu);

    store.menu$.subscribe(value => {
      expect(value).toEqual(menu);
    });

    done();
  });

  it('should throw error when menu is nullish', () => {
    expect(() => {
      // @ts-expect-error: Testing purposes
      store.setMenu(null);
    }).toThrowError();
  });

  it('should throw error when menu is not an array', () => {
    expect(() => {
      // @ts-expect-error: Testing purposes
      store.setMenu('string');
    }).toThrowError();
  });

  it('should get menu synchronously', () => {
    const menu: HelicarrierRoute[] = [
      'merchant_onboarding',
      'merchant_payment',
    ];

    store.setMenu(menu);

    expect(store.getMenu()).toEqual(menu);
  });
});
