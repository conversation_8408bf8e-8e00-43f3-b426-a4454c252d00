import {
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';

export class LocalUsecaseErrorHandler implements UseCaseErrorHandler {
  handle<T>(error: unknown, defaultResponse?: T): T | never {
    if (error instanceof RuntimeMerchantError) {
      console.warn(error.code);
    } else {
      console.error(error);
    }

    if (defaultResponse != null) {
      return defaultResponse;
    }

    throw error;
  }
}
