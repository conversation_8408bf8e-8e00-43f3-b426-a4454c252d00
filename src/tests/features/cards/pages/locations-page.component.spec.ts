import { ComponentFixture, TestBed } from '@angular/core/testing';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { DialogRef, DialogService } from '@ngneat/dialog';
import { of, throwError } from 'rxjs';
import { LocationsPageComponent } from '../../../../app/features/cards/pages/locations-page.component';
import {
  CardsService,
  CardLocation,
  CardLocationsResponse,
} from '../../../../app/features/cards/services/cards.service';
import { LocationFormComponent } from '../../../../app/features/cards/components/location-form.component';

describe('LocationsPageComponent', () => {
  let component: LocationsPageComponent;
  let fixture: ComponentFixture<LocationsPageComponent>;
  let mockCardsService: jasmine.SpyObj<CardsService>;
  let mockDialogService: jasmine.SpyObj<DialogService>;
  let mockDialogRef: jasmine.SpyObj<DialogRef<any, any>>;

  const mockLocation: CardLocation = {
    location_id: 'LOC123',
    name: 'Test Location',
    address1: '123 Main St',
    city: 'New York',
    state: 'NY',
    postalCode: '12345',
  };

  const mockLocationsResponse: CardLocationsResponse = {
    content: [mockLocation],
    pageable: {
      pageNumber: 0,
      pageSize: 12,
      sort: [],
      offset: 0,
      paged: true,
      unpaged: false,
    },
    totalPages: 2,
    totalElements: 15,
    last: false,
    numberOfElements: 12,
    first: true,
    size: 12,
    number: 0,
    sort: [],
    empty: false,
  };

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('DialogRef', ['close']);
    mockCardsService = jasmine.createSpyObj('CardsService', ['getLocations']);
    mockDialogService = jasmine.createSpyObj('DialogService', ['open']);

    // Default mock setup to prevent undefined errors
    mockCardsService.getLocations.and.returnValue(of(mockLocationsResponse));

    await TestBed.configureTestingModule({
      imports: [
        LocationsPageComponent,
        AsyncPipe,
        NgFor,
        NgIf,
        AplazoButtonComponent,
        AplazoCardComponent,
        AplazoSimpleTableComponents,
        AplazoPaginationComponent,
      ],
      providers: [
        { provide: CardsService, useValue: mockCardsService },
        { provide: DialogService, useValue: mockDialogService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(LocationsPageComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize signals with correct default values', () => {
      expect(component.locations()).toEqual([]);
      expect(component.totalPages()).toBe(1);
      expect(component.currentPage()).toBe(0);
    });

    it('should call fetchLocations on ngOnInit', () => {
      spyOn(component, 'fetchLocations');

      component.ngOnInit();

      expect(component.fetchLocations).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should fetch locations successfully with default pagination', () => {
      mockCardsService.getLocations.and.returnValue(of(mockLocationsResponse));

      component.fetchLocations();

      expect(mockCardsService.getLocations).toHaveBeenCalledWith(0, 12);
      expect(component.locations()).toEqual([mockLocation]);
      expect(component.totalPages()).toBe(2);
    });

    it('should handle fetch locations error', () => {
      const error = new Error('Service error');
      mockCardsService.getLocations.and.returnValue(throwError(() => error));
      spyOn(console, 'error');

      component.fetchLocations();

      expect(mockCardsService.getLocations).toHaveBeenCalledWith(0, 12);
      expect(console.error).toHaveBeenCalledWith(
        'Failed to fetch card locations:',
        error
      );
      expect(component.locations()).toEqual([]);
    });

    it('should set loading state during fetch', () => {
      mockCardsService.getLocations.and.returnValue(of(mockLocationsResponse));

      component.fetchLocations();

      expect(mockCardsService.getLocations).toHaveBeenCalledWith(0, 12);
    });
  });

  describe('Pagination', () => {
    it('should change page and fetch locations with correct parameters', () => {
      mockCardsService.getLocations.and.returnValue(of(mockLocationsResponse));
      spyOn(component, 'fetchLocations').and.callThrough();

      // Initialize the component first
      component.ngOnInit();

      component.changePage(2);

      expect(component.fetchLocations).toHaveBeenCalled();
      expect(mockCardsService.getLocations).toHaveBeenCalledWith(2, 12); // page index 2
    });

    it('should update current page calculation when page changes', () => {
      mockCardsService.getLocations.and.returnValue(of(mockLocationsResponse));

      // Initialize the component first
      component.ngOnInit();

      component.changePage(3);

      expect(component.currentPage()).toBe(3);
      expect(mockCardsService.getLocations).toHaveBeenCalledWith(3, 12); // page index 3
    });
  });

  describe('Dialog Integration', () => {
    it('should open location form dialog when createLocation is called', () => {
      Object.defineProperty(mockDialogRef, 'afterClosed$', {
        value: of({ hasConfirmation: true }),
        writable: true,
      });
      mockDialogService.open.and.returnValue(mockDialogRef);

      component.createLocation();

      expect(mockDialogService.open).toHaveBeenCalledWith(
        LocationFormComponent,
        { enableClose: false }
      );
    });

    it('should refresh locations when dialog closes with confirmation', () => {
      Object.defineProperty(mockDialogRef, 'afterClosed$', {
        value: of({ hasConfirmation: true }),
        writable: true,
      });
      mockDialogService.open.and.returnValue(mockDialogRef);
      mockCardsService.getLocations.and.returnValue(of(mockLocationsResponse));
      spyOn(component, 'fetchLocations').and.callThrough();

      component.createLocation();

      expect(component.fetchLocations).toHaveBeenCalled();
      expect(mockCardsService.getLocations).toHaveBeenCalledWith(0, 12);
    });

    it('should not refresh locations when dialog closes without confirmation', () => {
      Object.defineProperty(mockDialogRef, 'afterClosed$', {
        value: of({ hasConfirmation: false }),
        writable: true,
      });
      mockDialogService.open.and.returnValue(mockDialogRef);
      const fetchLocationsSpy = spyOn(component, 'fetchLocations');

      component.createLocation();

      expect(fetchLocationsSpy).not.toHaveBeenCalled();
    });

    it('should handle dialog close with no result', () => {
      Object.defineProperty(mockDialogRef, 'afterClosed$', {
        value: of(null),
        writable: true,
      });
      mockDialogService.open.and.returnValue(mockDialogRef);
      const fetchLocationsSpy = spyOn(component, 'fetchLocations');

      component.createLocation();

      expect(fetchLocationsSpy).not.toHaveBeenCalled();
    });
  });

  describe('Computed Signals', () => {
    beforeEach(() => {
      mockCardsService.getLocations.and.returnValue(of(mockLocationsResponse));
      fixture.detectChanges();
    });

    it('should compute locations from response', () => {
      component.fetchLocations();

      expect(component.locations()).toEqual([mockLocation]);
    });

    it('should compute totalPages from response', () => {
      component.fetchLocations();

      expect(component.totalPages()).toBe(2);
    });

    it('should return empty array when no response', () => {
      const emptyResponse = { ...mockLocationsResponse, content: [] };
      mockCardsService.getLocations.and.returnValue(of(emptyResponse));

      component.fetchLocations();

      expect(component.locations()).toEqual([]);
    });
  });

  describe('User Interactions', () => {
    beforeEach(() => {
      mockCardsService.getLocations.and.returnValue(of(mockLocationsResponse));
      fixture.detectChanges();
    });

    it('should call createLocation when register button is clicked', () => {
      spyOn(component, 'createLocation');
      const compiled = fixture.debugElement.nativeElement;
      const button = compiled.querySelector('button');

      button.click();

      expect(component.createLocation).toHaveBeenCalled();
    });

    it('should call changePage when pagination emits selectedPage', () => {
      spyOn(component, 'changePage');
      component.fetchLocations();
      fixture.detectChanges();

      const compiled = fixture.debugElement.nativeElement;
      const pagination = compiled.querySelector('aplz-ui-pagination');

      if (pagination) {
        // Simulate pagination event
        component.changePage(2);
        expect(component.changePage).toHaveBeenCalledWith(2);
      }
    });
  });

  describe('Template Rendering', () => {
    it('should display correct title', () => {
      const compiled = fixture.debugElement.nativeElement;
      const title = compiled.querySelector('h2');

      expect(title.textContent.trim()).toBe('Ubicaciones de Tarjetas');
    });

    it('should display register button', () => {
      const compiled = fixture.debugElement.nativeElement;
      const button = compiled.querySelector('button');

      expect(button.textContent.trim()).toContain('Registrar ubicación');
    });

    it('should display table headers', () => {
      const compiled = fixture.debugElement.nativeElement;
      const headers = compiled.querySelectorAll('th');

      expect(headers.length).toBe(6);
      expect(headers[0].textContent.trim()).toBe('ID');
      expect(headers[1].textContent.trim()).toBe('Nombre');
      expect(headers[2].textContent.trim()).toBe('Dirección');
      expect(headers[3].textContent.trim()).toBe('Ciudad');
      expect(headers[4].textContent.trim()).toBe('Estado');
      expect(headers[5].textContent.trim()).toBe('Código Postal');
    });

    it('should display no data message when locations array is empty', () => {
      mockCardsService.getLocations.and.returnValue(
        of({ ...mockLocationsResponse, content: [] })
      );
      component.fetchLocations();
      fixture.detectChanges();

      const compiled = fixture.debugElement.nativeElement;
      const noDataCell = compiled.querySelector('td[colspan="6"]');

      expect(noDataCell.textContent.trim()).toBe(
        'No hay ubicaciones registradas.'
      );
    });

    it('should display location data when available', () => {
      mockCardsService.getLocations.and.returnValue(of(mockLocationsResponse));
      component.fetchLocations();
      fixture.detectChanges();

      const compiled = fixture.debugElement.nativeElement;
      const dataCells = compiled.querySelectorAll('tbody td');

      expect(dataCells.length).toBeGreaterThan(0);
      expect(dataCells[0].textContent.trim()).toBe('LOC123');
      expect(dataCells[1].textContent.trim()).toBe('Test Location');
    });

    it('should show pagination when totalPages > 1', () => {
      mockCardsService.getLocations.and.returnValue(of(mockLocationsResponse));
      component.fetchLocations();
      fixture.detectChanges();

      const compiled = fixture.debugElement.nativeElement;
      const pagination = compiled.querySelector('aplz-ui-pagination');

      expect(pagination).toBeTruthy();
    });

    it('should hide pagination when totalPages <= 1', () => {
      const singlePageResponse = { ...mockLocationsResponse, totalPages: 1 };
      mockCardsService.getLocations.and.returnValue(of(singlePageResponse));
      component.fetchLocations();
      fixture.detectChanges();

      const compiled = fixture.debugElement.nativeElement;
      const pagination = compiled.querySelector('aplz-ui-pagination');

      expect(pagination).toBeFalsy();
    });
  });

  describe('Change Detection', () => {
    it('should use OnPush change detection strategy', () => {
      expect(component.constructor.prototype.ngOnDestroy).toBeUndefined();
      // OnPush components rely on signals and immutable data
      // This is more of a structural test to ensure the strategy is set
    });
  });
});
