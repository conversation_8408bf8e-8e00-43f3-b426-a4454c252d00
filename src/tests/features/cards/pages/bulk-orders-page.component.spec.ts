import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { DialogRef, DialogService } from '@ngneat/dialog';
import { of } from 'rxjs';
import { BulkOrdersPageComponent } from '../../../../app/features/cards/pages/bulk-orders-page.component';
import { BulkOrderFormComponent } from '../../../../app/features/cards/components/bulk-order-form.component';

describe('BulkOrdersPageComponent', () => {
  let component: BulkOrdersPageComponent;
  let fixture: ComponentFixture<BulkOrdersPageComponent>;
  let mockDialogService: jasmine.SpyObj<DialogService>;
  let mockDialogRef: jasmine.SpyObj<DialogRef<any, any>>;

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('DialogRef', ['close']);
    mockDialogService = jasmine.createSpyObj('DialogService', ['open']);

    await TestBed.configureTestingModule({
      imports: [BulkOrdersPageComponent, AplazoButtonComponent],
      providers: [{ provide: DialogService, useValue: mockDialogService }],
    }).compileComponents();

    fixture = TestBed.createComponent(BulkOrdersPageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Template', () => {
    it('should display the correct title', () => {
      const compiled = fixture.debugElement.nativeElement;
      const title = compiled.querySelector('h1');

      expect(title.textContent.trim()).toBe('Órdenes Masivas de Tarjetas');
    });

    it('should display the create order button', () => {
      const compiled = fixture.debugElement.nativeElement;
      const button = compiled.querySelector('button');

      expect(button).toBeTruthy();
      expect(button.textContent.trim()).toContain('Crear');
      expect(button.textContent.trim()).toContain('orden');
    });

    it('should display informational text', () => {
      const compiled = fixture.debugElement.nativeElement;
      const infoText = compiled.querySelector('.bg-white p');

      expect(infoText.textContent.trim()).toContain(
        'Desde aquí puedes crear órdenes masivas de tarjetas físicas'
      );
    });
  });

  describe('Dialog Integration', () => {
    it('should open bulk order form dialog when createBulkOrder is called', () => {
      Object.defineProperty(mockDialogRef, 'afterClosed$', {
        value: of({ hasConfirmation: true }),
        writable: true,
      });
      mockDialogService.open.and.returnValue(mockDialogRef);

      component.createBulkOrder();

      expect(mockDialogService.open).toHaveBeenCalledWith(
        BulkOrderFormComponent,
        { enableClose: false }
      );
    });

    it('should handle dialog close with confirmation', () => {
      const consoleSpy = spyOn(console, 'log');
      Object.defineProperty(mockDialogRef, 'afterClosed$', {
        value: of({ hasConfirmation: true }),
        writable: true,
      });
      mockDialogService.open.and.returnValue(mockDialogRef);

      component.createBulkOrder();

      expect(consoleSpy).toHaveBeenCalledWith(
        'Bulk order created successfully'
      );
    });

    it('should handle dialog close without confirmation', () => {
      const consoleSpy = spyOn(console, 'log');
      Object.defineProperty(mockDialogRef, 'afterClosed$', {
        value: of({ hasConfirmation: false }),
        writable: true,
      });
      mockDialogService.open.and.returnValue(mockDialogRef);

      component.createBulkOrder();

      expect(consoleSpy).not.toHaveBeenCalled();
    });

    it('should handle dialog close with no result', () => {
      const consoleSpy = spyOn(console, 'log');
      Object.defineProperty(mockDialogRef, 'afterClosed$', {
        value: of(null),
        writable: true,
      });
      mockDialogService.open.and.returnValue(mockDialogRef);

      component.createBulkOrder();

      expect(consoleSpy).not.toHaveBeenCalled();
    });
  });

  describe('User Interactions', () => {
    it('should call createBulkOrder when button is clicked', () => {
      spyOn(component, 'createBulkOrder');
      const compiled = fixture.debugElement.nativeElement;
      const button = compiled.querySelector('button');

      button.click();

      expect(component.createBulkOrder).toHaveBeenCalled();
    });
  });

  describe('Change Detection', () => {
    it('should use OnPush change detection strategy', () => {
      expect(component.constructor.prototype.ngOnDestroy).toBeUndefined();
      // OnPush components don't need manual change detection tracking
      // This is more of a structural test to ensure the strategy is set
    });
  });
});
