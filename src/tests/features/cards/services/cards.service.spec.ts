import { TestBed } from '@angular/core/testing';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import {
  CardsService,
  CardLocation,
  CardLocationsResponse,
  CreateLocationRequest,
  BulkOrderRequest,
} from '../../../../app/features/cards/services/cards.service';
import { SHIELD_ENVIRONMENT } from '../../../../app/features/core/infra/config/environments';
import {
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';

describe('CardsService', () => {
  let service: CardsService;
  let httpMock: HttpTestingController;
  const mockEnvironment = {
    gatewayUrl: 'https://api.example.com',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        CardsService,
        { provide: SHIELD_ENVIRONMENT, useValue: mockEnvironment },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    service = TestBed.inject(CardsService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getLocations', () => {
    it('should fetch card locations with default pagination', () => {
      const mockResponse: CardLocationsResponse = {
        content: [
          {
            location_id: 'LOC123',
            name: 'Test Location',
            address1: '123 Main St',
            city: 'New York',
            state: 'NY',
            postalCode: '12345',
          },
        ],
        pageable: {
          pageNumber: 0,
          pageSize: 12,
          sort: [],
          offset: 0,
          paged: true,
          unpaged: false,
        },
        totalPages: 1,
        totalElements: 1,
        last: true,
        numberOfElements: 1,
        first: true,
        size: 12,
        number: 0,
        sort: [],
        empty: false,
      };

      service.getLocations().subscribe(response => {
        expect(response).toEqual(mockResponse);
        expect(response.content.length).toBe(1);
        expect(response.content[0].location_id).toBe('LOC123');
      });

      const req = httpMock.expectOne(
        req =>
          req.url ===
            `${mockEnvironment.gatewayUrl}/physical-card/v1/locations` &&
          req.params.get('page') === '0' &&
          req.params.get('size') === '12'
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should fetch card locations with custom pagination parameters', () => {
      const mockResponse: CardLocationsResponse = {
        content: [],
        pageable: {
          pageNumber: 2,
          pageSize: 20,
          sort: [],
          offset: 40,
          paged: true,
          unpaged: false,
        },
        totalPages: 3,
        totalElements: 50,
        last: false,
        numberOfElements: 10,
        first: false,
        size: 20,
        number: 2,
        sort: [],
        empty: false,
      };

      service.getLocations(2, 20).subscribe(response => {
        expect(response).toEqual(mockResponse);
        expect(response.pageable.pageNumber).toBe(2);
        expect(response.pageable.pageSize).toBe(20);
      });

      const req = httpMock.expectOne(
        req =>
          req.url ===
            `${mockEnvironment.gatewayUrl}/physical-card/v1/locations` &&
          req.params.get('page') === '2' &&
          req.params.get('size') === '20'
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle empty locations response', () => {
      const emptyResponse: CardLocationsResponse = {
        content: [],
        pageable: {
          pageNumber: 0,
          pageSize: 12,
          sort: [],
          offset: 0,
          paged: true,
          unpaged: false,
        },
        totalPages: 0,
        totalElements: 0,
        last: true,
        numberOfElements: 0,
        first: true,
        size: 12,
        number: 0,
        sort: [],
        empty: true,
      };

      service.getLocations().subscribe(response => {
        expect(response.content).toEqual([]);
        expect(response.empty).toBeTruthy();
      });

      const req = httpMock.expectOne(
        req =>
          req.url ===
            `${mockEnvironment.gatewayUrl}/physical-card/v1/locations` &&
          req.params.get('page') === '0' &&
          req.params.get('size') === '12'
      );
      req.flush(emptyResponse);
    });

    it('should handle HTTP error for getLocations', () => {
      const errorMessage = 'Network error';

      service.getLocations().subscribe({
        next: () => fail('Should have failed'),
        error: error => {
          expect(error.status).toBe(500);
          expect(error.statusText).toBe('Server Error');
        },
      });

      const req = httpMock.expectOne(
        req =>
          req.url ===
            `${mockEnvironment.gatewayUrl}/physical-card/v1/locations` &&
          req.params.get('page') === '0' &&
          req.params.get('size') === '12'
      );
      req.flush(errorMessage, { status: 500, statusText: 'Server Error' });
    });
  });

  describe('createLocation', () => {
    it('should create a new location', () => {
      const request: CreateLocationRequest = {
        name: 'Test Location',
        address: '123 Main St',
        city: 'New York',
        state: 'NY',
        postalCode: '12345',
        parentLocation: '0',
        parentLocationType: 0,
      };

      const mockResponse: CardLocation = {
        location_id: 'LOC123',
        name: 'Test Location',
        address1: '123 Main St',
        city: 'New York',
        state: 'NY',
        postalCode: '12345',
      };

      service.createLocation(request).subscribe(response => {
        expect(response).toEqual(mockResponse);
        expect(response.location_id).toBe('LOC123');
        expect(response.name).toBe('Test Location');
      });

      const req = httpMock.expectOne(
        `${mockEnvironment.gatewayUrl}/physical-card/v1/locations`
      );
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(request);
      req.flush(mockResponse);
    });

    it('should create a location with optional fields', () => {
      const request: CreateLocationRequest = {
        name: 'Test Location',
        address: '123 Main St',
        city: 'New York',
        state: 'NY',
        postalCode: '12345',
        parentLocation: 'PARENT123',
        parentLocationType: 1,
      };

      const mockResponse: CardLocation = {
        location_id: 'LOC124',
        name: 'Test Location',
        address1: '123 Main St',
        city: 'New York',
        state: 'NY',
        postalCode: '12345',
      };

      service.createLocation(request).subscribe(response => {
        expect(response).toEqual(mockResponse);
        expect(response.location_id).toBe('LOC124');
      });

      const req = httpMock.expectOne(
        `${mockEnvironment.gatewayUrl}/physical-card/v1/locations`
      );
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(request);
      req.flush(mockResponse);
    });

    it('should handle HTTP error for createLocation', () => {
      const request: CreateLocationRequest = {
        name: 'Test Location',
        address: '123 Main St',
        city: 'New York',
        state: 'NY',
        postalCode: '12345',
        parentLocation: '0',
        parentLocationType: 0,
      };

      const errorMessage = 'Validation error';

      service.createLocation(request).subscribe({
        next: () => fail('Should have failed'),
        error: error => {
          expect(error.status).toBe(400);
          expect(error.statusText).toBe('Bad Request');
        },
      });

      const req = httpMock.expectOne(
        `${mockEnvironment.gatewayUrl}/physical-card/v1/locations`
      );
      req.flush(errorMessage, { status: 400, statusText: 'Bad Request' });
    });
  });

  describe('createBulkOrder', () => {
    it('should create a bulk order', () => {
      const request: BulkOrderRequest = {
        numberOfCards: 100,
        shipToName: 'John Doe',
        shipToAddress: '123 Main St',
        shipToCity: 'New York',
        shipToStateOrProvince: 'NY',
        shipToPostalCode: '12345',
        locationId: 'LOC123',
      };

      const mockResponse = {
        orderId: 'ORDER123',
        status: 'pending',
        message: 'Bulk order created successfully',
      };

      service.createBulkOrder(request).subscribe(response => {
        expect(response).toEqual(mockResponse);
        expect(response.orderId).toBe('ORDER123');
        expect(response.status).toBe('pending');
      });

      const req = httpMock.expectOne(
        `${mockEnvironment.gatewayUrl}/physical-card/v1/bulk-orders`
      );
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(request);
      req.flush(mockResponse);
    });

    it('should handle minimum required fields for bulk order', () => {
      const request: BulkOrderRequest = {
        numberOfCards: 1,
        shipToName: 'Jane Doe',
        shipToAddress: '456 Oak St',
        shipToCity: 'Los Angeles',
        shipToStateOrProvince: 'CA',
        shipToPostalCode: '90210',
        locationId: 'LOC456',
      };

      const mockResponse = { orderId: 'ORDER456' };

      service.createBulkOrder(request).subscribe(response => {
        expect(response.orderId).toBe('ORDER456');
      });

      const req = httpMock.expectOne(
        `${mockEnvironment.gatewayUrl}/physical-card/v1/bulk-orders`
      );
      expect(req.request.body.numberOfCards).toBe(1);
      expect(req.request.body.locationId).toBe('LOC456');
      req.flush(mockResponse);
    });

    it('should handle Canadian postal code format', () => {
      const request: BulkOrderRequest = {
        numberOfCards: 50,
        shipToName: 'Canadian User',
        shipToAddress: '789 Maple Ave',
        shipToCity: 'Toronto',
        shipToStateOrProvince: 'ON',
        shipToPostalCode: 'K1A 0B1',
        locationId: 'LOC789',
      };

      const mockResponse = { orderId: 'ORDER789' };

      service.createBulkOrder(request).subscribe(response => {
        expect(response.orderId).toBe('ORDER789');
      });

      const req = httpMock.expectOne(
        `${mockEnvironment.gatewayUrl}/physical-card/v1/bulk-orders`
      );
      expect(req.request.body.shipToPostalCode).toBe('K1A 0B1');
      req.flush(mockResponse);
    });

    it('should handle HTTP error for createBulkOrder', () => {
      const request: BulkOrderRequest = {
        numberOfCards: 100,
        shipToName: 'John Doe',
        shipToAddress: '123 Main St',
        shipToCity: 'New York',
        shipToStateOrProvince: 'NY',
        shipToPostalCode: '12345',
        locationId: 'INVALID_LOC',
      };

      const errorMessage = 'Invalid location ID';

      service.createBulkOrder(request).subscribe({
        next: () => fail('Should have failed'),
        error: error => {
          expect(error.status).toBe(422);
          expect(error.statusText).toBe('Unprocessable Entity');
        },
      });

      const req = httpMock.expectOne(
        `${mockEnvironment.gatewayUrl}/physical-card/v1/bulk-orders`
      );
      req.flush(errorMessage, {
        status: 422,
        statusText: 'Unprocessable Entity',
      });
    });
  });

  describe('Service Configuration', () => {
    it('should use correct base URL from environment', () => {
      service.getLocations().subscribe();

      const req = httpMock.expectOne(
        req =>
          req.url ===
            `${mockEnvironment.gatewayUrl}/physical-card/v1/locations` &&
          req.params.get('page') === '0' &&
          req.params.get('size') === '12'
      );
      expect(req.request.url).toBe(
        `${mockEnvironment.gatewayUrl}/physical-card/v1/locations`
      );
      req.flush({ content: [] });
    });

    it('should be provided in root', () => {
      const injectedService = TestBed.inject(CardsService);
      expect(injectedService).toBe(service);
    });
  });

  describe('Interface Contracts', () => {
    it('should match CardLocation interface', () => {
      const location: CardLocation = {
        location_id: 'test',
        name: 'test',
        address1: 'test',
        city: 'test',
        state: 'test',
        postalCode: 'test',
      };

      expect(location.location_id).toBeDefined();
    });

    it('should match CreateLocationRequest interface', () => {
      const request: CreateLocationRequest = {
        name: 'test',
        address: 'test',
        city: 'test',
        state: 'test',
        postalCode: 'test',
        parentLocation: 'test',
        parentLocationType: 1,
      };

      expect(request.name).toBeDefined();
      expect(request.address).toBeDefined();
      expect(request.city).toBeDefined();
      expect(request.state).toBeDefined();
      expect(request.postalCode).toBeDefined();
    });

    it('should match BulkOrderRequest interface', () => {
      const request: BulkOrderRequest = {
        numberOfCards: 1,
        shipToName: 'test',
        shipToAddress: 'test',
        shipToCity: 'test',
        shipToStateOrProvince: 'test',
        shipToPostalCode: 'test',
        locationId: 'test',
      };

      expect(request.numberOfCards).toBeDefined();
      expect(request.shipToName).toBeDefined();
      expect(request.locationId).toBeDefined();
    });
  });
});
