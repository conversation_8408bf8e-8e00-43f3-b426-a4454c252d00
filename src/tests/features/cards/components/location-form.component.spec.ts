import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NotifierService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import { of, throwError } from 'rxjs';
import { LocationFormComponent } from '../../../../app/features/cards/components/location-form.component';
import {
  CardsService,
  CreateLocationRequest,
} from '../../../../app/features/cards/services/cards.service';

describe('LocationFormComponent', () => {
  let component: LocationFormComponent;
  let fixture: ComponentFixture<LocationFormComponent>;
  let mockDialogRef: jasmine.SpyObj<DialogRef<any, any>>;
  let mockNotifier: jasmine.SpyObj<NotifierService>;
  let mockCardsService: jasmine.SpyObj<CardsService>;

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('DialogRef', ['close']);
    mockNotifier = jasmine.createSpyObj('NotifierService', [
      'warning',
      'success',
      'error',
    ]);
    mockCardsService = jasmine.createSpyObj('CardsService', ['createLocation']);

    await TestBed.configureTestingModule({
      imports: [
        LocationFormComponent,
        ReactiveFormsModule,
        AplazoFormFieldDirectives,
        AplazoButtonComponent,
        AplazoCardComponent,
      ],
      providers: [
        { provide: DialogRef, useValue: mockDialogRef },
        { provide: NotifierService, useValue: mockNotifier },
        { provide: CardsService, useValue: mockCardsService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(LocationFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Form Initialization', () => {
    it('should initialize form with empty values', () => {
      expect(component.nameControl.value).toBe('');
      expect(component.addressControl.value).toBe('');
      expect(component.cityControl.value).toBe('');
      expect(component.stateControl.value).toBe('');
      expect(component.postalCodeControl.value).toBe('');
    });

    it('should mark form as invalid initially', () => {
      expect(component.form.valid).toBeFalsy();
    });

    it('should load Mexican states catalog', () => {
      expect(component.mexicanStates).toBeDefined();
      expect(component.mexicanStates.length).toBe(32); // 31 states + Mexico City
      expect(component.mexicanStates).toContain(
        jasmine.objectContaining({ code: 'DF', name: 'Ciudad de México' })
      );
      expect(component.mexicanStates).toContain(
        jasmine.objectContaining({ code: 'JC', name: 'Jalisco' })
      );
    });
  });

  describe('Form Validation', () => {
    describe('name field', () => {
      it('should be required', () => {
        component.nameControl.setValue('');
        component.nameControl.markAsTouched();

        expect(component.nameControl.hasError('required')).toBeTruthy();
      });

      it('should have maximum length of 85', () => {
        component.nameControl.setValue('a'.repeat(86));
        component.nameControl.markAsTouched();

        expect(component.nameControl.hasError('maxlength')).toBeTruthy();
      });

      it('should validate pattern for allowed characters', () => {
        component.nameControl.setValue("Test Location 123_.-'");
        component.nameControl.markAsTouched();

        expect(component.nameControl.valid).toBeTruthy();
      });

      it('should reject invalid characters', () => {
        component.nameControl.setValue('Test@Location#');
        component.nameControl.markAsTouched();

        expect(component.nameControl.hasError('pattern')).toBeTruthy();
      });
    });

    describe('address field', () => {
      it('should be required', () => {
        component.addressControl.setValue('');
        component.addressControl.markAsTouched();

        expect(component.addressControl.hasError('required')).toBeTruthy();
      });

      it('should have minimum length of 4', () => {
        component.addressControl.setValue('123');
        component.addressControl.markAsTouched();

        expect(component.addressControl.hasError('minlength')).toBeTruthy();
      });

      it('should have maximum length of 40', () => {
        component.addressControl.setValue('a'.repeat(41));
        component.addressControl.markAsTouched();

        expect(component.addressControl.hasError('maxlength')).toBeTruthy();
      });

      it('should validate pattern for alphanumeric and spaces', () => {
        component.addressControl.setValue('1234 Main Street');
        component.addressControl.markAsTouched();

        expect(component.addressControl.valid).toBeTruthy();
      });

      it('should reject invalid characters', () => {
        component.addressControl.setValue('123@ Main#');
        component.addressControl.markAsTouched();

        expect(component.addressControl.hasError('pattern')).toBeTruthy();
      });
    });

    describe('city field', () => {
      it('should be required', () => {
        component.cityControl.setValue('');
        component.cityControl.markAsTouched();

        expect(component.cityControl.hasError('required')).toBeTruthy();
      });

      it('should have maximum length of 30', () => {
        component.cityControl.setValue('a'.repeat(31));
        component.cityControl.markAsTouched();

        expect(component.cityControl.hasError('maxlength')).toBeTruthy();
      });

      it('should validate pattern for letters, spaces, dots and hyphens', () => {
        component.cityControl.setValue('New York-City.');
        component.cityControl.markAsTouched();

        expect(component.cityControl.valid).toBeTruthy();
      });

      it('should reject invalid characters', () => {
        component.cityControl.setValue('New York123');
        component.cityControl.markAsTouched();

        expect(component.cityControl.hasError('pattern')).toBeTruthy();
      });
    });

    describe('state field', () => {
      it('should be required', () => {
        component.stateControl.setValue('');
        component.stateControl.markAsTouched();

        expect(component.stateControl.hasError('required')).toBeTruthy();
      });

      it('should accept valid Mexican state codes', () => {
        component.stateControl.setValue('JC');
        component.stateControl.markAsTouched();

        expect(component.stateControl.valid).toBeTruthy();
      });

      it('should accept Mexico City code', () => {
        component.stateControl.setValue('DF');
        component.stateControl.markAsTouched();

        expect(component.stateControl.valid).toBeTruthy();
      });

      it('should accept other valid state codes', () => {
        const validCodes = ['AG', 'BC', 'BS', 'NL', 'OA'];
        validCodes.forEach(code => {
          component.stateControl.setValue(code);
          component.stateControl.markAsTouched();

          expect(component.stateControl.valid).toBeTruthy();
        });
      });
    });

    describe('postalCode field', () => {
      it('should be required', () => {
        component.postalCodeControl.setValue('');
        component.postalCodeControl.markAsTouched();

        expect(component.postalCodeControl.hasError('required')).toBeTruthy();
      });

      it('should have maximum length of 10', () => {
        component.postalCodeControl.setValue('12345678901');
        component.postalCodeControl.markAsTouched();

        expect(component.postalCodeControl.hasError('maxlength')).toBeTruthy();
      });

      it('should validate US postal code format', () => {
        component.postalCodeControl.setValue('12345');
        component.postalCodeControl.markAsTouched();

        expect(component.postalCodeControl.valid).toBeTruthy();
      });

      it('should validate extended US postal code format', () => {
        component.postalCodeControl.setValue('12345-6789');
        component.postalCodeControl.markAsTouched();

        expect(component.postalCodeControl.valid).toBeTruthy();
      });

      it('should validate Canadian postal code format', () => {
        component.postalCodeControl.setValue('K1A 0B1');
        component.postalCodeControl.markAsTouched();

        expect(component.postalCodeControl.valid).toBeTruthy();
      });

      it('should reject invalid postal code format', () => {
        component.postalCodeControl.setValue('invalid');
        component.postalCodeControl.markAsTouched();

        expect(component.postalCodeControl.hasError('pattern')).toBeTruthy();
      });
    });
  });

  describe('User Interactions', () => {
    it('should close dialog with confirmation false when cancel is clicked', () => {
      component.close(false);

      expect(mockDialogRef.close).toHaveBeenCalledWith({
        hasConfirmation: false,
      });
    });

    it('should close dialog with confirmation true when successful', () => {
      component.close(true);

      expect(mockDialogRef.close).toHaveBeenCalledWith({
        hasConfirmation: true,
      });
    });
  });

  describe('Form Submission', () => {
    beforeEach(() => {
      // Set up valid form data
      component.nameControl.setValue('Test Location');
      component.addressControl.setValue('1234 Main St');
      component.cityControl.setValue('Guadalajara');
      component.stateControl.setValue('JC');
      component.postalCodeControl.setValue('44100');
    });

    it('should show warning when form is invalid', () => {
      component.nameControl.setValue('');

      component.finish();

      expect(mockNotifier.warning).toHaveBeenCalledWith({
        title:
          'Por favor, completa los campos requeridos o corrije los errores indicados.',
      });
      expect(mockCardsService.createLocation).not.toHaveBeenCalled();
    });

    it('should call service with correct data and always send parentLocation as 0 and parentLocationType as 0', () => {
      const expectedRequest: CreateLocationRequest = {
        name: 'Test Location',
        address: '1234 Main St',
        city: 'Guadalajara',
        state: 'JC',
        postalCode: '44100',
        parentLocation: '0',
        parentLocationType: 0,
      };

      mockCardsService.createLocation.and.returnValue(of({} as any));

      component.finish();

      expect(mockCardsService.createLocation).toHaveBeenCalledWith(
        expectedRequest
      );
    });

    it('should show success notification and close dialog on successful creation', () => {
      mockCardsService.createLocation.and.returnValue(of({} as any));

      component.finish();

      expect(mockNotifier.success).toHaveBeenCalledWith({
        title: 'Ubicación creada exitosamente',
      });
      expect(mockDialogRef.close).toHaveBeenCalledWith({
        hasConfirmation: true,
      });
    });

    it('should show error notification on creation failure', () => {
      const error = new Error('Service error');
      mockCardsService.createLocation.and.returnValue(throwError(() => error));

      component.finish();

      expect(mockNotifier.error).toHaveBeenCalledWith({
        title: 'Error al crear la ubicación',
        message: 'Por favor intenta nuevamente',
      });
      expect(mockDialogRef.close).not.toHaveBeenCalled();
    });

    it('should mark all fields as touched on submission', () => {
      mockCardsService.createLocation.and.returnValue(of({} as any));

      component.finish();

      expect(component.nameControl.touched).toBeTruthy();
      expect(component.addressControl.touched).toBeTruthy();
      expect(component.cityControl.touched).toBeTruthy();
      expect(component.stateControl.touched).toBeTruthy();
      expect(component.postalCodeControl.touched).toBeTruthy();
    });

    it('should call service with Mexico City state code', () => {
      // Ensure all required fields are set
      component.nameControl.setValue('Test Location');
      component.addressControl.setValue('1234 Main St');
      component.cityControl.setValue('Ciudad de Mexico');
      component.stateControl.setValue('DF');
      component.postalCodeControl.setValue('44100');

      const expectedRequest: CreateLocationRequest = {
        name: 'Test Location',
        address: '1234 Main St',
        city: 'Ciudad de Mexico',
        state: 'DF',
        postalCode: '44100',
        parentLocation: '0',
        parentLocationType: 0,
      };

      mockCardsService.createLocation.and.returnValue(of({} as any));

      component.finish();

      expect(mockCardsService.createLocation).toHaveBeenCalledWith(
        expectedRequest
      );
    });
  });

  describe('Template', () => {
    it('should display form fields', () => {
      const compiled = fixture.debugElement.nativeElement;

      expect(
        compiled.querySelector('input[formControlName="name"]')
      ).toBeTruthy();
      expect(
        compiled.querySelector('input[formControlName="address"]')
      ).toBeTruthy();
      expect(
        compiled.querySelector('input[formControlName="city"]')
      ).toBeTruthy();
      expect(
        compiled.querySelector('select[formControlName="state"]')
      ).toBeTruthy();
      expect(
        compiled.querySelector('input[formControlName="postalCode"]')
      ).toBeTruthy();
    });

    it('should display cancel and submit buttons', () => {
      const compiled = fixture.debugElement.nativeElement;
      const buttons = compiled.querySelectorAll('button');

      expect(buttons.length).toBe(2);
      expect(buttons[0].textContent.trim()).toContain('Cancelar');
      expect(buttons[1].textContent.trim()).toContain('Crear');
    });

    it('should disable submit button when form is disabled', () => {
      component.form.disable();
      fixture.detectChanges();

      const compiled = fixture.debugElement.nativeElement;
      const submitButton = compiled.querySelector('button[type="submit"]');

      expect(submitButton.disabled).toBeTruthy();
    });

    it('should render state select with Mexican states options', () => {
      const compiled = fixture.debugElement.nativeElement;
      const stateSelect = compiled.querySelector(
        'select[formControlName="state"]'
      ) as HTMLSelectElement;
      const options = stateSelect.querySelectorAll('option');

      expect(options.length).toBe(33); // 32 states + default option
      expect(options[0].textContent?.trim()).toBe('Selecciona un estado');
      expect(options[0].value).toBe('');

      // Check for specific states
      const jalOption = Array.from(
        options as NodeListOf<HTMLOptionElement>
      ).find(option => option.value === 'JC');
      const cmxOption = Array.from(
        options as NodeListOf<HTMLOptionElement>
      ).find(option => option.value === 'DF');

      expect(jalOption).toBeTruthy();
      expect(jalOption?.textContent?.trim()).toBe('Jalisco');
      expect(cmxOption).toBeTruthy();
      expect(cmxOption?.textContent?.trim()).toBe('Ciudad de México');
    });
  });
});
