import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NotifierService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import { of, throwError } from 'rxjs';
import { BulkOrderFormComponent } from '../../../../app/features/cards/components/bulk-order-form.component';
import {
  CardsService,
  BulkOrderRequest,
  CardLocation,
  CardLocationsResponse,
} from '../../../../app/features/cards/services/cards.service';

describe('BulkOrderFormComponent', () => {
  let component: BulkOrderFormComponent;
  let fixture: ComponentFixture<BulkOrderFormComponent>;
  let mockDialogRef: jasmine.SpyObj<DialogRef<any, any>>;
  let mockNotifier: jasmine.SpyObj<NotifierService>;
  let mockCardsService: jasmine.SpyObj<CardsService>;

  const mockLocationsResponse: CardLocationsResponse = {
    content: [
      {
        location_id: 'LOC001',
        name: 'Sucursal Centro',
        address1: 'Av. Principal 123',
        address2: null,
        city: 'México',
        state: 'DF',
        postalCode: '01000',
      },
      {
        location_id: 'LOC002',
        name: 'Sucursal Norte',
        address1: 'Calle Norte 456',
        address2: 'Piso 2',
        city: 'Guadalajara',
        state: 'JC',
        postalCode: '44100',
      },
    ],
    pageable: {
      pageNumber: 0,
      pageSize: 100,
      sort: [],
      offset: 0,
      paged: true,
      unpaged: false,
    },
    totalPages: 1,
    totalElements: 2,
    last: true,
    numberOfElements: 2,
    first: true,
    size: 100,
    number: 0,
    sort: [],
    empty: false,
  };

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('DialogRef', ['close']);
    mockNotifier = jasmine.createSpyObj('NotifierService', [
      'warning',
      'success',
      'error',
    ]);
    mockCardsService = jasmine.createSpyObj('CardsService', [
      'createBulkOrder',
      'getLocations',
    ]);

    // Setup default return values
    mockCardsService.getLocations.and.returnValue(of(mockLocationsResponse));

    await TestBed.configureTestingModule({
      imports: [
        BulkOrderFormComponent,
        ReactiveFormsModule,
        AplazoFormFieldDirectives,
        AplazoButtonComponent,
        AplazoCardComponent,
      ],
      providers: [
        { provide: DialogRef, useValue: mockDialogRef },
        { provide: NotifierService, useValue: mockNotifier },
        { provide: CardsService, useValue: mockCardsService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(BulkOrderFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Location Loading', () => {
    it('should load locations on component initialization', () => {
      expect(mockCardsService.getLocations).toHaveBeenCalledWith(0, 100);
      expect(component.locations().length).toBe(2);
      expect(component.locations()[0].location_id).toBe('LOC001');
      expect(component.locations()[1].location_id).toBe('LOC002');
    });

    it('should handle location loading error gracefully', () => {
      // Reset the locations to test error handling
      component.locations.set([]);

      // Set up error scenario for subsequent calls
      mockCardsService.getLocations.and.returnValue(
        throwError(() => new Error('Network error'))
      );

      // Clear previous calls
      mockNotifier.error.calls.reset();

      // Trigger the private loadLocations method by calling it through the component's public interface
      // Since loadLocations is private, we'll test it by simulating the error scenario
      (component as any).loadLocations();

      expect(mockNotifier.error).toHaveBeenCalledWith({
        title: 'Error al cargar ubicaciones',
        message: 'No se pudieron cargar las ubicaciones disponibles',
      });
      expect(component.locations().length).toBe(0);
    });
  });

  describe('Form Initialization', () => {
    it('should initialize form with default values', () => {
      expect(component.numberOfCardsControl.value).toBe(1);
      expect(component.shipToNameControl.value).toBe('');
      expect(component.shipToAddressControl.value).toBe('');
      expect(component.shipToCityControl.value).toBe('');
      expect(component.shipToStateOrProvinceControl.value).toBe('');
      expect(component.shipToPostalCodeControl.value).toBe('');
      expect(component.locationIdControl.value).toBe('');
    });

    it('should mark form as invalid initially', () => {
      expect(component.form.valid).toBeFalsy();
    });
  });

  describe('Form Validation', () => {
    describe('numberOfCards field', () => {
      it('should be required', () => {
        component.numberOfCardsControl.setValue(null as any);
        component.numberOfCardsControl.markAsTouched();

        expect(
          component.numberOfCardsControl.hasError('required')
        ).toBeTruthy();
      });

      it('should have minimum value of 1', () => {
        component.numberOfCardsControl.setValue(0);
        component.numberOfCardsControl.markAsTouched();

        expect(component.numberOfCardsControl.hasError('min')).toBeTruthy();
      });

      it('should have maximum value of 10000', () => {
        component.numberOfCardsControl.setValue(10001);
        component.numberOfCardsControl.markAsTouched();

        expect(component.numberOfCardsControl.hasError('max')).toBeTruthy();
      });

      it('should be valid with value between 1 and 10000', () => {
        component.numberOfCardsControl.setValue(100);
        component.numberOfCardsControl.markAsTouched();

        expect(component.numberOfCardsControl.valid).toBeTruthy();
      });
    });

    describe('shipToName field', () => {
      it('should be required', () => {
        component.shipToNameControl.setValue('');
        component.shipToNameControl.markAsTouched();

        expect(component.shipToNameControl.hasError('required')).toBeTruthy();
      });

      it('should have maximum length of 100', () => {
        component.shipToNameControl.setValue('a'.repeat(101));
        component.shipToNameControl.markAsTouched();

        expect(component.shipToNameControl.hasError('maxlength')).toBeTruthy();
      });
    });

    describe('shipToPostalCode field', () => {
      it('should be required', () => {
        component.shipToPostalCodeControl.setValue('');
        component.shipToPostalCodeControl.markAsTouched();

        expect(
          component.shipToPostalCodeControl.hasError('required')
        ).toBeTruthy();
      });

      it('should validate US postal code format', () => {
        component.shipToPostalCodeControl.setValue('12345');
        component.shipToPostalCodeControl.markAsTouched();

        expect(component.shipToPostalCodeControl.valid).toBeTruthy();
      });

      it('should validate extended US postal code format', () => {
        component.shipToPostalCodeControl.setValue('12345-6789');
        component.shipToPostalCodeControl.markAsTouched();

        expect(component.shipToPostalCodeControl.valid).toBeTruthy();
      });

      it('should validate Canadian postal code format', () => {
        component.shipToPostalCodeControl.setValue('K1A 0B1');
        component.shipToPostalCodeControl.markAsTouched();

        expect(component.shipToPostalCodeControl.valid).toBeTruthy();
      });

      it('should reject invalid postal code format', () => {
        component.shipToPostalCodeControl.setValue('invalid');
        component.shipToPostalCodeControl.markAsTouched();

        expect(
          component.shipToPostalCodeControl.hasError('pattern')
        ).toBeTruthy();
      });
    });

    describe('locationId field', () => {
      it('should be required', () => {
        component.locationIdControl.setValue('');
        component.locationIdControl.markAsTouched();

        expect(component.locationIdControl.hasError('required')).toBeTruthy();
      });
    });
  });

  describe('User Interactions', () => {
    it('should close dialog with confirmation false when cancel is clicked', () => {
      component.close(false);

      expect(mockDialogRef.close).toHaveBeenCalledWith({
        hasConfirmation: false,
      });
    });

    it('should close dialog with confirmation true when successful', () => {
      component.close(true);

      expect(mockDialogRef.close).toHaveBeenCalledWith({
        hasConfirmation: true,
      });
    });
  });

  describe('Form Submission', () => {
    beforeEach(() => {
      // Set up valid form data
      component.numberOfCardsControl.setValue(100);
      component.shipToNameControl.setValue('John Doe');
      component.shipToAddressControl.setValue('123 Main St');
      component.shipToCityControl.setValue('New York');
      component.shipToStateOrProvinceControl.setValue('NY');
      component.shipToPostalCodeControl.setValue('12345');
      component.locationIdControl.setValue('LOC123');
    });

    it('should show warning when form is invalid', () => {
      component.numberOfCardsControl.setValue(null as any);

      component.finish();

      expect(mockNotifier.warning).toHaveBeenCalledWith({
        title:
          'Por favor, completa los campos requeridos o corrije los errores indicados.',
      });
      expect(mockCardsService.createBulkOrder).not.toHaveBeenCalled();
    });

    it('should call service with correct data when form is valid', () => {
      const expectedRequest: BulkOrderRequest = {
        numberOfCards: 100,
        shipToName: 'John Doe',
        shipToAddress: '123 Main St',
        shipToCity: 'New York',
        shipToStateOrProvince: 'NY',
        shipToPostalCode: '12345',
        locationId: 'LOC123',
      };

      mockCardsService.createBulkOrder.and.returnValue(of({}));

      component.finish();

      expect(mockCardsService.createBulkOrder).toHaveBeenCalledWith(
        expectedRequest
      );
    });

    it('should show success notification and close dialog on successful creation', () => {
      mockCardsService.createBulkOrder.and.returnValue(of({}));

      component.finish();

      expect(mockNotifier.success).toHaveBeenCalledWith({
        title: 'Orden masiva creada exitosamente',
      });
      expect(mockDialogRef.close).toHaveBeenCalledWith({
        hasConfirmation: true,
      });
    });

    it('should show error notification on creation failure', () => {
      const error = new Error('Service error');
      mockCardsService.createBulkOrder.and.returnValue(throwError(() => error));

      component.finish();

      expect(mockNotifier.error).toHaveBeenCalledWith({
        title: 'Error al crear la orden masiva',
        message: 'Por favor intenta nuevamente',
      });
      expect(mockDialogRef.close).not.toHaveBeenCalled();
    });

    it('should mark all fields as touched on submission', () => {
      mockCardsService.createBulkOrder.and.returnValue(of({}));

      component.finish();

      expect(component.numberOfCardsControl.touched).toBeTruthy();
      expect(component.shipToNameControl.touched).toBeTruthy();
      expect(component.shipToAddressControl.touched).toBeTruthy();
      expect(component.shipToCityControl.touched).toBeTruthy();
      expect(component.shipToStateOrProvinceControl.touched).toBeTruthy();
      expect(component.shipToPostalCodeControl.touched).toBeTruthy();
      expect(component.locationIdControl.touched).toBeTruthy();
    });
  });

  describe('Template', () => {
    it('should display form fields', () => {
      const compiled = fixture.debugElement.nativeElement;

      expect(
        compiled.querySelector('input[formControlName="numberOfCards"]')
      ).toBeTruthy();
      expect(
        compiled.querySelector('input[formControlName="shipToName"]')
      ).toBeTruthy();
      expect(
        compiled.querySelector('input[formControlName="shipToAddress"]')
      ).toBeTruthy();
      expect(
        compiled.querySelector('input[formControlName="shipToCity"]')
      ).toBeTruthy();
      expect(
        compiled.querySelector(
          'select[formControlName="shipToStateOrProvince"]'
        )
      ).toBeTruthy();
      expect(
        compiled.querySelector('input[formControlName="shipToPostalCode"]')
      ).toBeTruthy();
      expect(
        compiled.querySelector('select[formControlName="locationId"]')
      ).toBeTruthy();
    });

    it('should display cancel and submit buttons', () => {
      const compiled = fixture.debugElement.nativeElement;
      const buttons = compiled.querySelectorAll('button');

      expect(buttons.length).toBe(2);
      expect(buttons[0].textContent.trim()).toContain('Cancelar');
      expect(buttons[1].textContent.trim()).toContain('Crear');
    });

    it('should disable submit button when form is disabled', () => {
      component.form.disable();
      fixture.detectChanges();

      const compiled = fixture.debugElement.nativeElement;
      const submitButton = compiled.querySelector('button[type="submit"]');

      expect(submitButton.disabled).toBeTruthy();
    });

    it('should display location options in dropdown', () => {
      fixture.detectChanges();
      const compiled = fixture.debugElement.nativeElement;
      const locationSelect = compiled.querySelector(
        'select[formControlName="locationId"]'
      );
      const options = locationSelect.querySelectorAll('option');

      // Should have placeholder option plus 2 location options
      expect(options.length).toBe(3);
      expect(options[0].textContent.trim()).toBe('Selecciona una ubicación');
      expect(options[1].value).toBe('LOC001');
      expect(options[1].textContent.trim()).toBe(
        'Sucursal Centro - México, DF'
      );
      expect(options[2].value).toBe('LOC002');
      expect(options[2].textContent.trim()).toBe(
        'Sucursal Norte - Guadalajara, JC'
      );
    });
  });
});
