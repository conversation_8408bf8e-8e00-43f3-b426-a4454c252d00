import {
  RangeNode,
  Ranges,
} from '../../../../app/features/campaigns/domain/entities/range-as-linked-list';

describe('Range as Linked List', () => {
  describe('RangeNode', () => {
    it('should create a new range node without a name', () => {
      const newNode = new RangeNode(7);
      const expected = {
        end: 7,
        start: 1,
        name: '',
      };

      expect(newNode.getValue()).toEqual(expected);
    });

    it('should has a node with next node and getValue returns the correct serialization', () => {
      const newNode = new RangeNode(7);
      const nextNode = new RangeNode(4);
      const expectedValueCurrentNode = {
        end: 7,
        start: 5,
        name: '',
      };
      const expectedValueNextNode = {
        end: 4,
        start: 1,
        name: '',
      };

      newNode.setNext(nextNode);

      expect(newNode.getValue()).toEqual(expectedValueCurrentNode);
      expect(nextNode.getValue()).toEqual(expectedValueNextNode);
    });
  });

  describe('Ranges', () => {
    describe('addOne', () => {
      it('should create a ranges list with one range', () => {
        const ranges = new Ranges(7);

        const expected = {
          end: 7,
          start: 1,
          name: '',
        };

        expect(ranges.head?.getValue()).toEqual(expected);
      });

      it('should create a ranges list with two ranges', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 5,
            name: '',
          },
          {
            end: 4,
            start: 1,
            name: '',
          },
        ];

        ranges.addOne();

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should create a ranges list with three ranges', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 7,
            name: '',
          },
          {
            end: 6,
            start: 5,
            name: '',
          },
          {
            end: 4,
            start: 1,
            name: '',
          },
        ];

        ranges.addOne();
        ranges.addOne();

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should create a ranges list with four ranges', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 7,
            name: '',
          },
          {
            end: 6,
            start: 6,
            name: '',
          },
          {
            end: 5,
            start: 5,
            name: '',
          },
          {
            end: 4,
            start: 1,
            name: '',
          },
        ];

        ranges.addOne();
        ranges.addOne();
        ranges.addOne();

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should create a ranges list with five ranges', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 7,
            name: '',
          },
          {
            end: 6,
            start: 6,
            name: '',
          },
          {
            end: 5,
            start: 5,
            name: '',
          },
          {
            end: 4,
            start: 3,
            name: '',
          },
          {
            end: 2,
            start: 1,
            name: '',
          },
        ];

        ranges.addOne();
        ranges.addOne();
        ranges.addOne();
        ranges.addOne();

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should throw an error if trying to add a range to a single node', () => {
        const ranges = new Ranges(3);

        ranges.addOne();
        ranges.addOne();

        expect(() => ranges.addOne()).toThrowError(
          'Cannot add a range to a single node'
        );
      });
    });

    describe('deleteOneByEnd', () => {
      it('should delete a range by end', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 7,
            name: '',
          },
          {
            end: 6,
            start: 5,
            name: '',
          },
          {
            end: 4,
            start: 1,
            name: '',
          },
        ];

        ranges.addOne();
        ranges.addOne();
        ranges.addOne();
        ranges.deleteOneByEnd(6);

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should throw an error if trying to delete a range with an end greater than the list', () => {
        const ranges = new Ranges(7);
        ranges.addOne();
        ranges.addOne();

        expect(() => ranges.deleteOneByEnd(8)).toThrowError(
          'Cannot delete a range that does not exist'
        );
      });

      it('should throw an error if trying to delete a range with an end less than 1', () => {
        const ranges = new Ranges(7);
        ranges.addOne();
        ranges.addOne();

        expect(() => ranges.deleteOneByEnd(-1)).toThrowError(
          'Cannot delete a range that does not exist'
        );
      });

      it('should have not changes if trying to delete a range that does not exist', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 1,
            name: '',
          },
        ];

        ranges.display();

        ranges.deleteOneByEnd(5);

        ranges.display();

        expect(ranges.getValueDescending()).toEqual(expected);
      });
    });

    describe('updateOneByEnd', () => {
      it('should update a range by end', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 7,
            name: '',
          },
          {
            end: 6,
            start: 6,
            name: '',
          },
          {
            end: 5,
            start: 4,
            name: '',
          },
          {
            end: 3,
            start: 1,
            name: 'updated',
          },
        ];

        ranges.addOne();
        ranges.addOne();
        ranges.addOne();
        ranges.updateOneByEnd(4, 3, 'updated');

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should update a range by end same end-start', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 6,
            name: '',
          },
          {
            end: 5,
            start: 5,
            name: 'updated',
          },
          {
            end: 4,
            start: 1,
            name: '',
          },
        ];

        ranges.addOne();
        ranges.addOne();

        ranges.updateOneByEnd(6, 5, 'updated');

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should merge ranges if the newEnd is equal to the next range start', () => {
        const ranges = new Ranges(7);
        ranges.addOne();
        ranges.addOne();
        ranges.addOne();
        const expected = [
          {
            end: 7,
            start: 7,
            name: '',
          },
          {
            end: 6,
            start: 5,
            name: 'updated',
          },
          {
            end: 4,
            start: 1,
            name: '',
          },
        ];

        ranges.updateOneByEnd(6, 5, 'updated');

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should update a range by end with newEnd greater than the next range', () => {
        const ranges = new Ranges(7);
        ranges.addOne();
        ranges.addOne();
        ranges.addOne();
        const expected = [
          {
            end: 7,
            start: 6,
            name: 'updated',
          },
          {
            end: 5,
            start: 5,
            name: '',
          },
          {
            end: 4,
            start: 1,
            name: '',
          },
        ];

        ranges.updateOneByEnd(6, 7, 'updated');

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should not do changes when updating range with end greater than max range', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 7,
            name: '',
          },
          {
            end: 6,
            start: 5,
            name: '',
          },
          {
            end: 4,
            start: 1,
            name: '',
          },
        ];
        ranges.addOne();
        ranges.addOne();
        ranges.updateOneByEnd(7, 8, 'updated');

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should not do changes when newEnd is less than 1', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 5,
            name: '',
          },
          {
            end: 4,
            start: 1,
            name: '',
          },
        ];
        ranges.addOne();
        ranges.updateOneByEnd(4, 0, '');

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should not do changes when updating non-existent range', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 5,
            name: '',
          },
          {
            end: 4,
            start: 1,
            name: '',
          },
        ];
        ranges.addOne();

        ranges.updateOneByEnd(3, 2, 'updated');

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should not do changes when there is only one range and newEnd is less than the end of the range', () => {
        const ranges = new Ranges(7);
        const expected = [
          {
            end: 7,
            start: 1,
            name: '',
          },
        ];

        ranges.updateOneByEnd(7, 5, 'updated');

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should handle update when range is the last node', () => {
        const ranges = new Ranges(7);
        ranges.addOne();
        ranges.addOne();
        const expected = [
          {
            end: 7,
            start: 7,
            name: '',
          },
          {
            end: 6,
            start: 4,
            name: '',
          },
          {
            end: 3,
            start: 1,
            name: 'updated',
          },
        ];

        ranges.updateOneByEnd(4, 3, 'updated');

        expect(ranges.getValueDescending()).toEqual(expected);
      });

      it('should merge with previous range when newEnd matches previous range start', () => {
        const ranges = new Ranges(7);
        ranges.addOne();
        ranges.addOne();
        const expected = [
          {
            end: 7,
            start: 5,
            name: 'updated',
          },
          {
            end: 4,
            start: 1,
            name: '',
          },
        ];

        ranges.updateOneByEnd(6, 7, 'updated');

        expect(ranges.getValueDescending()).toEqual(expected);
      });
    });
  });
});
