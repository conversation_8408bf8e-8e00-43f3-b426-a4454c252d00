import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { ConfirmPrizeReceivedUsecase } from '../../../../../app/features/campaigns/application/use_cases/confirm-prize-received.usecase';
import { CampaignsRepository } from '../../../../../app/features/campaigns/domain/repositories/premios-aplazo';

describe('ConfirmPrizeReceivedUsecase', () => {
  let usecase: ConfirmPrizeReceivedUsecase;
  let repository: jasmine.SpyObj<CampaignsRepository>;
  let loaderService: jasmine.SpyObj<LoaderService>;
  let notifierService: jasmine.SpyObj<NotifierService>;
  let errorHandler: jasmine.SpyObj<UseCaseErrorHandler>;

  beforeEach(() => {
    const repositorySpy = jasmine.createSpyObj('CampaignsRepository', [
      'confirmPrizeReceived',
    ]);
    const loaderServiceSpy = jasmine.createSpyObj('LoaderService', [
      'show',
      'hide',
    ]);
    const notifierServiceSpy = jasmine.createSpyObj('NotifierService', [
      'success',
    ]);
    const errorHandlerSpy = jasmine.createSpyObj('UseCaseErrorHandler', [
      'handle',
    ]);

    TestBed.configureTestingModule({
      providers: [
        ConfirmPrizeReceivedUsecase,
        { provide: CampaignsRepository, useValue: repositorySpy },
        { provide: LoaderService, useValue: loaderServiceSpy },
        { provide: NotifierService, useValue: notifierServiceSpy },
        { provide: UseCaseErrorHandler, useValue: errorHandlerSpy },
      ],
    });

    usecase = TestBed.inject(ConfirmPrizeReceivedUsecase);
    repository = TestBed.inject(
      CampaignsRepository
    ) as jasmine.SpyObj<CampaignsRepository>;
    loaderService = TestBed.inject(
      LoaderService
    ) as jasmine.SpyObj<LoaderService>;
    notifierService = TestBed.inject(
      NotifierService
    ) as jasmine.SpyObj<NotifierService>;
    errorHandler = TestBed.inject(
      UseCaseErrorHandler
    ) as jasmine.SpyObj<UseCaseErrorHandler>;
  });

  it('should confirm prize received successfully', fakeAsync(() => {
    const args = { campaignId: 1, winnerId: 1, prizeReceived: true };
    repository.confirmPrizeReceived.and.returnValue(of(void 0));
    loaderService.show.and.returnValue('loaderId');

    usecase.execute(args).subscribe();
    tick();

    expect(loaderService.show).toHaveBeenCalled();
    expect(repository.confirmPrizeReceived).toHaveBeenCalledWith(args);
    expect(notifierService.success).toHaveBeenCalledWith({
      title: 'Ganador confirmado',
      message: 'El ganador se actualizada exitosamente',
    });
    expect(loaderService.hide).toHaveBeenCalledWith('loaderId');
  }));

  it('should handle HttpErrorResponse', fakeAsync(() => {
    const args = { campaignId: 1, winnerId: 1, prizeReceived: true };
    const errorResponse = new HttpErrorResponse({
      error: 'test 404 error',
      status: 404,
    });
    repository.confirmPrizeReceived.and.returnValue(
      throwError(() => errorResponse)
    );
    loaderService.show.and.returnValue('loaderId');

    usecase.execute(args).subscribe({
      error: () => {},
    });
    tick();

    expect(loaderService.show).toHaveBeenCalled();
    expect(repository.confirmPrizeReceived).toHaveBeenCalledWith(args);
    expect(errorHandler.handle).toHaveBeenCalledWith(
      jasmine.any(RuntimeMerchantError)
    );
    expect(loaderService.hide).toHaveBeenCalledWith('loaderId');
  }));

  it('should handle other errors', fakeAsync(() => {
    const args = { campaignId: 1, winnerId: 1, prizeReceived: true };
    const error = new Error('test error');
    repository.confirmPrizeReceived.and.returnValue(throwError(() => error));
    loaderService.show.and.returnValue('loaderId');

    usecase.execute(args).subscribe({
      error: () => {},
    });
    tick();

    expect(loaderService.show).toHaveBeenCalled();
    expect(repository.confirmPrizeReceived).toHaveBeenCalledWith(args);
    expect(errorHandler.handle).toHaveBeenCalledWith(error);
    expect(loaderService.hide).toHaveBeenCalledWith('loaderId');
  }));
});
