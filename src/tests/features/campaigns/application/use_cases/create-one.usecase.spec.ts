/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  TemporalService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { CreateOneCampaignUsecase } from '../../../../../app/features/campaigns/application/use_cases/create-one.usecase';
import { CampaignsRepository } from '../../../../../app/features/campaigns/domain/repositories/premios-aplazo';

describe('CreateOneCampaignusecase', () => {
  let usecase: CreateOneCampaignUsecase;
  let repository: jasmine.SpyObj<CampaignsRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        CreateOneCampaignUsecase,
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: CampaignsRepository,
          useValue: jasmine.createSpyObj('CampaignsRepository', ['createOne']),
        },
        {
          provide: TemporalService,
          useValue: jasmine.createSpyObj('TemporalService', [
            'formatRawDateDayFirst',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(CreateOneCampaignUsecase);
    repository = TestBed.inject(
      CampaignsRepository
    ) as jasmine.SpyObj<CampaignsRepository>;
    const loader = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();

    const notifier = TestBed.inject(NotifierService);
    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();

    const errorHandler = TestBed.inject(UseCaseErrorHandler);
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(CreateOneCampaignUsecase);
  });

  it('should create a campaign successfully', fakeAsync(() => {
    const campaign = {
      name: 'Test Campaign',
      startDate: new Date(),
      endDate: new Date(),
      tyc: 'Test Tyc',
      numberOfWinners: 1,
      prizes: [
        {
          fromRank: 1,
          toRank: 1,
          prizeName: 'Test prize',
        },
      ],
    };
    const idLoader = 'loaderId';
    repository.createOne.and.returnValue(of(void 0));
    showLoaderSpy.and.returnValue(idLoader);

    let result: any;

    usecase.execute(campaign).subscribe({
      next: r => (result = r),
      error: fail,
    });

    flush();

    expect(showLoaderSpy).toHaveBeenCalled();
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
    expect(successNotifierSpy).toHaveBeenCalledWith({
      title: 'Campaña creada con éxito',
    });
  }));

  it('should handle error when campaign name is empty', fakeAsync(() => {
    const campaign = {
      name: '',
      startDate: new Date(),
      endDate: new Date(),
      tyc: 'Test Tyc',
      numberOfWinners: 1,
      prizes: [
        {
          fromRank: 1,
          toRank: 1,
          prizeName: 'Test prize',
        },
      ],
    };
    const idLoader = 'loaderId';
    showLoaderSpy.and.returnValue(idLoader);

    let errorResult: any;

    usecase.execute(campaign).subscribe({
      next: fail,
      error: err => {
        errorResult = err;
      },
    });

    flush();

    expect(errorResult).toBeInstanceOf(RuntimeMerchantError);
    expect(errorResult.message).toBe('El nombre de la campaña es requerido');
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
  }));

  it('should handle error when start date is invalid', fakeAsync(() => {
    const campaign = {
      name: 'Test Campaign',
      startDate: new Date('invalid-date'),
      endDate: new Date(),
      tyc: 'Test Tyc',
      numberOfWinners: 1,
      prizes: [
        {
          fromRank: 1,
          toRank: 1,
          prizeName: 'Test prize',
        },
      ],
    };
    const idLoader = 'loaderId';
    showLoaderSpy.and.returnValue(idLoader);

    let errorResult: any;

    usecase.execute(campaign).subscribe({
      next: fail,
      error: err => {
        errorResult = err;
      },
    });

    flush();

    expect(errorResult).toBeInstanceOf(RuntimeMerchantError);
    expect(errorResult.message).toBe('Fecha de inicio inválida');
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
  }));

  it('should handle error from repository', fakeAsync(() => {
    const campaign = {
      name: 'Test Campaign',
      startDate: new Date(),
      endDate: new Date(),
      tyc: 'Test Tyc',
      numberOfWinners: 1,
      prizes: [
        {
          fromRank: 1,
          toRank: 1,
          prizeName: 'Test prize',
        },
      ],
    };
    const idLoader = 'loaderId';
    const mockError = new Error('Repository error');
    repository.createOne.and.returnValue(throwError(mockError));
    showLoaderSpy.and.returnValue(idLoader);

    let errorResult: any;

    usecase.execute(campaign).subscribe({
      next: fail,
      error: err => {
        errorResult = err;
      },
    });

    flush();

    expect(handleErrorSpy).toHaveBeenCalledWith(mockError);
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
  }));
});
