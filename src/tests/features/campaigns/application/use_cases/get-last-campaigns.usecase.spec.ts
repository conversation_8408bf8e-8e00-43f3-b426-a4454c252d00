/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { GetLastCampaignsUseCase } from '../../../../../app/features/campaigns/application/use_cases/get-last-campaings.usecase';
import {
  CampaignsRepository,
  SimplifiedCampaignResponse,
} from '../../../../../app/features/campaigns/domain/repositories/premios-aplazo';

describe('GetLastCampaignsUseCase', () => {
  let usecase: GetLastCampaignsUseCase;
  let repository: jasmine.SpyObj<CampaignsRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        GetLastCampaignsUseCase,
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: CampaignsRepository,
          useValue: jasmine.createSpyObj('CampaignsRepository', [
            'getLastOnes',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(GetLastCampaignsUseCase);
    repository = TestBed.inject(
      CampaignsRepository
    ) as jasmine.SpyObj<CampaignsRepository>;
    const loader = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();

    const errorHandler = TestBed.inject(UseCaseErrorHandler);
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GetLastCampaignsUseCase);
  });

  it('should fetch campaign winners select items successfully', fakeAsync(() => {
    const idLoader = 'loaderId';
    const mockResponse: SimplifiedCampaignResponse = {
      campaigns: [
        { id: 1, name: 'Campaign 1' },
        { id: 2, name: 'Campaign 2' },
      ],
    };

    repository.getLastOnes.and.returnValue(of(mockResponse));
    showLoaderSpy.and.returnValue(idLoader);

    let result: any;

    usecase.execute().subscribe({
      next: r => (result = r),
      error: fail,
    });

    flush();

    expect(showLoaderSpy).toHaveBeenCalled();
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
    expect(result).toEqual([
      { id: 1, name: 'Campaign 1' },
      { id: 2, name: 'Campaign 2' },
    ]);
  }));

  it('should handle error from repository', fakeAsync(() => {
    const idLoader = 'loaderId';
    const mockError = new Error('Repository error');
    repository.getLastOnes.and.returnValue(throwError(() => mockError));
    showLoaderSpy.and.returnValue(idLoader);

    let errorResult: any;

    usecase.execute().subscribe({
      next: fail,
      error: err => {
        errorResult = err;
      },
    });

    flush();

    expect(handleErrorSpy).toHaveBeenCalledWith(mockError);
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
  }));

  it('should handle HttpErrorResponse with status 404', fakeAsync(() => {
    const idLoader = 'loaderId';
    const httpError = new HttpErrorResponse({
      status: 404,
      error: { code: 'APZRCS012', error: 'Campaign not found' },
    });
    repository.getLastOnes.and.returnValue(throwError(() => httpError));
    showLoaderSpy.and.returnValue(idLoader);

    let result: any;

    usecase.execute().subscribe({
      next: r => (result = r),
      error: fail,
    });

    flush();

    expect(result).toEqual([]);
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
  }));

  it('should handle HttpErrorResponse with other status', fakeAsync(() => {
    const idLoader = 'loaderId';
    const httpError = new HttpErrorResponse({
      status: 500,
      error: { message: 'Server Error' },
    });
    repository.getLastOnes.and.returnValue(throwError(() => httpError));
    showLoaderSpy.and.returnValue(idLoader);

    let errorResult: any;

    usecase.execute().subscribe({
      next: fail,
      error: err => {
        errorResult = err;
      },
    });

    flush();

    expect(errorResult).toBeInstanceOf(RuntimeMerchantError);
    expect(errorResult.message).toBe('Error cargando campañas');
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
  }));
});
