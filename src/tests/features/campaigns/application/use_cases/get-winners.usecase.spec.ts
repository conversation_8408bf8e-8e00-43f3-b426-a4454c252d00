/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { GetWinnersByCampaign } from '../../../../../app/features/campaigns/application/use_cases/get-winners.usecase';
import { CampaignWinner } from '../../../../../app/features/campaigns/domain/entities';
import { CampaignsRepository } from '../../../../../app/features/campaigns/domain/repositories/premios-aplazo';

describe('GetWinnersByCampaign', () => {
  let usecase: GetWinnersByCampaign;
  let repository: jasmine.SpyObj<CampaignsRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        GetWinnersByCampaign,
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: CampaignsRepository,
          useValue: jasmine.createSpyObj('CampaignsRepository', [
            'getWinnersBy',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(GetWinnersByCampaign);
    repository = TestBed.inject(
      CampaignsRepository
    ) as jasmine.SpyObj<CampaignsRepository>;
    const loader = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();

    const errorHandler = TestBed.inject(UseCaseErrorHandler);
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GetWinnersByCampaign);
  });

  it('should fetch campaign winners successfully', fakeAsync(() => {
    const campaignId = 1;
    const idLoader = 'loaderId';
    const winners: CampaignWinner[] = [
      {
        id: 500,
        position: 1,
        participantName: 'mario gonzales gonzales',
        usersRegistered: 1,
        address: 'calle siempre viva, springfield',
        prize: '',
        prizeReceived: false,
      },
    ];

    repository.getWinnersBy.and.returnValue(of(winners));
    showLoaderSpy.and.returnValue(idLoader);

    let result: any;

    usecase.execute({ campaignId }).subscribe({
      next: r => (result = r),
      error: fail,
    });

    flush();

    expect(showLoaderSpy).toHaveBeenCalled();
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
    expect(result).toEqual(winners);
  }));

  it('should handle error from repository', fakeAsync(() => {
    const campaignId = 1;
    const idLoader = 'loaderId';
    const mockError = new Error('Repository error');
    repository.getWinnersBy.and.returnValue(throwError(mockError));
    showLoaderSpy.and.returnValue(idLoader);

    let errorResult: any;

    usecase.execute({ campaignId }).subscribe({
      next: fail,
      error: err => {
        errorResult = err;
      },
    });

    flush();

    expect(handleErrorSpy).toHaveBeenCalledWith(mockError);
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
  }));

  it('should handle HttpErrorResponse with status 404', fakeAsync(() => {
    const campaignId = 1;
    const idLoader = 'loaderId';
    const httpError = new HttpErrorResponse({
      status: 404,
      error: { code: 'APZRCS012', error: 'Winners not found' },
    });
    repository.getWinnersBy.and.returnValue(throwError(httpError));
    showLoaderSpy.and.returnValue(idLoader);

    let result: any;

    usecase.execute({ campaignId }).subscribe({
      next: r => (result = r),
      error: fail,
    });

    flush();

    expect(result).toEqual([]);
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
  }));

  it('should handle HttpErrorResponse with other status', fakeAsync(() => {
    const campaignId = 1;
    const idLoader = 'loaderId';
    const httpError = new HttpErrorResponse({
      status: 400,
      error: { message: 'Bad Request' },
    });
    repository.getWinnersBy.and.returnValue(throwError(httpError));
    showLoaderSpy.and.returnValue(idLoader);

    let errorResult: any;

    usecase.execute({ campaignId }).subscribe({
      next: fail,
      error: err => {
        errorResult = err;
      },
    });

    flush();

    expect(errorResult).toBeInstanceOf(RuntimeMerchantError);
    expect(errorResult.message).toBe('Error cargando ganadores');
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
  }));
});
