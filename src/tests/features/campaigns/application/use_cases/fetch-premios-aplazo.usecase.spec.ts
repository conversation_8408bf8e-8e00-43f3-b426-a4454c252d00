/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  TemporalService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { FetchAPremiosAplazoUsecase } from '../../../../../app/features/campaigns/application/use_cases/fetch-premios-aplazo.usecase';
import { PremiosAplazoCampaign } from '../../../../../app/features/campaigns/domain/entities';
import {
  CampaignListResponse,
  CampaignsRepository,
} from '../../../../../app/features/campaigns/domain/repositories/premios-aplazo';

describe('FetchAPremiosAplazoUsecase', () => {
  let usecase: FetchAPremiosAplazoUsecase;
  let repository: jasmine.SpyObj<CampaignsRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        FetchAPremiosAplazoUsecase,
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: CampaignsRepository,
          useValue: jasmine.createSpyObj('CampaignsRepository', [
            'findCampaigns',
          ]),
        },
        {
          provide: TemporalService,
          useValue: jasmine.createSpyObj('TemporalService', [
            'formatRawDateDayFirst',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(FetchAPremiosAplazoUsecase);
    repository = TestBed.inject(
      CampaignsRepository
    ) as jasmine.SpyObj<CampaignsRepository>;
    const loader = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();

    const notifier = TestBed.inject(NotifierService);
    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();

    const errorHandler = TestBed.inject(UseCaseErrorHandler);
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(FetchAPremiosAplazoUsecase);
  });

  it('should fetch campaigns successfully', fakeAsync(() => {
    const pageNum = 0;
    const idLoader = 'loaderId';
    const content: PremiosAplazoCampaign[] = [
      {
        id: 48,
        name: 'Test',
        startDate: '2025-04-01',
        endDate: '2025-04-06',
        active: false,
        createdBy: '<EMAIL>',
        createdAt: '2025-01-28T00:28:19.537214',
        termsConditions: null,
        numberOfWinners: 1,
      },
    ];
    const campaignRes = {
      content: content,
      totalPages: 2,
      totalElements: 12,
    };

    const campaignsResponse: CampaignListResponse = campaignRes;

    repository.findCampaigns.and.returnValue(of(campaignsResponse));
    showLoaderSpy.and.returnValue(idLoader);

    let result: any;

    usecase.execute({ pageNum }).subscribe({
      next: r => (result = r),
      error: fail,
    });

    flush();

    expect(showLoaderSpy).toHaveBeenCalled();
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
    expect(result).toEqual(campaignsResponse);
  }));

  it('should handle error from repository', fakeAsync(() => {
    const pageNum = 0;
    const idLoader = 'loaderId';
    const mockError = new Error('Repository error');
    repository.findCampaigns.and.returnValue(throwError(mockError));
    showLoaderSpy.and.returnValue(idLoader);

    let errorResult: any;

    usecase.execute({ pageNum }).subscribe({
      next: fail,
      error: err => {
        errorResult = err;
      },
    });

    flush();

    expect(handleErrorSpy).toHaveBeenCalledWith(mockError);
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
  }));

  it('should handle HttpErrorResponse with status 400', fakeAsync(() => {
    const pageNum = 0;
    const idLoader = 'loaderId';
    const httpError = new HttpErrorResponse({
      status: 400,
      error: { message: 'Bad Request' },
    });
    repository.findCampaigns.and.returnValue(throwError(httpError));
    showLoaderSpy.and.returnValue(idLoader);

    let errorResult: any;

    usecase.execute({ pageNum }).subscribe({
      next: fail,
      error: err => {
        errorResult = err;
      },
    });

    flush();

    expect(errorResult).toBeInstanceOf(RuntimeMerchantError);
    expect(errorResult.message).toBe('Error cargando datos');
    expect(hideLoaderSpy).toHaveBeenCalledWith(idLoader);
  }));
});
