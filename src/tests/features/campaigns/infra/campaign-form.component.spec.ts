import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import {
  NotifierService,
  RuntimeMerchantError,
  TemporalService,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
} from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import { StatsigService } from '@statsig/angular-bindings';
import { of, throwError } from 'rxjs';
import { CreateOneCampaignUsecase } from '../../../../app/features/campaigns/application/use_cases/create-one.usecase';
import { UpdateOneCampaignUsecase } from '../../../../app/features/campaigns/application/use_cases/update-one.usecase';
import { CampaignFormComponent } from '../../../../app/features/campaigns/infra/components/campaign-form.component';
import { PrizeDistributionComponent } from '../../../../app/features/campaigns/infra/components/distribution/prize-distribution.component';

describe('CampaignFormComponent', () => {
  let component: CampaignFormComponent;
  let fixture: ComponentFixture<CampaignFormComponent>;
  let createOneSpy: jasmine.Spy;
  let warningNotificationSpy: jasmine.Spy;
  let closeDialogSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        AplazoFormFieldDirectives,
        AplazoButtonComponent,
        AplazoCardComponent,
        AplazoFormDatepickerComponent,
        PrizeDistributionComponent,
      ],
      providers: [
        {
          provide: CreateOneCampaignUsecase,
          useValue: {
            execute: () => {
              return of(void 0);
            },
          },
        },
        TemporalService,
        provideLoaderTesting(),
        provideNotifierTesting(),
        {
          provide: DialogRef,
          useValue: {
            data: undefined,
            close: () => {},
          },
        },
        {
          provide: UpdateOneCampaignUsecase,
          useValue: {
            execute: () => {
              return of(void 0);
            },
          },
        },
        {
          provide: StatsigService,
          useValue: {
            checkGate: () => false,
          },
        },
      ],
    });

    fixture = TestBed.createComponent(CampaignFormComponent);
    component = fixture.componentInstance;
    createOneSpy = spyOn(TestBed.inject(CreateOneCampaignUsecase), 'execute');
    fixture.detectChanges();

    const dialogRef = TestBed.inject(DialogRef);
    closeDialogSpy = spyOn(dialogRef, 'close');
    const notifier = TestBed.inject(NotifierService);
    warningNotificationSpy = spyOn(notifier, 'warning').and.callThrough();
  });

  beforeEach(() => {
    component.form.reset();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should not submit form if invalid', () => {
    component.form.markAllAsTouched();
    component.finish();

    expect(createOneSpy).not.toHaveBeenCalled();
  });

  it('should submit form and close dialog on success', fakeAsync(() => {
    createOneSpy.and.returnValue(of(void 0));

    component.form.setValue({
      name: 'Test Campaign',
      date: [new Date('2025/01/10'), new Date('2025/01/15')],
      tyc: 'https://cdn.aplazo.mx/reglas/enero25.pdf',
      prizeDistribution: {
        numberOfWinners: 2,
        prizes: [
          {
            toRank: 2,
            fromRank: 1,
            prizeName: 'Test Prize',
          },
        ],
      },
    });

    tick();
    fixture.detectChanges();
    console.log(
      'debug:: component.form.value 123 :: ',
      component.prizeDistributionControl
    );

    component.finish();
    tick();

    expect(createOneSpy).toHaveBeenCalled();
    expect(closeDialogSpy).toHaveBeenCalledWith({
      hasConfirmation: true,
      data: {
        name: 'Test Campaign',
        dateRange: [new Date('2025/01/10'), new Date('2025/01/15')],
        tyc: 'https://cdn.aplazo.mx/reglas/enero25.pdf',
        numberOfWinners: 2,
        prizes: [
          {
            toRank: 2,
            fromRank: 1,
            prizeName: 'Test Prize',
          },
        ],
      },
    });
  }));

  it('should show warning when the form has errors', fakeAsync(() => {
    const errorResponse = new RuntimeMerchantError(
      'El nombre de la campaña es requerido',
      'GetOneWithDetailsUseCase::participantNotFound'
    );
    const spy = spyOn(component, 'close').and.callThrough();

    createOneSpy.and.returnValue(throwError(() => errorResponse));

    component.form.setValue({
      name: '',
      date: [new Date(), new Date()],
      tyc: 'Test Tyc',
      prizeDistribution: {
        numberOfWinners: 1,
        prizes: [],
      },
    });

    fixture.detectChanges();

    component.finish();
    tick();

    expect(warningNotificationSpy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledTimes(0);
  }));

  it('should close dialog without confirmation', () => {
    component.close(false);

    expect(closeDialogSpy).toHaveBeenCalledWith({
      hasConfirmation: false,
      data: null,
    });
  });

  it('should validate campaign name control', () => {
    const nameControl = component.form.get('name');
    nameControl?.setValue('');
    expect(nameControl?.valid).toBeFalse();

    nameControl?.setValue('Te');
    expect(nameControl?.valid).toBeFalse();

    nameControl?.setValue('Test Campaign');
    expect(nameControl?.valid).toBeTrue();
  });

  it('should validate date control', () => {
    const dateControl = component.form.get('date');
    dateControl?.setValue(null);
    expect(dateControl?.valid).toBeFalse();

    dateControl?.setValue([new Date(), new Date()]);
    expect(dateControl?.valid).toBeTrue();
  });
});
