import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { RawDateDayFirst } from '@aplazo/merchant/shared';
import { CampaignWinner } from '../../../../../app/features/campaigns/domain/entities';
import { CampaignsWithHttp } from '../../../../../app/features/campaigns/infra/respositories/rest-campaign-premios-aplazo-fetcher';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';
import {
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';

describe('CampaignsWithHttp', () => {
  let service: CampaignsWithHttp;
  let httpTestingController: HttpTestingController;
  const mockBaseUrl = 'https://api.test.com';

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        CampaignsWithHttp,
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: { gatewayUrl: mockBaseUrl },
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    service = TestBed.inject(CampaignsWithHttp);
    httpTestingController = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('findCampaigns', () => {
    it('should make GET request with pagination parameter', () => {
      const mockResponse = {
        content: [
          {
            id: 203,
            name: 'newCampaignCreationUpdate',
            startDate: '2040-04-11',
            endDate: '2040-04-12',
            active: false,
            createdBy: '1561',
            createdAt: '2025-02-28T19:47:49.982056',
            termsConditions: 'https://cdn.aplazo.mx/reglas/testing_terms.pdf',
            numberOfWinners: 16,
          },
          {
            id: 202,
            name: 'asdas',
            startDate: '2039-01-01',
            endDate: '2039-01-31',
            active: false,
            createdBy: '<EMAIL>',
            createdAt: '2025-02-27T03:51:11.585417',
            termsConditions: null,
            numberOfWinners: 10,
          },
        ],
        totalPages: 5,
        totalElements: 42,
        last: false,
        numberOfElements: 10,
        size: 10,
        number: 0,
        first: true,
        empty: false,
      };

      // We still pass date parameters for backend compatibility but focus
      // on testing pagination functionality since dates are no longer used for filtering
      service
        .findCampaigns({
          pageNum: 0,
        })
        .subscribe(response => {
          expect(response.content.length).toEqual(2);
          expect(response.totalPages).toEqual(5);
          expect(response.totalElements).toEqual(42);
        });

      const req = httpTestingController.expectOne(request => {
        return (
          request.url ===
            `${mockBaseUrl}/api/v1/merchant-rewards/campaign/page` &&
          request.params.has('pageNum') &&
          request.params.get('pageNum') === '0' &&
          request.params.get('pageSize') === '10'
        );
      });
      expect(req.request.method).toEqual('GET');
      req.flush(mockResponse);
    });
  });

  describe('createOne', () => {
    it('should make POST request with correct body', () => {
      const campaignData = {
        name: 'Test Campaign',
        startDate: '01/01/2025' as RawDateDayFirst,
        endDate: '31/01/2025' as RawDateDayFirst,
        termsConditions: 'https://cdn.aplazo.mx/reglas/enero25.pdf',
        numberOfWinners: 2,
        prizes: [
          {
            fromRank: 1,
            toRank: 1,
            prizeName: 'laptopUpdated',
          },
          {
            fromRank: 2,
            toRank: 2,
            prizeName: 'teleUpdated4',
          },
        ],
      };

      service.createOne(campaignData).subscribe();

      const req = httpTestingController.expectOne(
        `${mockBaseUrl}/api/v1/merchant-rewards/campaign`
      );
      expect(req.request.method).toEqual('POST');
      expect(req.request.body).toEqual(campaignData);
      req.flush({});
    });
  });

  describe('updateOne', () => {
    it('should make PUT request with correct URL and body', () => {
      const campaignId = 123;
      const campaignData = {
        id: campaignId,
        name: 'Updated Campaign',
        startDate: '01/01/2025' as RawDateDayFirst,
        endDate: '31/01/2025' as RawDateDayFirst,
        termsConditions: 'Updated terms',
        numberOfWinners: 2,
        prizes: [
          {
            fromRank: 1,
            toRank: 1,
            prizeName: 'laptopUpdated',
          },
          {
            fromRank: 2,
            toRank: 2,
            prizeName: 'teleUpdated4',
          },
        ],
      };

      service.updateOne(campaignData).subscribe();

      const req = httpTestingController.expectOne(
        `${mockBaseUrl}/api/v1/merchant-rewards/campaign/${campaignId}`
      );
      expect(req.request.method).toEqual('PUT');
      expect(req.request.body).toEqual({
        name: campaignData.name,
        startDate: campaignData.startDate,
        endDate: campaignData.endDate,
        termsConditions: campaignData.termsConditions,
        numberOfWinners: campaignData.numberOfWinners,
        prizes: campaignData.prizes,
      });
      req.flush({});
    });
  });

  describe('findCampaignWinners', () => {
    it('should make GET request with correct URL', () => {
      const campaignId = 123;
      const mockResponse = [
        {
          id: 500,
          position: 1,
          participantName: 'mario gonzales gonzales',
          participantId: 'PART-500',
          phoneNumber: '+52 55 9876 5432',
          usersRegistered: 1,
          address: 'calle siempre viva, springfield',
          prize: '',
          prizeReceived: false,
        },
      ];

      service.getWinnersBy({ campaignId }).subscribe(response => {
        expect(response.length).toEqual(1);
        expect(response).toEqual(mockResponse as CampaignWinner[]);
      });

      const req = httpTestingController.expectOne(
        `${mockBaseUrl}/api/v1/merchant-rewards/campaigns/${campaignId}/winners`
      );
      expect(req.request.method).toEqual('GET');
      req.flush(mockResponse);
    });
  });

  describe('confirmPrizeReceived', () => {
    it('should make PATCH request with correct URL and body', () => {
      const args = {
        campaignId: 123,
        winnerId: 456,
        prizeReceived: true,
      };

      service.confirmPrizeReceived(args).subscribe();

      const req = httpTestingController.expectOne(
        `${mockBaseUrl}/api/v1/merchant-rewards/campaigns/${args.campaignId}/confirm/${args.winnerId}`
      );
      expect(req.request.method).toEqual('PATCH');
      expect(req.request.body).toEqual({
        prizeReceived: args.prizeReceived,
      });
      req.flush({});
    });
  });
});
