import { AsyncPipe } from '@angular/common';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoCommonMessageComponent,
  AplazoCommonMessageComponents,
} from '@aplazo/shared-ui/merchant';
import { of } from 'rxjs';
import { ConfirmPrizeReceivedUsecase } from '../../../../../app/features/campaigns/application/use_cases/confirm-prize-received.usecase';
import { GetLastCampaignsUseCase } from '../../../../../app/features/campaigns/application/use_cases/get-last-campaings.usecase';
import { GetWinnersByCampaign } from '../../../../../app/features/campaigns/application/use_cases/get-winners.usecase';
import { CampaignWinner } from '../../../../../app/features/campaigns/domain/entities';
import { CampaignWinnersTableComponent } from '../../../../../app/features/campaigns/infra/components/aplazo-winners-table/aplazo-winners-table.component';
import { ListingCampaignsWinnersComponent } from '../../../../../app/features/campaigns/infra/pages/listing-campaign-winners/listing-campaign-winners.component';

const setup = (args?: {
  campaigns?: { id: number; name: string }[];
  winners?: Partial<CampaignWinner>[];
}) => {
  const defaultConfig = {
    campaigns: [] as { id: number; name: string }[],
    winners: [] as Partial<CampaignWinner>[],
  };

  const config = {
    campaigns: args?.campaigns ?? defaultConfig.campaigns,
    winners: args?.winners ?? defaultConfig.winners,
  };

  TestBed.configureTestingModule({
    imports: [
      AplazoCommonMessageComponents,
      AplazoCardComponent,
      CampaignWinnersTableComponent,
      AplazoFormFieldDirectives,
      ReactiveFormsModule,
      AsyncPipe,
      AplazoDynamicPipe,
    ],
    providers: [
      {
        provide: GetWinnersByCampaign,
        useValue: {
          execute: () => of(config.winners),
        },
      },
      {
        provide: GetLastCampaignsUseCase,
        useValue: {
          execute: () => of(config.campaigns),
        },
      },
      {
        provide: ConfirmPrizeReceivedUsecase,
        useValue: {
          execute: () => of(void 0),
        },
      },
    ],
  });

  const fixture = TestBed.createComponent(ListingCampaignsWinnersComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  const getCampaignsSpy = spyOn(
    TestBed.inject(GetLastCampaignsUseCase),
    'execute'
  ).and.callThrough();
  const winnersSpy = spyOn(
    TestBed.inject(GetWinnersByCampaign),
    'execute'
  ).and.callThrough();
  const confirmPrizeSpy = spyOn(
    TestBed.inject(ConfirmPrizeReceivedUsecase),
    'execute'
  ).and.callThrough();

  return { fixture, component, getCampaignsSpy, winnersSpy, confirmPrizeSpy };
};

describe('ListingCampaignsWinnersComponent', () => {
  const mockWinners: CampaignWinner[] = [
    {
      id: 500,
      position: 1,
      participantName: 'mario gonzales gonzales',
      usersRegistered: 1,
      address: 'calle siempre viva, springfield',
      prize: '',
      prizeReceived: false,
    },
  ];

  const mockCampaigns = [
    { id: 1, name: 'Campaign 1' },
    { id: 2, name: 'Campaign 2' },
  ];

  it('should create', () => {
    const { component } = setup();

    expect(component).toBeTruthy();
  });

  it('should call GetWinners on initialization', fakeAsync(() => {
    const { winnersSpy, component, fixture } = setup({
      campaigns: mockCampaigns,
      winners: mockWinners,
    });

    component.ngOnInit();

    fixture.detectChanges();
    flush();

    expect(winnersSpy).toHaveBeenCalled();
  }));

  it('should load campaigns and select the first one by default', fakeAsync(() => {
    const { component } = setup({
      campaigns: mockCampaigns,
      winners: mockWinners,
    });

    let campaigns: any[] = [];

    component.campaigns$.subscribe(data => {
      campaigns = data;
    });

    flush();

    expect(campaigns).toEqual(mockCampaigns);

    expect(component.selectedCampaignControl.value).toBe(campaigns[0].id);
  }));

  it('should execute GetWinnerUsecase when campaign selection changes', fakeAsync(() => {
    const { component, fixture, winnersSpy } = setup({
      winners: mockWinners,
      campaigns: mockCampaigns,
    });

    component.ngOnInit();
    flush();
    fixture.detectChanges();

    expect(winnersSpy).toHaveBeenCalledTimes(1);

    component.selectedCampaignControl.setValue(mockCampaigns[1].id);
    flush();
    fixture.detectChanges();

    expect(winnersSpy).toHaveBeenCalledTimes(2);
  }));

  it('should update lastUpdateDate', fakeAsync(() => {
    const { component, fixture } = setup({
      winners: mockWinners,
      campaigns: mockCampaigns,
    });

    const date = new Date();
    const hour = date.toTimeString().split(':').slice(0, 2).join(':');

    component.ngOnInit();
    flush();
    fixture.detectChanges();

    const lastUpdateDateEle = fixture.debugElement.query(
      By.css('[data-testid="last-update-date"]')
    );

    expect(
      lastUpdateDateEle.nativeElement.textContent.trim()?.endsWith(hour)
    ).toBeTrue();
  }));

  it('should render empty message component with correct title when no winners are found ', fakeAsync(() => {
    const { component, fixture } = setup({
      winners: [],
      campaigns: mockCampaigns,
    });

    component.ngOnInit();
    flush();
    fixture.detectChanges();

    const emptyMessageEle = fixture.debugElement.query(
      By.directive(AplazoCommonMessageComponent)
    );

    expect(emptyMessageEle.componentInstance.text?.title).toBe(
      'No hay ganadores para mostrar'
    );
  }));

  // it('should call confirmPrizeReceived when prize status changes', fakeAsync(() => {
  //   const prizeEvent = {
  //     winnerId: 500,
  //     prizeReceived: true,
  //   };

  //   spyOn(component, 'refreshWinners');

  //   component.onPrizeReceivedChange(prizeEvent);
  //   flush();

  //   expect(confirmPrizeReceivedUsecase.execute).toHaveBeenCalledWith({
  //     campaignId: component.campaign,
  //     winnerId: prizeEvent.winnerId,
  //     prizeReceived: prizeEvent.prizeReceived,
  //   });

  //   expect(component.refreshWinners).toHaveBeenCalled();
  // }));

  // it('should refresh winners when prize confirmation fails', fakeAsync(() => {
  //   const prizeEvent = {
  //     winnerId: 500,
  //     prizeReceived: true,
  //   };

  //   confirmPrizeReceivedUsecase.execute.and.returnValue(
  //     throwError(() => new Error('Test error'))
  //   );

  //   spyOn(component, 'refreshWinners');

  //   component.onPrizeReceivedChange(prizeEvent);
  //   flush();

  //   expect(component.refreshWinners).toHaveBeenCalled();
  // }));
});
