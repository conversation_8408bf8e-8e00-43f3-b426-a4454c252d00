/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
  flush,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import {
  LoaderService,
  NotifierService,
  TemporalService,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { DialogRef, DialogService } from '@ngneat/dialog';
import { of } from 'rxjs';
import { FetchAPremiosAplazoUsecase } from '../../../../../app/features/campaigns/application/use_cases/fetch-premios-aplazo.usecase';
import { PremiosAplazoCampaign } from '../../../../../app/features/campaigns/domain/entities';
import { CampaignFormComponent } from '../../../../../app/features/campaigns/infra/components/campaign-form.component';
import { ListingCampaignsComponent } from '../../../../../app/features/campaigns/infra/pages/listing-campaigns/listing-campaigns.component';

describe('ListingCampaignsComponent', () => {
  let component: ListingCampaignsComponent;
  let fixture: ComponentFixture<ListingCampaignsComponent>;
  let fetchCampaignsSpy: jasmine.Spy;
  let dialogService: jasmine.SpyObj<DialogService>;
  let router: jasmine.SpyObj<Router>;
  let temporalService: jasmine.SpyObj<TemporalService>;

  const mockCampaigns: PremiosAplazoCampaign[] = [
    {
      id: 1,
      name: 'Test Campaign 1',
      startDate: '2025-01-01',
      endDate: '2025-01-15',
      active: true,
      createdBy: '<EMAIL>',
      createdAt: '2025-01-01T10:00:00',
      termsConditions: 'Test terms',
      numberOfWinners: 1,
    },
    {
      id: 2,
      name: 'Test Campaign 2',
      startDate: '2025-01-05',
      endDate: '2025-01-20',
      active: false,
      createdBy: '<EMAIL>',
      createdAt: '2025-01-02T10:00:00',
      termsConditions: null,
      numberOfWinners: 2,
    },
  ];

  const mockDialogRef = {
    afterClosed$: of({
      hasConfirmation: true,
      data: { name: 'Test Campaign', dateRange: [new Date(), new Date()] },
    }),
    close: jasmine.createSpy('close'),
  };

  beforeEach(() => {
    const dialogServiceSpy = jasmine.createSpyObj('DialogService', ['open']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const temporalServiceSpy = jasmine.createSpyObj('TemporalService', [
      'fromStringToDate',
    ]);
    const fetchAPremiosAplazoUsecaseSpy = jasmine.createSpyObj(
      'FetchAPremiosAplazoUsecase',
      ['execute']
    );

    TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        AplazoCardComponent,
        AplazoButtonComponent,
        AplazoPaginationComponent,
      ],
      providers: [
        { provide: DialogService, useValue: dialogServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: TemporalService, useValue: temporalServiceSpy },
        {
          provide: FetchAPremiosAplazoUsecase,
          useValue: fetchAPremiosAplazoUsecaseSpy,
        },
        provideNotifierTesting(),
        provideLoaderTesting(),
      ],
    });

    fixture = TestBed.createComponent(ListingCampaignsComponent);
    component = fixture.componentInstance;

    dialogService = TestBed.inject(
      DialogService
    ) as jasmine.SpyObj<DialogService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    temporalService = TestBed.inject(
      TemporalService
    ) as jasmine.SpyObj<TemporalService>;

    fetchCampaignsSpy = TestBed.inject(FetchAPremiosAplazoUsecase)
      .execute as jasmine.Spy;
    fetchCampaignsSpy.and.returnValue(
      of({
        content: mockCampaigns,
        totalPages: 2,
        totalElements: 12,
      })
    );

    dialogService.open.and.returnValue(mockDialogRef as unknown as DialogRef);

    temporalService.fromStringToDate.and.callFake(dateStr => new Date(dateStr));

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should fetch campaigns on init', fakeAsync(() => {
    // Component is created in beforeEach
    tick(); // Allow async operations to complete

    expect(fetchCampaignsSpy).toHaveBeenCalled();

    // Get the most recent call arguments
    const args = fetchCampaignsSpy.calls.mostRecent().args[0];

    // Verify arguments structure
    expect(args).toBeDefined();
    expect(args.pageNum).toBeDefined();
  }));

  it('should change page', () => {
    const nextPageSpy = spyOn(component.currentPage$, 'next');

    component.changePage(1);

    expect(nextPageSpy).toHaveBeenCalledWith(1);
  });

  it('should open dialog to create campaign', fakeAsync(() => {
    component.createOneCampaign();
    tick();

    expect(dialogService.open).toHaveBeenCalledWith(CampaignFormComponent, {
      enableClose: false,
    });
    expect(router.navigate).toHaveBeenCalled();
  }));

  it('should open dialog to edit campaign', fakeAsync(() => {
    const campaign = mockCampaigns[0];

    component.editOneCampaign(campaign);
    tick();

    expect(dialogService.open).toHaveBeenCalledWith(CampaignFormComponent, {
      enableClose: false,
      data: jasmine.objectContaining({
        id: campaign.id,
        name: campaign.name,
      }),
    });
  }));
});
