import { Component } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoCommonMessageComponents } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { PremiosAplazoCampaign } from '../../../../../../app/features/campaigns/domain/entities';
import { AplazoRewardsTableComponent } from '../../../../../../app/features/campaigns/infra/components/aplazo-rewards-table/aplazo-rewards-table.component';

@Component({
  selector: 'app-test',
  template: `
    <app-aplazo-rewards-table [data]="data"></app-aplazo-rewards-table>
  `,
  imports: [AplazoRewardsTableComponent],
})
class TestContainerComponent {
  data: PremiosAplazoCampaign[] = [];
}

describe('AplazoRewardsTableComponent', () => {
  let fixture: ComponentFixture<TestContainerComponent>;
  let component: TestContainerComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TestContainerComponent,
        AplazoRewardsTableComponent,
        AplazoCardComponent,
        AplazoButtonComponent,
        AplazoSimpleTableComponents,
        AplazoCommonMessageComponents,
        AplazoDynamicPipe,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TestContainerComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have empty data input by default', () => {
    const table = fixture.debugElement.query(
      By.directive(AplazoRewardsTableComponent)
    );

    expect(table.componentInstance.data()).toEqual([]);
  });

  it('should render campaign data in table', () => {
    const campaignData: PremiosAplazoCampaign[] = [
      {
        id: 1,
        name: 'Campaign 1',
        startDate: '2025-03-10',
        endDate: '2025-03-12',
        active: true,
        createdAt: new Date().toISOString(),
        createdBy: 'User 1',
        termsConditions: null,
        numberOfWinners: 1,
      },
      {
        id: 2,
        name: 'Campaign 1',
        startDate: '2025-03-10',
        endDate: '2025-03-12',
        active: true,
        createdAt: new Date().toISOString(),
        createdBy: 'User 1',
        termsConditions: 'Terms and Conditions',
        numberOfWinners: 1,
      },
    ];

    component.data = campaignData;

    fixture.detectChanges();

    const table = fixture.debugElement.queryAll(By.css('table tbody tr'));

    expect(table.length).toBe(campaignData.length);
  });
});
