import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { OnlyNumbersDirective } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';

import {
  PrizeDistributionComponent,
  PrizeDistributionValue,
} from '../../../../../app/features/campaigns/infra/components/distribution/prize-distribution.component';

describe('PrizeDistributionComponent', () => {
  let component: PrizeDistributionComponent;
  let fixture: ComponentFixture<PrizeDistributionComponent>;
  let onChangeSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        AplazoFormFieldDirectives,
        AplazoButtonComponent,
        OnlyNumbersDirective,
        AplazoIconComponent,
        PrizeDistributionComponent,
      ],
      providers: [AplazoIconRegistryService],
    });

    fixture = TestBed.createComponent(PrizeDistributionComponent);
    component = fixture.componentInstance;
    onChangeSpy = spyOn(component, 'onChange').and.callThrough();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with empty form', () => {
    expect(component.winnersControl.value).toBeNull();
    expect(component.prizesControl.length).toBe(0);
  });

  it('should implement ControlValueAccessor interface', () => {
    expect(component.writeValue).toBeDefined();
    expect(component.registerOnChange).toBeDefined();
    expect(component.registerOnTouched).toBeDefined();
    expect(component.setDisabledState).toBeDefined();
  });

  it('should implement Validator interface', () => {
    expect(component.validate).toBeDefined();
    expect(component.registerOnValidatorChange).toBeDefined();
  });

  it('should set value correctly via writeValue', () => {
    const testValue: PrizeDistributionValue = {
      numberOfWinners: 2,
      prizes: [
        { fromRank: 1, toRank: 1, prizeName: 'First Prize' },
        { fromRank: 2, toRank: 2, prizeName: 'Second Prize' },
      ],
    };

    component.writeValue(testValue);
    fixture.detectChanges();

    expect(component.winnersControl.value).toBe(2);
    expect(component.prizesControl.length).toBe(2);
    expect(component.prizesControl.at(0).get('name')?.value).toBe(
      'First Prize'
    );
    expect(component.prizesControl.at(1).get('name')?.value).toBe(
      'Second Prize'
    );
  });

  it('should handle null value in writeValue', () => {
    component.writeValue(null);
    fixture.detectChanges();

    expect(component.winnersControl.value).toBeNull();
    expect(component.prizesControl.length).toBe(0);
  });

  it('should disable controls when setDisabledState is called with true', () => {
    component.setDisabledState(true);
    fixture.detectChanges();

    expect(component.form.disabled).toBeTrue();
  });

  it('should enable controls when setDisabledState is called with false', () => {
    component.setDisabledState(true);
    fixture.detectChanges();

    component.setDisabledState(false);
    fixture.detectChanges();

    expect(component.form.disabled).toBeFalse();
  });

  it('should validate and return errors when form is invalid', () => {
    component.winnersControl.setValue(null);
    component.form.markAllAsTouched();
    fixture.detectChanges();

    const errors = component.validate(component.form);

    expect(errors).not.toBeNull();
    expect(errors?.['prizeDistributionError']).toBeTrue();
  });

  it('should not allow adding prizes if no winners are set', () => {
    component.winnersControl.setValue(null);
    fixture.detectChanges();

    component.addPrize();
    fixture.detectChanges();

    expect(component.prizesControl.length).toBe(0);
  });
});
