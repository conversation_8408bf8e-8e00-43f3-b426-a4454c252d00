import { Component } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoCommonMessageComponents } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoSelectComponent } from '@aplazo/shared-ui/forms';
import { CampaignWinner } from '../../../../../../app/features/campaigns/domain/entities';
import { CampaignWinnersTableComponent } from '../../../../../../app/features/campaigns/infra/components/aplazo-winners-table/aplazo-winners-table.component';

@Component({
  selector: 'app-test',
  template: `
    <app-campaign-winners-table
      [data]="data"
      [isLoading]="isLoading"
      (prizeReceivedChange)="onPrizeReceivedChange($event)">
    </app-campaign-winners-table>
  `,
  imports: [CampaignWinnersTableComponent],
})
class TestContainerComponent {
  data: CampaignWinner[] = [];
  isLoading = false;
  prizeReceivedEvent: { winnerId: number; prizeReceived: boolean } | null =
    null;

  onPrizeReceivedChange(event: {
    winnerId: number;
    prizeReceived: boolean;
  }): void {
    this.prizeReceivedEvent = event;
  }
}

describe('CampaignWinnersTableComponent', () => {
  let fixture: ComponentFixture<TestContainerComponent>;
  let component: TestContainerComponent;

  const mockWinners: CampaignWinner[] = [
    {
      id: 1,
      position: 1,
      participantName: 'John Doe',
      usersRegistered: 10,
      address: 'Springfield, 123',
      prize: 'First Prize',
      prizeReceived: false,
    },
    {
      id: 2,
      position: 2,
      participantName: 'Jane Smith',
      usersRegistered: 5,
      address: 'Shelbyville, 456',
      prize: 'Second Prize',
      prizeReceived: true,
    },
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TestContainerComponent,
        CampaignWinnersTableComponent,
        AplazoDynamicPipe,
        AplazoButtonComponent,
        AplazoCardComponent,
        AplazoCommonMessageComponents,
        AplazoSimpleTableComponents,
        AplazoSelectComponent,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TestContainerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have empty data input by default', () => {
    const table = fixture.debugElement.query(
      By.directive(CampaignWinnersTableComponent)
    );

    expect(table.componentInstance.data()).toEqual([]);
  });

  it('should render winners data in table', () => {
    component.data = mockWinners;
    fixture.detectChanges();

    const tableRows = fixture.debugElement.queryAll(By.css('table tbody tr'));
    expect(tableRows.length).toBe(mockWinners.length);
  });

  it('should display empty message when no data is available', () => {
    component.data = [];
    fixture.detectChanges();

    const emptyMessage = fixture.debugElement.query(
      By.css('aplz-ui-common-message')
    );

    expect(emptyMessage).toBeTruthy();
  });

  it('should emit prizeReceivedChange event when checkbox is toggled', () => {
    component.data = mockWinners;
    fixture.detectChanges();

    // Get the first checkbox and trigger change event
    const checkbox = fixture.debugElement.query(
      By.css('input[type="checkbox"]')
    ).nativeElement;

    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));
    fixture.detectChanges();

    expect(component.prizeReceivedEvent).toBeTruthy();
    expect(component.prizeReceivedEvent?.winnerId).toBe(mockWinners[0].id);
    expect(component.prizeReceivedEvent?.prizeReceived).toBe(true);
  });

  it('should display checkboxes with correct initial state', () => {
    component.data = mockWinners;
    fixture.detectChanges();

    const checkboxes = fixture.debugElement.queryAll(
      By.css('input[type="checkbox"]')
    );

    expect(checkboxes.length).toBe(mockWinners.length);
    expect(checkboxes[0].nativeElement.checked).toBe(
      mockWinners[0].prizeReceived
    );
    expect(checkboxes[1].nativeElement.checked).toBe(
      mockWinners[1].prizeReceived
    );
  });
});
