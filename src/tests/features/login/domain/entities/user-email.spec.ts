import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import {
  invalidEmailErrorMessage,
  UserEmail,
} from '../../../../../app/features/login/domain/entities/user-email';

describe('UserEmail', () => {
  it('should create a new UserEmail instance', () => {
    const email = UserEmail.create('<EMAIL>');
    expect(email.value).toBe('<EMAIL>');
  });

  it('should throw an error for an invalid email', () => {
    expect(() => UserEmail.create('invalid-email')).toThrowError(
      RuntimeMerchantError
    );
  });

  it('should throw an error for a null email', () => {
    expect(() => UserEmail.create(null)).toThrowError(RuntimeMerchantError);
  });

  it('should throw an error for an undefined email', () => {
    expect(() => UserEmail.create(undefined)).toThrowError(
      RuntimeMerchantError
    );
  });

  it('should throw an error for an empty string email', () => {
    expect(() => UserEmail.create('')).toThrowError(RuntimeMerchantError);
  });

  it('should throw an error for a whitespace email', () => {
    expect(() => UserEmail.create(' ')).toThrowError(RuntimeMerchantError);
  });

  it('should throw an error with the correct error message', () => {
    expect(() => UserEmail.create('invalid-email')).toThrowError(
      invalidEmailErrorMessage
    );
  });
});
