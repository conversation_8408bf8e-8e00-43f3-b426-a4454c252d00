import {
  User,
  UserProps,
} from '../../../../../app/features/login/domain/entities/user';
import { UserEmail } from '../../../../../app/features/login/domain/entities/user-email';

describe('User Entity', () => {
  let userProps: UserProps;
  let user: User;

  beforeEach(() => {
    userProps = {
      id: '1',
      roles: ['Admin'],
      email: '<EMAIL>',
      accessToken: 'xyz',
      lastLogin: new Date(),
    };
    user = User.create(userProps);
  });

  it('should create a user with correct properties', () => {
    expect(user.id).toBe(userProps.id);
    expect(user.roles).toEqual(userProps.roles);
    expect(user.email).toBe(userProps.email);
  });

  it('should throw an error when creating a user without id', () => {
    userProps.id = '';
    expect(() => User.create(userProps)).toThrow();
  });

  it('should throw an error when creating a user without email', () => {
    userProps.email = '';
    expect(() => User.create(userProps)).toThrow();
  });

  it('should throw an error when creating a user without roles', () => {
    userProps.roles = [];
    expect(() => User.create(userProps)).toThrow();
  });

  it('should correctly store accessToken and update lastLogin when setAccessToken is called', () => {
    const newAccessToken = 'newToken';
    user.setAccessToken(newAccessToken);
    expect(user.accessToken).toBe(newAccessToken);
    expect(user.lastLogin).toBeDefined();
  });

  it('isLoggedIn should return true if accessToken is present and false otherwise', () => {
    expect(user.isLoggedIn).toBeTrue();
    user.setAccessToken('');
    expect(user.isLoggedIn).toBeFalse();
  });

  it('should allow email to be updated', () => {
    const newUserEmail = UserEmail.create('<EMAIL>');

    user.setEmail(newUserEmail);
    expect(user.email).toBe('<EMAIL>'.toLowerCase());
  });
});
