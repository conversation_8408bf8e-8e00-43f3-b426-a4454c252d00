import { HttpErrorResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import {
  LoginRequest,
  LoginResponse,
} from '../../../../../app/features/login/application/dtos/login.dto';
import { LoginRepository } from '../../../../../app/features/login/domain/login.repository';
import users from '../local-user.db.json';

export class LocalLoginRepository
  implements LoginRepository<LoginRequest, Observable<LoginResponse>>
{
  readonly #users = users;

  execute(params: LoginRequest): Observable<LoginResponse> {
    const user = this.#users.find(
      u => params.googleToken === u.account.username
    );

    if (!user) {
      throw new HttpErrorResponse({
        error: new Error('User not found'),
        status: 400,
        statusText: 'User not found',
      });
    }

    return of(user);
  }
}
