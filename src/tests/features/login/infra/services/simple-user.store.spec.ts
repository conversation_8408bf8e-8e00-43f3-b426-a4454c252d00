import { User } from '../../../../../app/features/login/domain/entities/user';
import { SimpleUserStore } from '../../../../../app/features/login/infra/services/simple-user.store';

describe('Simple User Store', () => {
  let store: SimpleUserStore;

  const testUser: Omit<User, 'setEmail' | 'setAccessToken'> = {
    email: '<EMAIL>',
    roles: ['USER'],
    lastLogin: new Date(),
    id: '1',
    isLoggedIn: true,
    accessToken: 'test',
  };

  beforeEach(() => {
    store = new SimpleUserStore();
  });

  it('should set user', done => {
    let result: any;

    store.user$.subscribe(user => {
      result = user;
    });

    store.setUser(testUser);

    expect(result.id).toBe(testUser.id);
    expect(result.email).toBe(testUser.email);
    expect(result.roles).toEqual(testUser.roles);
    expect(result.accessToken).toBe(testUser.accessToken);
    expect(result.isLoggedIn).toBe(testUser.isLoggedIn);

    done();
  });

  it('should clear user', done => {
    let result: any;

    store.user$.subscribe(user => {
      result = user;
    });

    store.setUser(testUser);

    expect(result.id).toBe(testUser.id);
    expect(result.email).toBe(testUser.email);

    store.clearUser();

    expect(result.id).toBe('');
    expect(result.email).toBe('');
    expect(result.roles).toEqual([]);
    expect(result.accessToken).toBe('');
    expect(result.isLoggedIn).toBe(false);

    done();
  });

  it('should return email', done => {
    let result: any;

    store.email$.subscribe(email => {
      result = email;
    });

    store.setUser(testUser);

    expect(result).toBe(testUser.email);

    done();
  });

  it('should return roles', done => {
    let result: any;

    store.roles$.subscribe(roles => {
      result = roles;
    });

    store.setUser(testUser);

    expect(result).toEqual(testUser.roles);

    done();
  });

  it('should return isLoggedIn', done => {
    let result: any;

    store.isLoggedIn$.subscribe(isLoggedIn => {
      result = isLoggedIn;
    });

    store.setUser(testUser);

    expect(result).toBe(testUser.isLoggedIn);

    done();
  });

  it('should return accessToken', done => {
    let result: any;

    store.tokenSession$.subscribe(accessToken => {
      result = accessToken;
    });

    store.setUser(testUser);

    expect(result).toBe(testUser.accessToken);

    done();
  });
});
