import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';
import { LoginWithHttpRepository } from '../../../../../app/features/login/infra/repositories/login-with-http.repository';

describe('Login With Http Repository', () => {
  let httpTestingController: HttpTestingController;
  let service: LoginWithHttpRepository;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            googleClientId: 'googleId',
            apiBaseUrl: 'http://localhost:3000',
            bifrostUrl: 'http://localhost:3000',
            i18nUrl: 'http://localhost:3000',
            landingUrl: 'http://localhost:3000',
          },
        },
        LoginWithHttpRepository,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(LoginWithHttpRepository);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(LoginWithHttpRepository);
  });

  it('should retrieve the Aplazo Login Response', () => {
    const expectedResponse = {
      account: {
        id: 'testId',
        username: 'test1',
        roles: ['user'],
      },
      authorization: 'token',
      menu: [],
    };

    service
      .execute({
        googleToken: 'test',
      })
      .subscribe({
        next: response => {
          expect(response).toEqual(expectedResponse);
          expect(spyHttp).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/account/login'
    );
    expect(req.request.method).toEqual('POST');

    req.flush(expectedResponse);
  });

  it('should throws an error when the server returns an error', () => {
    const errorMsg = 'Deliberate 404 error';

    service
      .execute({
        googleToken: 'test',
      })
      .subscribe({
        next: fail,
        error: err => {
          expect(err).toBeInstanceOf(HttpErrorResponse);
          expect(err.error).toBe('Deliberate 404 error');
          expect(err.status).toBe(404);
          expect(spyHttp).toHaveBeenCalledTimes(1);
        },
      });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/account/login'
    );
    expect(req.request.method).toEqual('POST');

    req.flush(errorMsg, { status: 404, statusText: 'Not Found' });
  });
});
