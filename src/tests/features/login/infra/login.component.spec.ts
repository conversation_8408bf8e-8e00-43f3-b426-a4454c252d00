import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NgZone } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { BaseSSOProvider } from '@aplazo/front-social-sso';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { provideScriptRenderer } from '@aplazo/shared-ui/script-renderer';
import { StatsigService } from '@statsig/angular-bindings';
import { of } from 'rxjs';
import { SingleSSOResponse } from '../../../../app/features/login/application/dtos/single-sso-response.dto';
import { LoginUsecase } from '../../../../app/features/login/application/usecases/login.usecase';
import { User } from '../../../../app/features/login/domain/entities/user';
import { LoginComponent } from '../../../../app/features/login/infra/pages/login.component';

const setup = async (args?: { loginResponse?: Partial<User> }) => {
  const defaultConfig = {
    loginResponse: {
      id: '123',
      accessToken: 'token',
      email: '<EMAIL>',
      isLoggedIn: true,
      lastLogin: new Date('2025-05-01'),
      roles: ['admin', 'user'],
    } satisfies Partial<User>,
  };

  const config = {
    loginResponse:
      args && 'loginResponse' in args
        ? args.loginResponse
        : defaultConfig.loginResponse,
  };

  TestBed.configureTestingModule({
    imports: [LoginComponent, AplazoLogoComponent],
    providers: [
      {
        provide: LoginUsecase,
        useValue: {
          execute: () => of(config.loginResponse),
        },
      },
      {
        provide: StatsigService,
        useValue: {
          updateUserAsync: () => {},
          logEvent: () => {},
          getClient: () => {
            () => {};
          },
        },
      },
      {
        provide: BaseSSOProvider,
        useValue: {
          init: async () => {
            return void 0;
          },
        },
      },
      provideScriptRenderer(),
      provideRouter([{ path: '**', component: LoginComponent }]),
      provideHttpClient(),
      provideHttpClientTesting(),
    ],
  });

  const routerHarness = await RouterTestingHarness.create();
  const component = await routerHarness.navigateByUrl('/', LoginComponent);
  const fixture = routerHarness.fixture as ComponentFixture<any>;

  const sso = TestBed.inject(BaseSSOProvider);
  const usecase = TestBed.inject(LoginUsecase);
  const ssoSpy = spyOn(sso, 'init').and.resolveTo(void 0);
  const usecaseSpy = spyOn(usecase, 'execute').and.callThrough();

  return { component, fixture, sso, usecaseSpy, ssoSpy };
};

describe('Login Component', () => {
  it('should create the component', async () => {
    const { component } = await setup();

    expect(component).toBeTruthy();
  });

  it('should call the sso init method', async () => {
    const { component, ssoSpy } = await setup();

    component.ngAfterViewInit();

    expect(ssoSpy).toHaveBeenCalledTimes(1);
  });

  it('should contain the logo component', async () => {
    const { fixture } = await setup();

    expect(fixture?.nativeElement.querySelector('aplz-ui-logo')).toBeTruthy();
  });

  it('should contain the google GSI container', async () => {
    const { fixture } = await setup();

    const container = fixture.nativeElement.querySelector(
      'app-login article div'
    ) as HTMLElement;

    expect(container).toBeTruthy();
  });

  it('should call ngZone run method', async () => {
    const { component } = await setup();

    const zone = TestBed.inject(NgZone);
    const zoneSpy = spyOn(zone, 'run').and.callThrough();

    const testSSOResp: SingleSSOResponse = {
      credential: 'test credential',
      client_id: 'test client id',
      clientId: 'test client id',
      select_by: 'auto',
    };

    component.onSuccessSSO(testSSOResp);

    expect(zoneSpy).toHaveBeenCalledTimes(1);
  });

  it('should call loginUsecase execute method', async () => {
    const { component, usecaseSpy } = await setup();

    const testSSOResp: SingleSSOResponse = {
      credential: 'test credential',
      client_id: 'test client_id',
      clientId: 'test client id',
      select_by: 'auto',
    };

    component.onSuccessSSO(testSSOResp);

    expect(usecaseSpy).toHaveBeenCalledTimes(1);
    expect(usecaseSpy).toHaveBeenCalledWith(testSSOResp.credential);
  });
});
