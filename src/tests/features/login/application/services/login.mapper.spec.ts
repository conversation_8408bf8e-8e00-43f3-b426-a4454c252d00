import { UserStorePersistanceDto } from '../../../../../app/features/login/application/dtos/user-store-persistance.dto';
import {
  fromDomainToPersistence,
  fromPersistenceToDomain,
  fromTokenToRepositoryRequest,
} from '../../../../../app/features/login/application/services/login.mapper';
import { User } from '../../../../../app/features/login/domain/entities/user';
import { UserEmail } from '../../../../../app/features/login/domain/entities/user-email';

describe('fromResponseToPersistence test suite', () => {
  it('should correctly persist the user data from the response', () => {
    const user = User.create({
      id: '1',
      email: UserEmail.create('<EMAIL>').value,
      roles: ['USER'],
      lastLogin: new Date(),
    });

    const persistence = fromDomainToPersistence(user);

    expect(persistence.id).toEqual(user.id);
    expect(persistence.email).toEqual(user.email);
    expect(persistence.roles).toEqual(user.roles);
    expect(persistence.accessToken).toEqual(user.accessToken);
    expect(persistence.isLoggedIn).toEqual(user.isLoggedIn);
    expect(persistence.lastLogin.getTime()).toBeLessThanOrEqual(
      user.lastLogin.getTime()
    );
  });

  it('should not persist the setEmail and setAccessToken methods', () => {
    const user = User.create({
      id: '1',
      email: UserEmail.create('<EMAIL>').value,
      roles: ['USER'],
      lastLogin: new Date(),
    });
    const persistence = fromDomainToPersistence(user);

    expect((persistence as any).setEmail).toBeUndefined();
    expect((persistence as any).setAccessToken).toBeUndefined();
  });

  it('should serialize from persistence dto to domain', () => {
    const persistence: UserStorePersistanceDto = {
      accessToken: 'test',
      email: '<EMAIL>',
      id: '1',
      isLoggedIn: true,
      lastLogin: new Date(),
      roles: ['USER'],
    };

    const user = fromPersistenceToDomain(persistence);

    expect(user.id).toEqual(persistence.id);
    expect(user.email).toEqual(UserEmail.create(persistence.email).value);
    expect(user.roles).toEqual(persistence.roles);
    expect(user.accessToken).toEqual(persistence.accessToken);
    expect(user.isLoggedIn).toEqual(persistence.isLoggedIn);
    expect(persistence.lastLogin.getTime()).toBeLessThanOrEqual(
      user.lastLogin.getTime()
    );
  });

  it('should serialize from token to repository request', () => {
    const token = 'test';
    const req = fromTokenToRepositoryRequest(token);

    expect(req.googleToken).toEqual(token);
  });

  it('should throw error if token is null', () => {
    const token = null;

    // @ts-expect-error: testing purposes
    expect(() => fromTokenToRepositoryRequest(token)).toThrowError();
  });

  it('should throw error if token is empty', () => {
    const token = '';

    expect(() => fromTokenToRepositoryRequest(token)).toThrowError();
  });
});
