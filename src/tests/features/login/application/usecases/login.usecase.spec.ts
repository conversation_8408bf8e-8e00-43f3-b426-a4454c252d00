import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import { provideLoaderTesting } from '@aplazo/merchant/shared-testing';
import { isEmpty, lastValueFrom, Observable, of } from 'rxjs';
import {
  LoginRequest,
  LoginResponse,
} from '../../../../../app/features/login/application/dtos/login.dto';
import { UserStore } from '../../../../../app/features/login/application/services/user.store';
import { LoginUsecase } from '../../../../../app/features/login/application/usecases/login.usecase';
import { VALID_SHIELD_ROLES } from '../../../../../app/features/login/domain/entities/valid-roles';
import { LoginRepository } from '../../../../../app/features/login/domain/login.repository';
import { MenuStore } from '../../../../../app/features/shared/application/services/menu.store';
import { provideMenuStore } from '../../../../../app/features/shared/infra/config/providers';
import { LocalUsecaseErrorHandler } from '../../../shared/infra/local-usecase-error-handler';
import { LocalLoginRepository } from '../../infra/services/local-login.repository';

describe('Login Usecase', () => {
  let usecase: LoginUsecase;
  let repository: LoginRepository<LoginRequest, Observable<LoginResponse>>;
  let loader: LoaderService;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let repoExecuteSpy: jasmine.Spy;
  let userStoreSpy: jasmine.SpyObj<UserStore>;
  let handleErrorSpy: jasmine.Spy;
  let setMenuSpy: jasmine.Spy;

  beforeEach(() => {
    const mockUser = jasmine.createSpyObj('UserStore', ['setUser']);

    TestBed.configureTestingModule({
      providers: [
        {
          provide: LoginRepository,
          useClass: LocalLoginRepository,
        },
        provideLoaderTesting(),
        {
          provide: UserStore,
          useValue: mockUser,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        provideMenuStore(),
        LoginUsecase,
      ],
    });

    usecase = TestBed.inject(LoginUsecase);
    repository = TestBed.inject(LoginRepository);
    loader = TestBed.inject(LoaderService);

    showLoaderSpy = spyOn(loader, 'show');
    hideLoaderSpy = spyOn(loader, 'hide');
    repoExecuteSpy = spyOn(repository, 'execute');
    handleErrorSpy = spyOn(TestBed.inject(UseCaseErrorHandler), 'handle');
    userStoreSpy = TestBed.inject(UserStore) as jasmine.SpyObj<UserStore>;
    setMenuSpy = spyOn(TestBed.inject(MenuStore), 'setMenu').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(LoginUsecase);
  });

  it('should retrieve user', async () => {
    repoExecuteSpy.and.callThrough();

    userStoreSpy.setUser.and.returnValue(undefined);
    const testToken = '<EMAIL>';

    const user = await lastValueFrom(usecase.execute(testToken));

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repoExecuteSpy).toHaveBeenCalledTimes(1);
    expect(userStoreSpy.setUser).toHaveBeenCalledTimes(1);
    expect(setMenuSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);

    expect(user.email).toBe('<EMAIL>');
  });

  it('should complete without emission when token is null', async () => {
    repoExecuteSpy.and.callThrough();
    handleErrorSpy.and.callThrough();
    const testInvalidToken = null;

    const isCompleteWithoutEmission = await lastValueFrom(
      // @ts-expect-error: test invalid token
      usecase.execute(testInvalidToken).pipe(isEmpty())
    );

    expect(isCompleteWithoutEmission).toBeTrue();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(handleErrorSpy).toHaveBeenCalledTimes(1);

    expect(repoExecuteSpy).toHaveBeenCalledTimes(0);
    expect(userStoreSpy.setUser).toHaveBeenCalledTimes(0);
  });

  it('should complete without emission when token is undefined', async () => {
    repoExecuteSpy.and.callThrough();
    handleErrorSpy.and.callThrough();
    const testInvalidToken = undefined;

    const isCompleteWithoutEmission = await lastValueFrom(
      // @ts-expect-error: test invalid token
      usecase.execute(testInvalidToken).pipe(isEmpty())
    );

    expect(isCompleteWithoutEmission).toBeTrue();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(handleErrorSpy).toHaveBeenCalledTimes(1);

    expect(repoExecuteSpy).toHaveBeenCalledTimes(0);
    expect(userStoreSpy.setUser).toHaveBeenCalledTimes(0);
  });

  it('should complete without emission when token is empty', async () => {
    repoExecuteSpy.and.callThrough();
    handleErrorSpy.and.callThrough();
    const testInvalidToken = '';

    const isCompleteWithoutEmission = await lastValueFrom(
      usecase.execute(testInvalidToken).pipe(isEmpty())
    );

    expect(isCompleteWithoutEmission).toBeTrue();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(handleErrorSpy).toHaveBeenCalledTimes(1);

    expect(repoExecuteSpy).toHaveBeenCalledTimes(0);
    expect(userStoreSpy.setUser).toHaveBeenCalledTimes(0);
  });

  it('should complete without emission when repository returns an error', async () => {
    repoExecuteSpy.and.throwError('error test');
    handleErrorSpy.and.callThrough();
    const testToken = '<EMAIL>';

    const isCompleteWithoutEmission = await lastValueFrom(
      usecase.execute(testToken).pipe(isEmpty())
    );

    expect(isCompleteWithoutEmission).toBeTrue();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(handleErrorSpy).toHaveBeenCalledTimes(1);

    expect(repoExecuteSpy).toHaveBeenCalledTimes(1);
    expect(userStoreSpy.setUser).toHaveBeenCalledTimes(0);
  });

  it('should complete without emission when repository response is invalid', async () => {
    repoExecuteSpy.and.returnValue(of({} as LoginResponse));
    handleErrorSpy.and.callThrough();
    const testToken = '<EMAIL>';

    const isCompleteWithoutEmission = await lastValueFrom(
      usecase.execute(testToken).pipe(isEmpty())
    );

    expect(isCompleteWithoutEmission).toBeTrue();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(handleErrorSpy).toHaveBeenCalledTimes(1);

    expect(repoExecuteSpy).toHaveBeenCalledTimes(1);
    expect(userStoreSpy.setUser).toHaveBeenCalledTimes(0);
  });

  it('should complete without emission when repository response does not have account', async () => {
    repoExecuteSpy.and.returnValue(
      of({ authorization: 'token' } as LoginResponse)
    );
    handleErrorSpy.and.callThrough();
    const testToken = '<EMAIL>';

    const isCompleteWithoutEmission = await lastValueFrom(
      usecase.execute(testToken).pipe(isEmpty())
    );

    expect(isCompleteWithoutEmission).toBeTrue();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(handleErrorSpy).toHaveBeenCalledTimes(1);

    expect(repoExecuteSpy).toHaveBeenCalledTimes(1);
    expect(userStoreSpy.setUser).toHaveBeenCalledTimes(0);
  });

  it('should complete without emission when repository response does not have valid roles', async () => {
    repoExecuteSpy.and.returnValue(
      of({
        authorization: 'token',
        account: { id: '1', username: 'username', roles: null },
      } as unknown as LoginResponse)
    );
    handleErrorSpy.and.callThrough();
    const testToken = '<EMAIL>';

    const isCompleteWithoutEmission = await lastValueFrom(
      usecase.execute(testToken).pipe(isEmpty())
    );

    expect(isCompleteWithoutEmission).toBeTrue();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(handleErrorSpy).toHaveBeenCalledTimes(1);

    expect(repoExecuteSpy).toHaveBeenCalledTimes(1);
    expect(userStoreSpy.setUser).toHaveBeenCalledTimes(0);
  });

  it('should complete without emission when repository response does not have any roles', async () => {
    repoExecuteSpy.and.returnValue(
      of({
        authorization: 'token',
        account: { id: '1', username: 'username', roles: [] },
      } as unknown as LoginResponse)
    );
    handleErrorSpy.and.callThrough();
    const testToken = '<EMAIL>';

    const isCompleteWithoutEmission = await lastValueFrom(
      usecase.execute(testToken).pipe(isEmpty())
    );

    expect(isCompleteWithoutEmission).toBeTrue();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(handleErrorSpy).toHaveBeenCalledTimes(1);

    expect(repoExecuteSpy).toHaveBeenCalledTimes(1);
  });

  it('should complete without emission when repository response does have generic role', async () => {
    repoExecuteSpy.and.returnValue(
      of({
        authorization: 'token',
        account: {
          id: '1',
          username: '<EMAIL>',
          roles: [VALID_SHIELD_ROLES.generic],
        },
      } as unknown as LoginResponse)
    );
    handleErrorSpy.and.callThrough();
    const testToken = '<EMAIL>';

    const isCompleteWithoutEmission = await lastValueFrom(
      usecase.execute(testToken).pipe(isEmpty())
    );

    expect(isCompleteWithoutEmission).toBeTrue();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(handleErrorSpy).toHaveBeenCalledTimes(1);

    expect(repoExecuteSpy).toHaveBeenCalledTimes(1);
    expect(userStoreSpy.setUser).toHaveBeenCalledTimes(0);
  });
});
