import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';
import {
  PaymentInvoicesRepositoryRequest,
  PaymentInvoiceSummaryResponse,
} from '../../../../../app/features/payments/domain/entities/invoices';
import { PaymentInvoicesRepository } from '../../../../../app/features/payments/domain/repositories/invoices.repository';
import { PaymentInvoicesWithHttp } from '../../../../../app/features/payments/infra/repositories/invoices.repository';

describe('PaymentsInvoicesWithHttp', () => {
  let httpTestingController: HttpTestingController;
  let repository: PaymentInvoicesRepository;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'http://localhost:3000',
          },
        },
        {
          provide: PaymentInvoicesRepository,
          useClass: PaymentInvoicesWithHttp,
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    repository = TestBed.inject(PaymentInvoicesRepository);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  const request: PaymentInvoicesRepositoryRequest = {
    start: '2024-03-27',
    end: '2024-03-27',
  };

  it('should be created', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(PaymentInvoicesWithHttp);
  });

  it('should retrieve success response', () => {
    const response: PaymentInvoiceSummaryResponse = {
      error: 0,
      excluded: 0,
      invoiced: 0,
      pending: 0,
      processing: 1,
    };

    repository.getSummary(request).subscribe(summary => {
      expect(summary).toEqual(response);
      expect(spyHttp).toHaveBeenCalledTimes(1);
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/invoice/range/status?start=2024-03-27&end=2024-03-27'
    );

    expect(spyHttp).toHaveBeenCalledTimes(1);

    expect(req.request.method).toEqual('GET');

    req.flush(response);
  });

  it('should throws an error when the repository throws an error', () => {
    const errorMsg = 'Deliberate 404 error';

    repository.getSummary(request).subscribe({
      next: fail,
      error: err => {
        expect(err).toBeInstanceOf(HttpErrorResponse);
        expect(err.error).toBe('Deliberate 404 error');
        expect(err.status).toBe(404);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/invoice/range/status?start=2024-03-27&end=2024-03-27'
    );

    expect(req.request.method).toEqual('GET');

    req.flush(errorMsg, { status: 404, statusText: 'Not Found' });
  });
});
