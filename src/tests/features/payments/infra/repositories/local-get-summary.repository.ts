import { RawDateYearFirstWithHyphen } from '@aplazo/merchant/shared';
import { Observable, of } from 'rxjs';
import { Payment } from '../../../../../app/features/payments/domain/entities/payment';
import { PaymentSummaryResponse } from '../../../../../app/features/payments/domain/entities/summary-response';
import { PaymentSummaryRepository } from '../../../../../app/features/payments/domain/repositories/payment-summary.repository';
import payments from '../loca-payments.db.json';

export class LocalGetSummaryRepository
  implements
    PaymentSummaryRepository<
      RawDateYearFirstWithHyphen,
      Observable<PaymentSummaryResponse>
    >
{
  readonly #payments = payments as Payment[];

  getByDate(
    date: RawDateYearFirstWithHyphen
  ): Observable<PaymentSummaryResponse> {
    const filtered = this.#payments.filter(itm => itm.paymentDate === date);

    const reduced = filtered.reduce(
      (acc, cur) => {
        acc.adjustmentMinus += cur.adjustmentMinus;
        acc.adjustmentPlus += cur.adjustmentPlus;
        acc.feeAmount += cur.feeAmount;
        acc.finalAmount += cur.finalAmount;
        acc.payAmount += cur.payAmount;
        acc.saleAmount += cur.saleAmount;

        return acc;
      },
      {
        saleAmount: 0,
        feeAmount: 0,
        adjustmentPlus: 0,
        adjustmentMinus: 0,
        finalAmount: 0,
        payAmount: 0,
      }
    );

    return of({
      paymentDate: date,
      saleAmount: +reduced.saleAmount.toFixed(2),
      feeAmount: +reduced.feeAmount.toFixed(2),
      adjustmentPlus: +reduced.adjustmentPlus.toFixed(2),
      adjustmentMinus: +reduced.adjustmentMinus.toFixed(2),
      finalAmount: +reduced.finalAmount.toFixed(2),
      payAmount: +reduced.payAmount.toFixed(2),
    });
  }
}
