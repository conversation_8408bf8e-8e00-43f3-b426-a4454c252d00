import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';
import {
  InvoiceGenerationRepositoryRequest,
  InvoiceGenerationResponse,
} from '../../../../../app/features/payments/domain/entities/payment';
import { PaymentsWithHttp } from '../../../../../app/features/payments/infra/repositories/payments.repository';

describe('PaymentsWithHttp', () => {
  let httpTestingController: HttpTestingController;
  let service: PaymentsWithHttp;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'http://localhost:3000',
          },
        },
        PaymentsWithHttp,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(PaymentsWithHttp);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  const request: InvoiceGenerationRepositoryRequest = {
    start: '2024-03-27T00:00:00.000Z',
    end: '2024-03-27T23:59:59.999Z',
  };

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(PaymentsWithHttp);
  });

  it('should generate invoices and retrieve the result', () => {
    const response: InvoiceGenerationResponse = {
      error: 2,
      excluded: 0,
      invoiced: 0,
      pending: 0,
      processing: 0,
    };

    service.generateInvoices(request).subscribe(summary => {
      expect(summary).toEqual(response);
      expect(spyHttp).toHaveBeenCalledTimes(1);
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/invoice/generate/range'
    );

    expect(spyHttp).toHaveBeenCalledTimes(1);

    expect(req.request.method).toEqual('POST');

    req.flush(response);
  });

  it('should throws an error when the repository throws an error', () => {
    const errorMsg = 'Deliberate 404 error';

    service.generateInvoices(request).subscribe({
      next: fail,
      error: err => {
        expect(err).toBeInstanceOf(HttpErrorResponse);
        expect(err.error).toBe('Deliberate 404 error');
        expect(err.status).toBe(404);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/invoice/generate/range'
    );

    expect(req.request.method).toEqual('POST');

    req.flush(errorMsg, { status: 404, statusText: 'Not Found' });
  });
});
