import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';
import { PaymentSummaryResponse } from '../../../../../app/features/payments/domain/entities/summary-response';
import { PaymentsSummaryWithHttpRepository } from '../../../../../app/features/payments/infra/repositories/summary-with-http.repository';

describe('Payments Summary With Http Repository', () => {
  let httpTestingController: HttpTestingController;
  let service: PaymentsSummaryWithHttpRepository;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'http://localhost:3000',
          },
        },
        PaymentsSummaryWithHttpRepository,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(PaymentsSummaryWithHttpRepository);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(PaymentsSummaryWithHttpRepository);
  });

  it('should retrieve the summary of payments', () => {
    const response: PaymentSummaryResponse = {
      paymentDate: '2024-03-27',
      saleAmount: 3070,
      feeAmount: 498.65,
      adjustmentPlus: 0,
      adjustmentMinus: 0,
      finalAmount: 2571.35,
      payAmount: 2571.35,
    };

    service.getByDate('2024-03-27').subscribe(summary => {
      expect(summary).toEqual(response);
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/finance/merchant-payment/data-summary/payment-date/2024-03-27'
    );

    expect(spyHttp).toHaveBeenCalledTimes(1);

    expect(req.request.method).toEqual('GET');

    req.flush(response);
  });

  it('should throws an error when the repository throws an error', () => {
    const errorMsg = 'Deliberate 404 error';

    service.getByDate('2024-03-27').subscribe({
      next: fail,
      error: err => {
        expect(err).toBeInstanceOf(HttpErrorResponse);
        expect(err.error).toBe('Deliberate 404 error');
        expect(err.status).toBe(404);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/finance/merchant-payment/data-summary/payment-date/2024-03-27'
    );

    expect(spyHttp).toHaveBeenCalledTimes(1);

    expect(req.request.method).toEqual('GET');

    req.flush(errorMsg, { status: 404, statusText: 'Not Found' });
  });
});
