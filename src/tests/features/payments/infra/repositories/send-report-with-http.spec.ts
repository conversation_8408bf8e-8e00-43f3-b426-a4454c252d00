import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';
import { PaymentReportRepositoryDto } from '../../../../../app/features/payments/application/dtos/report-request.dto';
import { PaymentsSendReportWithHttpRepository } from '../../../../../app/features/payments/infra/repositories/send-report-with-http.repository';

describe('Payments Send Report With Http Repository', () => {
  let httpTestingController: HttpTestingController;
  let service: PaymentsSendReportWithHttpRepository;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            bifrostUrl: 'http://localhost:3000',
          },
        },
        PaymentsSendReportWithHttpRepository,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(PaymentsSendReportWithHttpRepository);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(PaymentsSendReportWithHttpRepository);
  });

  it('should trigger the send report feature', () => {
    const request: PaymentReportRepositoryDto = {
      date: '12/03/2024',
      type: 'PAYMENT',
      emails: '<EMAIL>',
    };

    service.getReport(request).subscribe(res => {
      expect(res).toBeFalsy();
      expect(spyHttp).toHaveBeenCalledTimes(1);
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/merchant-payment/finance/send-mail?emails=<EMAIL>&type=PAYMENT&paymentDate=12/03/2024'
    );

    expect(req.request.method).toEqual('GET');

    req.flush(null);
  });

  it('should throws an error when the repository throws an error', () => {
    const errorMsg = 'Deliberate 404 error';

    const request: PaymentReportRepositoryDto = {
      date: '12/03/2024',
      type: 'PAYMENT',
      emails: '<EMAIL>',
    };

    service.getReport(request).subscribe({
      next: fail,
      error: err => {
        expect(err).toBeInstanceOf(HttpErrorResponse);
        expect(err.error).toBe('Deliberate 404 error');
        expect(err.status).toBe(404);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/merchant-payment/finance/send-mail?emails=<EMAIL>&type=PAYMENT&paymentDate=12/03/2024'
    );

    expect(req.request.method).toEqual('GET');

    req.flush(errorMsg, { status: 404, statusText: 'Not Found' });
  });
});
