import { Observable, of } from 'rxjs';
import { UploadReceiptsResponse } from '../../../../../app/features/payments/domain/entities/list-response';
import db from '../loca-receipts-history.db.json';
import { ReceiptListRepository } from '../../../../../app/features/payments/domain/repositories/receipt-list.repository';
import { ReceiptHistoryRequest } from '../../../../../app/features/payments/application/dtos/upload-receipts.dto';

export class LocalGetReceiptsRepository
  implements
    ReceiptListRepository<
      ReceiptHistoryRequest,
      Observable<UploadReceiptsResponse>
    >
{
  getList(args: ReceiptHistoryRequest): Observable<UploadReceiptsResponse> {
    return of(db as UploadReceiptsResponse);
  }
}
