import { Observable, of } from 'rxjs';
import { PaymentListRepositoryDto } from '../../../../../app/features/payments/application/dtos/list-request.dto';
import {
  PaymentInvoicesRepositoryRequest,
  PaymentInvoicesStatusResponse,
} from '../../../../../app/features/payments/domain/entities/invoices';
import { PaymentListResponse } from '../../../../../app/features/payments/domain/entities/list-response';
import { Payment } from '../../../../../app/features/payments/domain/entities/payment';
import { PaymentListRepository } from '../../../../../app/features/payments/domain/repositories/payment-list.repository';
import db from '../loca-payments.db.json';

export class LocalGetPaymentsRepository implements PaymentListRepository {
  readonly #list = db as Payment[];

  getList(args: PaymentListRepositoryDto): Observable<PaymentListResponse> {
    const date = args.date.split('/').reverse().join('-');

    const filteredByDate = this.#list.filter(itm => itm.paymentDate === date);

    const initialIndex = args.pageNum * args.pageSize;
    const finalIndex = initialIndex + args.pageSize;
    const totalPages = Math.ceil(filteredByDate.length / Number(args.pageSize));

    if (initialIndex > filteredByDate.length) {
      return of({
        content: [],
        number: Number(args.pageNum),
        size: Number(args.pageSize),
        totalElements: filteredByDate.length,
        totalPages,
        hasContent: filteredByDate.length > 0,
        numberOfElements: filteredByDate.length,
        first: Number(args.pageNum) === 0,
        last: Number(args.pageNum) === totalPages - 1,
      });
    }

    return of({
      content: filteredByDate.slice(initialIndex, finalIndex),
      number: Number(args.pageNum),
      size: Number(args.pageSize),
      totalElements: filteredByDate.length,
      totalPages,
      hasContent: filteredByDate.length > 0,
      numberOfElements: filteredByDate.length,
      first: Number(args.pageNum) === 0,
      last: Number(args.pageNum) === totalPages - 1,
    });
  }

  getInvoiceStatusList(
    daterange: PaymentInvoicesRepositoryRequest
  ): Observable<PaymentInvoicesStatusResponse[]> {
    console.log('getInvoiceStatusList', daterange);
    return of(db.map(i => i.invoice as any));
  }
}
