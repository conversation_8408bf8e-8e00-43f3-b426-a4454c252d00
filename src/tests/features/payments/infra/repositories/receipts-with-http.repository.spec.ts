import {
  HttpClient,
  HttpEventType,
  HttpHeaders,
  HttpResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';
import { ReceiptsWithHttpRepository } from '../../../../../app/features/payments/infra/repositories/receipts-with-http.repository';
import { ReceiptHistoryDto } from '../../../../../app/features/payments/application/dtos/upload-receipts.dto';

describe('ReceiptsWithHttpRepository', () => {
  let httpTestingController: HttpTestingController;
  let service: ReceiptsWithHttpRepository;
  let spyPostHttp: jasmine.Spy;
  let spyRequestHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'http://localhost:3000',
          },
        },
        ReceiptsWithHttpRepository,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(ReceiptsWithHttpRepository);
    const http = TestBed.inject(HttpClient);
    spyPostHttp = spyOn(http, 'post').and.callThrough();
    spyRequestHttp = spyOn(http, 'request').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(ReceiptsWithHttpRepository);
  });

  it('should upload a file', () => {
    const identifier = '123';
    const formData = new FormData();

    service.uploadFile(identifier, formData).subscribe({
      next: event => {
        expect(spyPostHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      `http://localhost:3000/api/v1/finance/merchant-payment/receipt/upload?identifier=${identifier}`
    );

    expect(req.request.method).toBe('POST');
    expect(req.request.params.get('identifier')).toBe(identifier);

    req.event({
      type: HttpEventType.UploadProgress,
      loaded: 70,
      total: 100,
    });
  });

  it('should retrieve events', () => {
    service.retrieveEvents().subscribe({
      next: event => {
        expect(spyRequestHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/shield/sse/v1/merchant-receipt-notify'
    );

    expect(req.request.method).toBe('GET');

    req.event({
      type: HttpEventType.Response,
      ok: true,
      body: 'data: {"message": "Hello, World!"}\n\n',
      status: 200,
      statusText: 'OK',
      url: 'http://localhost:3000/api/v1/shield/sse/v1/merchant-receipt-notify',
      headers: new HttpHeaders('content-type: text/event-stream'),
      clone: () => new HttpResponse(),
    });
  });

  it('should post receipt history', () => {
    const req: ReceiptHistoryDto = {
      fileName: 'filename',
      status: 'status',
      receiptsGenerated: 1,
      paymentDate: '2021-10-10',
    };

    service.postReceiptHistory(req).subscribe({
      next: event => {
        expect(spyPostHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const reqHttp = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/finance/merchant-payment/receipt-history'
    );

    expect(reqHttp.request.method).toBe('POST');
    expect(reqHttp.request.body).toEqual(req);

    reqHttp.flush({});
  });
});
