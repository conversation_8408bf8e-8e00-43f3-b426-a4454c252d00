import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';
import { PaymentListRepositoryDto } from '../../../../../app/features/payments/application/dtos/list-request.dto';
import { UploadReceiptsResponse } from '../../../../../app/features/payments/domain/entities/list-response';
import { ReceiptListWithHttpRepository } from '../../../../../app/features/payments/infra/repositories/receipt-list-http.repository';
import { ReceiptHistoryRequest } from '../../../../../app/features/payments/application/dtos/upload-receipts.dto';

describe('Payments List With Http Repository', () => {
  let httpTestingController: HttpTestingController;
  let service: ReceiptListWithHttpRepository;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'http://localhost:3000',
          },
        },
        ReceiptListWithHttpRepository,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(ReceiptListWithHttpRepository);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(ReceiptListWithHttpRepository);
  });

  it('should retrieve the list of payments', () => {
    const response: UploadReceiptsResponse = {
      content: [
        {
          id: 4,
          fileName: '06_JUN_2024_APLAZ_CSF.pdf',
          createdAt: '2024-09-23T19:18:37.156',
          status: 'ready',
          uploadedBy: '<EMAIL>',
          receiptsGenerated: 0,
          paymentDate: '2024-09-23',
        },
      ],
      number: 0,
      size: 20,
      totalElements: 1,
      totalPages: 1,
      hasContent: true,
      numberOfElements: 1,
      first: true,
      last: true,
    };
    const request: ReceiptHistoryRequest = {
      pageNum: 0,
      pageSize: 10,
    };

    service.getList(request).subscribe({
      next: res => {
        expect(res).toEqual(response);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/finance/merchant-payment/receipt-history/creation-date-after?pageNum=0&pageSize=10'
    );

    expect(req.request.method).toEqual('GET');

    req.flush(response);
  });

  it('should throws an error when the server returns an error', () => {
    const errorMsg = 'Deliberate 404 error';

    const request: PaymentListRepositoryDto = {
      date: '2024-03-27',
      pageNum: 0,
      pageSize: 10,
    };

    service.getList(request).subscribe({
      next: fail,
      error: err => {
        expect(err).toBeInstanceOf(HttpErrorResponse);
        expect(err.error).toBe('Deliberate 404 error');
        expect(err.status).toBe(404);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/finance/merchant-payment/receipt-history/creation-date-after?pageNum=0&pageSize=10'
    );

    expect(req.request.method).toEqual('GET');

    req.flush(errorMsg, { status: 404, statusText: 'Not Found' });
  });
});
