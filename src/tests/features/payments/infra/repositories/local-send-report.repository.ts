import { Observable, of } from 'rxjs';
import { PaymentReportRepositoryDto } from '../../../../../app/features/payments/application/dtos/report-request.dto';
import { PaymentReportRepository } from '../../../../../app/features/payments/domain/repositories/payment-report.repository';

export class LocalSendReportRepository
  implements
    PaymentReportRepository<PaymentReportRepositoryDto, Observable<void>>
{
  getReport(req: PaymentReportRepositoryDto): Observable<void> {
    console.log('sending payments report :::', req);

    return of(void 0);
  }
}
