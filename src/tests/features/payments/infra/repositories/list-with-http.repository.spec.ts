import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';
import { PaymentListRepositoryDto } from '../../../../../app/features/payments/application/dtos/list-request.dto';
import { PaymentListResponse } from '../../../../../app/features/payments/domain/entities/list-response';
import { PaymentsListWithHttpRepository } from '../../../../../app/features/payments/infra/repositories/list-with-http.repository';

describe('Payments List With Http Repository', () => {
  let httpTestingController: HttpTestingController;
  let service: PaymentsListWithHttpRepository;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'http://localhost:3000',
          },
        },
        PaymentsListWithHttpRepository,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(PaymentsListWithHttpRepository);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(PaymentsListWithHttpRepository);
  });

  it('should retrieve the list of payments', () => {
    const response: PaymentListResponse = {
      content: [
        {
          id: 21259,
          merchantId: 37,
          name: 'Casa Taller de Obsidiana ',
          saleAmount: 3070,
          feeAmount: 498.65,
          adjustmentMinus: 0,
          adjustmentPlus: 0,
          finalAmount: 2571.35,
          payAmount: 2571.35,
          paymentDate: '2024-03-27',
          paymentFrequency: 'Daily',
          status: 'PROCESS',
          loans: 41,
          adjustments: 0,
        },
      ],
      pageable: {
        pageNumber: 0,
        pageSize: 10,
        offset: 0,
        paged: true,
        unpaged: false,
        sort: {
          sorted: false,
          unsorted: true,
          empty: true,
        },
      },
      totalPages: 1,
      totalElements: 1,
      last: true,
      numberOfElements: 1,
      first: true,
      size: 10,
      number: 0,
    };
    const request: PaymentListRepositoryDto = {
      date: '2024-03-27',
      pageNum: 0,
      pageSize: 10,
    };

    service.getList(request).subscribe({
      next: res => {
        expect(res).toEqual(response);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/finance/merchant-payment/data-detail/payment-date/2024-03-27?pageNum=0&pageSize=10'
    );

    expect(req.request.method).toEqual('GET');

    req.flush(response);
  });

  it('should retrieve the list of payments with all optional parameters', () => {
    const response: PaymentListResponse = {
      content: [],
      pageable: {
        pageNumber: 0,
        pageSize: 10,
        offset: 0,
        paged: true,
        unpaged: false,
        sort: {
          sorted: false,
          unsorted: true,
          empty: true,
        },
      },
      totalPages: 1,
      totalElements: 0,
      last: true,
      numberOfElements: 0,
      first: true,
      size: 10,
      number: 0,
    };

    const request: PaymentListRepositoryDto = {
      date: '2024-03-27',
      pageNum: 0,
      pageSize: 10,
      frequency: 'daily',
      paymentStatus: 'process',
      invoiceStatus: 'pending',
      paymentId: 123,
      merchantId: 456,
      name: 'Test Merchant',
    };

    service.getList(request).subscribe({
      next: res => {
        expect(res).toEqual(response);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/finance/merchant-payment/data-detail/payment-date/2024-03-27?pageNum=0&pageSize=10&frequency=DAILY&status=PROCESS&invoiceStatus=PENDING&paymentId=123&merchantId=456&name=Test%20Merchant'
    );

    expect(req.request.method).toEqual('GET');
    req.flush(response);
  });

  it('should throws an error when the server returns an error', () => {
    const errorMsg = 'Deliberate 404 error';

    const request: PaymentListRepositoryDto = {
      date: '2024-03-27',
      pageNum: 0,
      pageSize: 10,
    };

    service.getList(request).subscribe({
      next: fail,
      error: err => {
        expect(err).toBeInstanceOf(HttpErrorResponse);
        expect(err.error).toBe('Deliberate 404 error');
        expect(err.status).toBe(404);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpTestingController.expectOne(
      'http://localhost:3000/api/v1/finance/merchant-payment/data-detail/payment-date/2024-03-27?pageNum=0&pageSize=10'
    );

    expect(req.request.method).toEqual('GET');

    req.flush(errorMsg, { status: 404, statusText: 'Not Found' });
  });
});
