import { ComponentFixture, TestBed } from '@angular/core/testing';
import { I18NService } from '@aplazo/i18n';
import { NotifierService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
} from '@aplazo/merchant/shared-testing';
import { of } from 'rxjs';
import { GetListReceiptUseCase } from '../../../../../app/features/payments/application/usecases/get-list-receipts.usecase';
import { UploadReceiptsUseCase } from '../../../../../app/features/payments/application/usecases/upload-receipts.usecase';
import { ReceiptListRepository } from '../../../../../app/features/payments/domain/repositories/receipt-list.repository';
import { ReceiptsComponent } from '../../../../../app/features/payments/infra/pages/receipts/receipts.component';
import { PaymentsCriteria } from '../../../../../app/features/payments/infra/services/payment-criteria';
import text from '../../../../../assets/i18n/receipts/es.json';
import { LocalUsecaseErrorHandler } from '../../../shared/infra/local-usecase-error-handler';
import { LocalGetReceiptsRepository } from '../repositories/local-get-receipt.repository';

describe('ReceiptsComponent', () => {
  let fixture: ComponentFixture<ReceiptsComponent>;
  let component: ReceiptsComponent;
  let uploadUsecase: jasmine.SpyObj<UploadReceiptsUseCase>;
  let notifyWarningSpy: jasmine.Spy;
  let notifySuccessSpy: jasmine.Spy;
  let errorHandler: UseCaseErrorHandler;
  let listUseCaseSpy: jasmine.Spy;
  let criteria: PaymentsCriteria;
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideNotifierTesting(),
        GetListReceiptUseCase,
        {
          provide: ReceiptListRepository,
          useClass: LocalGetReceiptsRepository,
        },
        provideLoaderTesting(),
        { provide: UseCaseErrorHandler, useClass: LocalUsecaseErrorHandler },
        {
          provide: UploadReceiptsUseCase,
          useValue: jasmine.createSpyObj('UploadReceiptsUseCase', ['execute']),
        },
        {
          provide: PaymentsCriteria,
        },
        {
          provide: I18NService,
          useValue: {
            getTranslateObjectByKey: (args: { key: string; scope: string }) => {
              const base = text as any;
              const translations = base[args.key];

              return of(translations);
            },
          },
        },
      ],
    });

    fixture = TestBed.createComponent(ReceiptsComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();

    uploadUsecase = TestBed.inject(
      UploadReceiptsUseCase
    ) as jasmine.SpyObj<UploadReceiptsUseCase>;
    const notifier = TestBed.inject(NotifierService);
    notifyWarningSpy = spyOn(notifier, 'warning').and.callThrough();
    notifySuccessSpy = spyOn(notifier, 'success').and.callThrough();
    listUseCaseSpy = spyOn(
      TestBed.inject(GetListReceiptUseCase),
      'execute'
    ).and.callThrough();
    criteria = TestBed.inject(PaymentsCriteria);
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should set the step as 0 when there are no files', () => {
    expect(component.step()).toBe(0);
  });
  it('should call listUsecase on init', () => {
    expect(listUseCaseSpy).toHaveBeenCalledTimes(0);
  });

  it('should call setPageNum when changePage is called', () => {
    const spy = spyOn(criteria, 'setPageNum');
    const newPage = 1;

    component.changePage(newPage);

    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledWith(newPage);
  });
});
