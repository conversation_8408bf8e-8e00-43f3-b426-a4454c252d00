import { <PERSON><PERSON><PERSON><PERSON><PERSON>, I<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON> } from '@angular/common';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { provideNoopAnimations } from '@angular/platform-browser/animations';
import { I18NService } from '@aplazo/i18n';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormDatepickerComponent } from '@aplazo/shared-ui/forms';
import { AplazoIconComponent } from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoStatCardComponent,
} from '@aplazo/shared-ui/merchant';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { DialogService } from '@ngneat/dialog';
import { of } from 'rxjs';
import { UserStore } from '../../../../../app/features/login/application/services/user.store';
import { GenerateInvoicesUseCase } from '../../../../../app/features/payments/application/usecases/generate-invoices.usecase';
import {
  defaultPaymentSummaryUI,
  GetInvoicesSummaryUseCase,
} from '../../../../../app/features/payments/application/usecases/get-invoices-summary.usecase';
import { emptyListResponse } from '../../../../../app/features/payments/application/usecases/get-list-receipts.usecase';
import { GetListUseCase } from '../../../../../app/features/payments/application/usecases/get-list.usecase';
import {
  emptyPaymentSummaryResponse,
  GetSummaryUseCase,
} from '../../../../../app/features/payments/application/usecases/get-summary.usecase';
import { SendReportUseCase } from '../../../../../app/features/payments/application/usecases/send-report.usecase';
import { PaymentInvoicesSummaryUI } from '../../../../../app/features/payments/domain/entities/invoices';
import { PaymentListResponse } from '../../../../../app/features/payments/domain/entities/list-response';
import { PaymentSummaryResponse } from '../../../../../app/features/payments/domain/entities/summary-response';
import { PaymentsComponent } from '../../../../../app/features/payments/infra/pages/payments/payments.component';
import { HandleInvoicesByIdsGenerationService } from '../../../../../app/features/payments/infra/services/handle-all-invoices-by-ids-generation.service';
import { HandleAllInvoicesGenerationService } from '../../../../../app/features/payments/infra/services/handle-all-invoices-generation.service';
import { PaymentsCriteria } from '../../../../../app/features/payments/infra/services/payment-criteria';
import paymentsI18N from '../../../../../assets/i18n/payments/es.json';

const setup = (args?: {
  invoiceSummary?: Partial<PaymentInvoicesSummaryUI>;
  payments?: Partial<PaymentListResponse>;
  paymentsSummary?: Partial<PaymentSummaryResponse>;
  store?: { email: string; roles: string[] };
  allInvoicesGeneration?: { withExecution: boolean };
  invoicesByIdsGeneration?: { withExecution: boolean };
}) => {
  const defaultConfig = {
    invoiceSummary: defaultPaymentSummaryUI,
    payments: emptyListResponse,
    paymentsSummary: emptyPaymentSummaryResponse,
    store: {
      email: '<EMAIL>',
      roles: ['ROLE_CONTROL_TOWER_ADMIN'],
    },
    allInvoicesGeneration: { withExecution: false },
    invoicesByIdsGeneration: { withExecution: false },
  };

  const config = {
    invoiceSummary: {
      ...defaultConfig.invoiceSummary,
      ...args?.invoiceSummary,
    },
    payments: {
      ...defaultConfig.payments,
      ...args?.payments,
    },
    paymentsSummary: {
      ...defaultConfig.paymentsSummary,
      ...args?.paymentsSummary,
    },
    store: {
      ...defaultConfig.store,
      ...args?.store,
    },
    allInvoicesGeneration: {
      ...defaultConfig.allInvoicesGeneration,
      ...args?.allInvoicesGeneration,
    },
    invoicesByIdsGeneration: {
      ...defaultConfig.invoicesByIdsGeneration,
      ...args?.invoicesByIdsGeneration,
    },
  };

  TestBed.configureTestingModule({
    imports: [
      NgIf,
      NgFor,
      AsyncPipe,
      I18nPluralPipe,
      AplazoFormDatepickerComponent,
      AplazoStatCardComponent,
      AplazoCardComponent,
      AplazoButtonComponent,
      AplazoIconComponent,
      AplazoPaginationComponent,
      AplazoSimpleTableComponents,
      AplazoCommonMessageComponents,
      AplazoDynamicPipe,
    ],
    providers: [
      provideNoopAnimations(),
      provideNotifierTesting(),
      provideLoaderTesting(),
      provideUseCaseErrorHandlerTesting(),
      {
        provide: GetInvoicesSummaryUseCase,
        useValue: {
          execute: () => {
            return of(config.invoiceSummary);
          },
        },
      },
      {
        provide: GetSummaryUseCase,
        useValue: {
          execute: () => {
            return of(config.paymentsSummary);
          },
        },
      },
      {
        provide: GetListUseCase,
        useValue: {
          execute: () => {
            return of(config.payments);
          },
        },
      },
      {
        provide: SendReportUseCase,
        useValue: {
          execute: () => {
            return of(void 0);
          },
        },
      },
      {
        provide: UserStore,
        useValue: {
          email$: of(config.store.email),
          roles$: of(config.store.roles),
        },
      },
      {
        provide: I18NService,
        useValue: {
          getTranslateObjectByKey: (args: { key: string; scope: string }) => {
            const payments = paymentsI18N as Record<string, any>;

            return of(payments[args.key]);
          },
        },
      },
      {
        provide: GenerateInvoicesUseCase,
        useValue: jasmine.createSpyObj('GenerateInvoicesUseCase', ['execute']),
      },
      {
        provide: HandleAllInvoicesGenerationService,
        useValue: {
          execute: async () => config.allInvoicesGeneration,
        },
      },
      {
        provide: HandleInvoicesByIdsGenerationService,
        useValue: {
          execute: async () => config.invoicesByIdsGeneration,
        },
      },
      DialogService,
      PaymentsCriteria,
    ],
  });

  const fixture = TestBed.createComponent(PaymentsComponent);
  const component = fixture.componentInstance;
  const summaryUseCaseSpy = spyOn(
    TestBed.inject(GetSummaryUseCase),
    'execute'
  ).and.callThrough();
  const listUseCaseSpy = spyOn(
    TestBed.inject(GetListUseCase),
    'execute'
  ).and.callThrough();

  const invoicesSummarySpy = TestBed.inject(GetInvoicesSummaryUseCase);

  fixture.detectChanges();

  const reportUsecase = TestBed.inject(SendReportUseCase);

  const criteria = TestBed.inject(PaymentsCriteria);
  const dialog = TestBed.inject(DialogService);

  return {
    fixture,
    component,
    summaryUseCaseSpy,
    listUseCaseSpy,
    invoicesSummarySpy,
    reportUsecase,
    criteria,
    dialog,
  };
};

describe('Payments Component', () => {
  it('should be created', () => {
    const { component } = setup();

    expect(component).toBeTruthy();
  });

  it('should call summaryUsecase on init', () => {
    const { summaryUseCaseSpy } = setup();

    expect(summaryUseCaseSpy).toHaveBeenCalledTimes(1);
  });

  it('should call listUsecase on init', () => {
    const { listUseCaseSpy } = setup();

    expect(listUseCaseSpy).toHaveBeenCalledTimes(1);
  });

  it('should call setPageNum when changePage is called', () => {
    const { component, criteria } = setup();

    const spy = spyOn(criteria, 'setPageNum');
    const newPage = 1;

    component.changePage(newPage);

    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledWith(newPage);
  });

  it('should call dialog open when sendReport is called', () => {
    const { component, dialog } = setup();

    const dialogSpy = spyOn(dialog, 'open').and.returnValue({
      afterClosed$: of('payment'),
    } as any);

    component.sendReport();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
  });

  it('should not call report usecase when sendReport is called and dialog result is undefined', () => {
    const { component, dialog, reportUsecase } = setup();

    const dialogSpy = spyOn(dialog, 'open').and.returnValue({
      afterClosed$: of(undefined),
    } as any);

    const reportUsecaseSpy = spyOn(reportUsecase, 'execute').and.callThrough();

    component.sendReport();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(reportUsecaseSpy).toHaveBeenCalledTimes(0);
  });

  it('should not call report usecase when sendReport is called and dialog result is not a valid REPORT_TYPES', () => {
    const { component, dialog, reportUsecase } = setup();

    const dialogSpy = spyOn(dialog, 'open').and.returnValue({
      afterClosed$: of('NOT_VALID'),
    } as any);

    const reportUsecaseSpy = spyOn(reportUsecase, 'execute');
    reportUsecaseSpy.and.callThrough();

    component.sendReport();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(reportUsecaseSpy).toHaveBeenCalledTimes(0);
  });

  it('should call report usecase when sendReport is called and dialog result is a valid REPORT_TYPES', fakeAsync(() => {
    const { component, dialog, reportUsecase, fixture } = setup();

    const reportUsecaseSpy = spyOn(reportUsecase, 'execute').and.callThrough();
    const dialogSpy = spyOn(dialog, 'open').and.returnValue({
      afterClosed$: of('payment'),
    } as any);

    component.sendReport();
    fixture.detectChanges();
    tick();

    expect(dialogSpy)
      .withContext('dialog open should be called once')
      .toHaveBeenCalledTimes(1);

    expect(reportUsecaseSpy)
      .withContext('report usecase should be called once')
      .toHaveBeenCalledTimes(1);
  }));

  describe('Filters', () => {
    it('should call criteria setFrequency when frequency filter changes', () => {
      const { component, criteria } = setup();
      const spy = spyOn(criteria, 'setFrequency');
      const value = 'WEEKLY';

      component.frequencyControl.setValue(value);

      expect(spy).toHaveBeenCalledWith(value);
    });

    it('should call criteria setPaymentStatus when payment status filter changes', () => {
      const { component, criteria } = setup();
      const spy = spyOn(criteria, 'setPaymentStatus');
      const value = 'PAID';

      component.paymentStatusControl.setValue(value);

      expect(spy).toHaveBeenCalledWith(value);
    });

    it('should call criteria setInvoiceStatus when invoice status filter changes', () => {
      const { component, criteria } = setup();
      const spy = spyOn(criteria, 'setInvoiceStatus');
      const value = 'PENDING';

      component.invoiceStatusControl.setValue(value);

      expect(spy).toHaveBeenCalledWith(value);
    });

    it('should reset all filters when resetFilters is called', () => {
      const { component, criteria } = setup();
      const resetSpy = spyOn(criteria, 'resetFilters');

      component.frequencyControl.setValue('WEEKLY');
      component.paymentStatusControl.setValue('PAID');
      component.invoiceStatusControl.setValue('PENDING');

      component.resetFilters();

      expect(component.frequencyControl.value).toBeNull();
      expect(component.paymentStatusControl.value).toBeNull();
      expect(component.invoiceStatusControl.value).toBeNull();
      expect(resetSpy).toHaveBeenCalled();
    });

    it('should update criteria and trigger update when onSelectFilterChange is called', () => {
      const { component, criteria } = setup();
      const event = { target: { value: 'WEEKLY' } } as unknown as Event;
      const spy = spyOn(criteria, 'setFrequency');

      component.onSelectFilterChange(event, 'frequency');

      expect(spy).toHaveBeenCalledWith('WEEKLY');
    });

    it('should handle null value in onSelectFilterChange', () => {
      const { component, criteria } = setup();
      const event = { target: { value: 'null' } } as unknown as Event;
      const spy = spyOn(criteria, 'setFrequency');

      component.onSelectFilterChange(event, 'frequency');

      expect(spy).toHaveBeenCalledWith(null);
    });

    it('should update criteria when onAutocompleteItemSelected is called', () => {
      const { component, criteria } = setup();
      const event = { id: 123, name: 'Test' };
      const spy = spyOn(criteria, 'setPaymentId');

      component.onAutocompleteItemSelected(event, 'paymentIdAutocomplete');

      expect(spy).toHaveBeenCalledWith(123);
    });

    it('should reset criteria when onAutocompleteItemRemoved is called', () => {
      const { component, criteria } = setup();
      const spy = spyOn(criteria, 'setPaymentId');

      component.onAutocompleteItemRemoved('paymentIdAutocomplete');

      expect(spy).toHaveBeenCalledWith(null);
    });
  });
});
