import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { ReceiptStateIconComponent } from '../../../../../app/features/payments/infra/components/receipt-state-icon.component';

describe('ReceiptStateIconComponent', () => {
  let fixture: ComponentFixture<ReceiptStateIconComponent>;
  let component: ReceiptStateIconComponent;
  let serviceSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [AplazoIconComponent],
      providers: [AplazoIconRegistryService],
    });

    fixture = TestBed.createComponent(ReceiptStateIconComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();

    serviceSpy = spyOn(
      TestBed.inject(AplazoIconRegistryService),
      'registerIcons'
    ).and.callThrough();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
