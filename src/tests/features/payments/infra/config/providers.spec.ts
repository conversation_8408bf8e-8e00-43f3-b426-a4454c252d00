import { provideHttpClient } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';
import { PaymentListRepository } from '../../../../../app/features/payments/domain/repositories/payment-list.repository';
import { PaymentReportRepository } from '../../../../../app/features/payments/domain/repositories/payment-report.repository';
import { PaymentSummaryRepository } from '../../../../../app/features/payments/domain/repositories/payment-summary.repository';
import { providePayments } from '../../../../../app/features/payments/infra/config/providers';
import { PaymentsListWithHttpRepository } from '../../../../../app/features/payments/infra/repositories/list-with-http.repository';
import { PaymentsSendReportWithHttpRepository } from '../../../../../app/features/payments/infra/repositories/send-report-with-http.repository';
import { PaymentsSummaryWithHttpRepository } from '../../../../../app/features/payments/infra/repositories/summary-with-http.repository';

describe('Payments config providers', () => {
  it('should provide Payments Summary Repository', () => {
    TestBed.configureTestingModule({
      providers: [
        providePayments(),
        provideHttpClient(),
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            googleClientId: 'googleId',
            apiBaseUrl: 'http://localhost:3000',
            bifrostUrl: 'http://localhost:3000',
            i18nUrl: 'http://localhost:3000',
            landingUrl: 'http://localhost:3000',
          },
        },
      ],
    });

    const repository = TestBed.inject(PaymentSummaryRepository);

    expect(repository).toBeDefined();
    expect(repository).toBeInstanceOf(PaymentsSummaryWithHttpRepository);
  });

  it('should provide Payments List Repository', () => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        providePayments(),
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            googleClientId: 'googleId',
            apiBaseUrl: 'http://localhost:3000',
            bifrostUrl: 'http://localhost:3000',
            i18nUrl: 'http://localhost:3000',
            landingUrl: 'http://localhost:3000',
          },
        },
      ],
    });

    const repository = TestBed.inject(PaymentListRepository);

    expect(repository).toBeDefined();
    expect(repository).toBeInstanceOf(PaymentsListWithHttpRepository);
  });

  it('should provide Payments Report Repository', () => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        providePayments(),
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            googleClientId: 'googleId',
            apiBaseUrl: 'http://localhost:3000',
            bifrostUrl: 'http://localhost:3000',
            i18nUrl: 'http://localhost:3000',
            landingUrl: 'http://localhost:3000',
          },
        },
      ],
    });

    const repository = TestBed.inject(PaymentReportRepository);

    expect(repository).toBeDefined();
    expect(repository).toBeInstanceOf(PaymentsSendReportWithHttpRepository);
  });
});
