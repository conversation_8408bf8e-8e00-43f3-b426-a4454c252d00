import { FileSizePipe } from '../../../../../app/features/payments/infra/pipes/filesize.pipe';

describe('FilesizePipe', () => {
  it('should return "0 B" when value is null', () => {
    const pipe = new FileSizePipe();
    const result = pipe.transform(null);
    expect(result).toBe('0 B');
  });

  it('should return the value in bytes', () => {
    const pipe = new FileSizePipe();
    const result = pipe.transform(1024);
    expect(result).toBe('1.02 kB');
  });
});
