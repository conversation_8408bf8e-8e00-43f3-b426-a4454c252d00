import { ReceiptStatusUiPipe } from '../../../../../app/features/payments/infra/pipes/receipt-status-ui.pipe';

describe('ReceiptStatusUiPipe', () => {
  let pipe: ReceiptStatusUiPipe;

  beforeEach(() => {
    pipe = new ReceiptStatusUiPipe();
  });

  it('should return "DESCONOCIDO" when value is null', () => {
    expect(pipe.transform(null)).toBe('DESCONOCIDO');
  });

  it('should return "DESCONOCIDO" when value is undefined', () => {
    expect(pipe.transform(undefined)).toBe('DESCONOCIDO');
  });

  it('should return the UI value when value is not null or undefined', () => {
    expect(pipe.transform('uploadError')).toBe('CARGA FALLIDA');
  });

  it('should return the UI value when value is not null or undefined', () => {
    expect(pipe.transform('loading')).toBe('CARGANDO');
  });

  it('should return the UI value when value is not null or undefined', () => {
    expect(pipe.transform('loaded')).toBe('CARGADO');
  });

  it('should return the UI value when value is not null or undefined', () => {
    expect(pipe.transform('ready')).toBe('LISTO');
  });
});
