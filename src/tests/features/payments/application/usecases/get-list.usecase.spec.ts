import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import { provideLoaderTesting } from '@aplazo/merchant/shared-testing';
import { lastValueFrom } from 'rxjs';
import { PaymentListRequestDto } from '../../../../../app/features/payments/application/dtos/list-request.dto';
import { GetListUseCase } from '../../../../../app/features/payments/application/usecases/get-list.usecase';
import { PaymentListRepository } from '../../../../../app/features/payments/domain/repositories/payment-list.repository';
import { LocalUsecaseErrorHandler } from '../../../shared/infra/local-usecase-error-handler';
import { LocalGetPaymentsRepository } from '../../infra/repositories/local-get-payments.repository';

describe('GetListUseCase', () => {
  let usecase: GetListUseCase;

  let repository: PaymentListRepository;
  let loader: LoaderService;
  let errorHandler: UseCaseErrorHandler;
  let spyLoaderShow: jasmine.Spy;
  let spyLoaderHide: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: PaymentListRepository,
          useClass: LocalGetPaymentsRepository,
        },
        provideLoaderTesting(),
        { provide: UseCaseErrorHandler, useClass: LocalUsecaseErrorHandler },
        GetListUseCase,
      ],
    });

    usecase = TestBed.inject(GetListUseCase);
    repository = TestBed.inject(PaymentListRepository);
    loader = TestBed.inject(LoaderService);
    errorHandler = TestBed.inject(UseCaseErrorHandler);

    spyLoaderShow = spyOn(loader, 'show').and.callThrough();
    spyLoaderHide = spyOn(loader, 'hide').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GetListUseCase);
  });

  it('should retrieve the list of payments', async () => {
    const spyGetList = spyOn(repository, 'getList').and.callThrough();
    const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();
    const request: PaymentListRequestDto = {
      date: '12/03/2024',
      pageNum: 0,
      pageSize: 10,
    };

    const response = await lastValueFrom(usecase.execute(request));

    expect(response.hasContent).toBeTrue();
    expect(response.content.length).toBe(10);
    expect(response.totalElements).toBe(10);

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
    expect(spyGetList).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(0);
  });

  it('should throw a RuntimeMerchantError when the date is invalid', async () => {
    const spyGetList = spyOn(repository, 'getList').and.callThrough();
    const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();
    const request: PaymentListRequestDto = {
      // @ts-expect-error: testing invalid date
      date: '12-03-2024',
      pageNum: 0,
      pageSize: 10,
    };

    const response = await lastValueFrom(usecase.execute(request));

    expect(response.hasContent).toBeFalse();
    expect(response.content.length).toBe(0);
    expect(response.totalElements).toBe(0);

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);

    expect(spyGetList).toHaveBeenCalledTimes(0);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
  });

  it('should throw a RuntimeMerchantError when the pageNum is invalid', async () => {
    const spyGetList = spyOn(repository, 'getList').and.callThrough();
    const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();
    const request: PaymentListRequestDto = {
      date: '12/03/2024',
      // @ts-expect-error: testing invalid pageNum
      pageNum: 'a',
      pageSize: 10,
    };

    const response = await lastValueFrom(usecase.execute(request));

    expect(response.hasContent).toBeFalse();
    expect(response.content.length).toBe(0);
    expect(response.totalElements).toBe(0);

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);

    expect(spyGetList).toHaveBeenCalledTimes(0);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
  });

  it('should throw a RuntimeMerchantError when the pageSize is invalid', async () => {
    const spyGetList = spyOn(repository, 'getList').and.callThrough();
    const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();
    const request: PaymentListRequestDto = {
      date: '12/03/2024',
      pageNum: 0,
      // @ts-expect-error: testing invalid pageSize
      pageSize: 'a',
    };

    const response = await lastValueFrom(usecase.execute(request));

    expect(response.hasContent).toBeFalse();
    expect(response.content.length).toBe(0);
    expect(response.totalElements).toBe(0);

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);

    expect(spyGetList).toHaveBeenCalledTimes(0);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
  });

  it('should handle error from error response from repository', async () => {
    const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();
    const spyGetList = spyOn(repository, 'getList').and.throwError(
      'error test'
    );
    const request: PaymentListRequestDto = {
      date: '12/03/2024',
      pageNum: 0,
      pageSize: 10,
    };

    const response = await lastValueFrom(usecase.execute(request));

    expect(response.hasContent).toBeFalse();
    expect(response.content.length).toBe(0);
    expect(response.totalElements).toBe(0);

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);

    expect(spyGetList).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
  });

  it('should retrieve the list of payments with all filters', async () => {
    const spyGetList = spyOn(repository, 'getList').and.callThrough();
    const spyGetInvoiceStatusList = spyOn(
      repository,
      'getInvoiceStatusList'
    ).and.callThrough();
    const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();
    const request: PaymentListRequestDto = {
      date: '12/03/2024',
      pageNum: 0,
      pageSize: 10,
      frequency: 'weekly',
      paymentStatus: 'PAID',
      invoiceStatus: 'ACTIVE',
      paymentId: 123,
      merchantId: 456,
      name: 'Test Merchant',
    };

    const response = await lastValueFrom(usecase.execute(request));

    expect(response.hasContent).toBeTrue();
    expect(response.content.length).toBe(10);
    expect(response.totalElements).toBe(10);

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
    expect(spyGetList).toHaveBeenCalledWith({
      date: '2024-03-12',
      pageNum: 0,
      pageSize: 10,
      frequency: 'WEEKLY',
      paymentStatus: 'PAID',
      invoiceStatus: 'ACTIVE',
      paymentId: 123,
      merchantId: 456,
      name: 'Test Merchant',
    });
    expect(spyGetInvoiceStatusList).toHaveBeenCalledWith({
      start: '2024-03-12',
      end: '2024-03-12',
      frequency: 'WEEKLY',
      paymentStatus: 'PAID',
      invoiceStatus: 'ACTIVE',
      paymentId: 123,
      merchantId: 456,
      name: 'Test Merchant',
    });
    expect(spyErrorHandler).toHaveBeenCalledTimes(0);
  });

  it('should convert frequency to uppercase when provided', async () => {
    const spyGetList = spyOn(repository, 'getList').and.callThrough();
    const request: PaymentListRequestDto = {
      date: '12/03/2024',
      pageNum: 0,
      pageSize: 10,
      frequency: 'monthly',
    };

    await lastValueFrom(usecase.execute(request));

    expect(spyGetList).toHaveBeenCalledWith(
      jasmine.objectContaining({
        frequency: 'MONTHLY',
      })
    );
  });

  it('should handle null frequency', async () => {
    const spyGetList = spyOn(repository, 'getList').and.callThrough();
    const request: PaymentListRequestDto = {
      date: '12/03/2024',
      pageNum: 0,
      pageSize: 10,
      frequency: '',
    };

    await lastValueFrom(usecase.execute(request));

    expect(spyGetList).toHaveBeenCalledWith(
      jasmine.objectContaining({
        frequency: null,
      })
    );
  });
});
