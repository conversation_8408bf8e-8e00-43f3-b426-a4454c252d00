import { TestBed } from '@angular/core/testing';
import {
  LoaderService,
  RawDateYearFirstWithHyphen,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { provideLoaderTesting } from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable, of } from 'rxjs';
import { GetSummaryUseCase } from '../../../../../app/features/payments/application/usecases/get-summary.usecase';
import { PaymentSummaryResponse } from '../../../../../app/features/payments/domain/entities/summary-response';
import { PaymentSummaryRepository } from '../../../../../app/features/payments/domain/repositories/payment-summary.repository';
import { LocalUsecaseErrorHandler } from '../../../shared/infra/local-usecase-error-handler';
import { LocalGetSummaryRepository } from '../../infra/repositories/local-get-summary.repository';

describe('GetSummaryUseCase', () => {
  let usecase: GetSummaryUseCase;
  let repository: PaymentSummaryRepository<
    RawDateYearFirstWithHyphen,
    Observable<PaymentSummaryResponse>
  >;
  let loader: LoaderService;

  let spyLoaderShow: jasmine.Spy;
  let spyLoaderHide: jasmine.Spy;
  let spyUsecaseHandler: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideLoaderTesting(),
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        {
          provide: PaymentSummaryRepository,
          useClass: LocalGetSummaryRepository,
        },
        GetSummaryUseCase,
      ],
    });

    usecase = TestBed.inject(GetSummaryUseCase);
    repository = TestBed.inject(PaymentSummaryRepository);
    loader = TestBed.inject(LoaderService);

    spyLoaderShow = spyOn(loader, 'show').and.callThrough();
    spyLoaderHide = spyOn(loader, 'hide').and.callThrough();
    spyUsecaseHandler = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GetSummaryUseCase);
  });

  it('should retrieve the summary of payments', async () => {
    const spyGetSummary = spyOn(repository, 'getByDate').and.callThrough();
    const req = '12/03/2024';

    const summary = await lastValueFrom(usecase.execute(req));

    expect(summary).toEqual({
      paymentDate: '2024-03-12',
      saleAmount: 81097.46,
      feeAmount: 52713.35,
      adjustmentPlus: 5110,
      adjustmentMinus: -4963,
      finalAmount: 28384.11,
      payAmount: 28531.11,
    });

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);

    expect(spyGetSummary).toHaveBeenCalledTimes(1);
    expect(spyUsecaseHandler).toHaveBeenCalledTimes(0);
  });

  it('should retrieve empty summary when the date is mal formatted', async () => {
    const spyGetSummary = spyOn(repository, 'getByDate').and.callThrough();

    const req = '12-03-2024';

    // @ts-expect-error: testing invalid date
    const summary = await lastValueFrom(usecase.execute(req));

    expect(summary).toEqual({
      paymentDate: undefined,
      saleAmount: 0,
      feeAmount: 0,
      adjustmentPlus: 0,
      adjustmentMinus: 0,
      finalAmount: 0,
      payAmount: 0,
    });

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
    expect(spyUsecaseHandler).toHaveBeenCalledTimes(1);

    expect(spyGetSummary).toHaveBeenCalledTimes(0);
  });

  it('should retrieve empty summary when the repository throws an error', async () => {
    const spyGetSummary = spyOn(repository, 'getByDate').and.throwError(
      'error test'
    );

    const req = '12/03/2024';

    const summary = await lastValueFrom(usecase.execute(req));

    expect(summary).toEqual({
      paymentDate: undefined,
      saleAmount: 0,
      feeAmount: 0,
      adjustmentPlus: 0,
      adjustmentMinus: 0,
      finalAmount: 0,
      payAmount: 0,
    });

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
    expect(spyGetSummary).toHaveBeenCalledTimes(1);
    expect(spyUsecaseHandler).toHaveBeenCalledTimes(1);
  });

  it('should retrieve empty summary when the repository returns null', async () => {
    const spyGetSummary = spyOn(repository, 'getByDate').and.returnValue(
      of(null) as unknown as Observable<PaymentSummaryResponse>
    );

    const req = '12/03/2024';

    const summary = await lastValueFrom(usecase.execute(req));

    expect(summary).toEqual({
      paymentDate: undefined,
      saleAmount: 0,
      feeAmount: 0,
      adjustmentPlus: 0,
      adjustmentMinus: 0,
      finalAmount: 0,
      payAmount: 0,
    });

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
    expect(spyGetSummary).toHaveBeenCalledTimes(1);

    expect(spyUsecaseHandler).toHaveBeenCalledTimes(0);
  });

  it('should clean the summary response when response have null or invalid data', async () => {
    const spyGetSummary = spyOn(repository, 'getByDate').and.returnValue(
      of({
        paymentDate: '2024-03-12',
        saleAmount: null,
        feeAmount: null,
        adjustmentPlus: null,
        adjustmentMinus: null,
        finalAmount: null,
        payAmount: null,
      } as unknown as PaymentSummaryResponse)
    );

    const req = '12/03/2024';

    const summary = await lastValueFrom(usecase.execute(req));

    expect(summary).toEqual({
      paymentDate: '2024-03-12',
      saleAmount: 0,
      feeAmount: 0,
      adjustmentPlus: 0,
      adjustmentMinus: 0,
      finalAmount: 0,
      payAmount: 0,
    });

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
    expect(spyGetSummary).toHaveBeenCalledTimes(1);
    expect(spyUsecaseHandler).toHaveBeenCalledTimes(0);
  });
});
