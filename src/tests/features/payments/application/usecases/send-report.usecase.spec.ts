import { TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable } from 'rxjs';
import {
  PaymentReportRepositoryDto,
  PaymentReportRequestDto,
} from '../../../../../app/features/payments/application/dtos/report-request.dto';
import { SendReportUseCase } from '../../../../../app/features/payments/application/usecases/send-report.usecase';
import { PaymentReportRepository } from '../../../../../app/features/payments/domain/repositories/payment-report.repository';
import { LocalUsecaseErrorHandler } from '../../../shared/infra/local-usecase-error-handler';
import { LocalSendReportRepository } from '../../infra/repositories/local-send-report.repository';

describe('SendReportUseCase', () => {
  let usecase: SendReportUseCase;
  let repository: PaymentReportRepository<
    PaymentReportRepositoryDto,
    Observable<void>
  >;
  let loader: LoaderService;
  let notifier: NotifierService;

  let spyLoaderShow: jasmine.Spy;
  let spyLoaderHide: jasmine.Spy;
  let spyErrorHandler: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideNotifierTesting(),
        provideLoaderTesting(),
        {
          provide: PaymentReportRepository,
          useClass: LocalSendReportRepository,
        },
        { provide: UseCaseErrorHandler, useClass: LocalUsecaseErrorHandler },
        SendReportUseCase,
      ],
    });

    usecase = TestBed.inject(SendReportUseCase);
    repository = TestBed.inject(PaymentReportRepository);
    loader = TestBed.inject(LoaderService);
    notifier = TestBed.inject(NotifierService);

    spyLoaderShow = spyOn(loader, 'show').and.callThrough();
    spyLoaderHide = spyOn(loader, 'hide').and.callThrough();
    spyErrorHandler = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should create a report', async () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(SendReportUseCase);
  });

  it('should send a report', async () => {
    const spySendReport = spyOn(repository, 'getReport').and.callThrough();
    const successNotifierSpy = spyOn(notifier, 'success').and.callThrough();

    const request: PaymentReportRequestDto = {
      date: '12/03/2024',
      type: 'payment',
      email: '<EMAIL>',
    };

    await lastValueFrom(usecase.execute(request));

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
    expect(spySendReport).toHaveBeenCalledTimes(1);
    expect(spySendReport).toHaveBeenCalledWith({
      date: '12/03/2024',
      emails: '<EMAIL>',
      type: 'PAYMENT',
    });
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);

    expect(spyErrorHandler).toHaveBeenCalledTimes(0);
  });

  it('should call error handler when date request is invalid', async () => {
    const spySendReport = spyOn(repository, 'getReport').and.callThrough();

    const request: PaymentReportRequestDto = {
      // @ts-expect-error: testing invalid date
      date: '12-03-2024',
      type: 'payment',
      email: '<EMAIL>',
    };

    await lastValueFrom(usecase.execute(request));

    expect(spySendReport).toHaveBeenCalledTimes(0);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
  });

  it('should call error handler when email request is invalid', async () => {
    const spySendReport = spyOn(repository, 'getReport').and.callThrough();

    const request: PaymentReportRequestDto = {
      date: '12/03/2024',
      type: 'payment',
      // @ts-expect-error: testing invalid email
      email: null,
    };

    await lastValueFrom(usecase.execute(request));

    expect(spySendReport).toHaveBeenCalledTimes(0);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
  });

  it('should call error handler when type request is null', async () => {
    const spySendReport = spyOn(repository, 'getReport').and.callThrough();

    const request: PaymentReportRequestDto = {
      date: '12/03/2024',
      // @ts-expect-error: testing invalid type
      type: null,
      email: '<EMAIL>',
    };

    await lastValueFrom(usecase.execute(request));

    expect(spySendReport).toHaveBeenCalledTimes(0);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
  });

  it('should call error handler when type request is invalid', async () => {
    const spySendReport = spyOn(repository, 'getReport').and.callThrough();

    const request: PaymentReportRequestDto = {
      date: '12/03/2024',
      type: 'invalidType',
      email: '<EMAIL>',
    };

    await lastValueFrom(usecase.execute(request));

    expect(spySendReport).toHaveBeenCalledTimes(0);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
  });

  it('should call error handler when repository throws an exception', async () => {
    const spySendReport = spyOn(repository, 'getReport').and.throwError(
      'error test'
    );

    const request: PaymentReportRequestDto = {
      date: '12/03/2024',
      type: 'payment',
      email: '<EMAIL>',
    };

    await lastValueFrom(usecase.execute(request));

    expect(spySendReport).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
  });
});
