import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import {
  defaultPaymentSummaryUI,
  GetInvoicesSummaryUseCase,
} from '../../../../../app/features/payments/application/usecases/get-invoices-summary.usecase';
import { PaymentInvoicesSummaryUI } from '../../../../../app/features/payments/domain/entities/invoices';
import { PaymentInvoicesRepository } from '../../../../../app/features/payments/domain/repositories/invoices.repository';

describe('GetInvoicesSummaryUseCase', () => {
  let useCase: GetInvoicesSummaryUseCase;
  let repository: jasmine.SpyObj<PaymentInvoicesRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handlerErrorSpy: jasmine.Spy;

  beforeEach(() => {
    const repositorySpy = jasmine.createSpyObj('PaymentInvoicesRepository', [
      'getSummary',
    ]);

    TestBed.configureTestingModule({
      providers: [
        GetInvoicesSummaryUseCase,
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        { provide: PaymentInvoicesRepository, useValue: repositorySpy },
      ],
    });

    useCase = TestBed.inject(GetInvoicesSummaryUseCase);
    repository = TestBed.inject(
      PaymentInvoicesRepository
    ) as jasmine.SpyObj<PaymentInvoicesRepository>;
    const loaderService = TestBed.inject(LoaderService);
    const errorHandler = TestBed.inject(UseCaseErrorHandler);

    showLoaderSpy = spyOn(loaderService, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loaderService, 'hide').and.callThrough();

    handlerErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should return summary data on success', fakeAsync(() => {
    const mockResponse = {
      processing: 10,
      invoiced: 20,
      pending: 30,
      error: 5,
      excluded: 2,
    };
    const expectedSummary: PaymentInvoicesSummaryUI = {
      processing: { label: 'En proceso', amount: 10 },
      invoiced: { label: 'Facturado', amount: 20 },
      pending: { label: 'Pendiente', amount: 30 },
      error: { label: 'Error', amount: 5 },
      excluded: { label: 'Excluido', amount: 2 },
    };

    repository.getSummary.and.returnValue(of(mockResponse));

    let result: any;

    useCase
      .execute({ startDate: '05/05/2024', endDate: '05/05/2024' })
      .subscribe({
        next: r => {
          result = r;
        },
        error: fail,
      });

    tick();

    expect(result).toEqual(expectedSummary);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handlerErrorSpy).toHaveBeenCalledTimes(0);
  }));

  it('should handle error and return default summary', fakeAsync(() => {
    repository.getSummary.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
          })
      )
    );

    let result: any;

    useCase
      .execute({ startDate: '05/05/2024', endDate: '05/05/2024' })
      .subscribe({
        next: r => {
          result = r;
        },
        error: fail,
      });

    tick();

    expect(result).toEqual(defaultPaymentSummaryUI);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
  }));
});
