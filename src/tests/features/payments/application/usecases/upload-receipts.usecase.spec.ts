import { TestBed } from '@angular/core/testing';
import { NotifierService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { isEmpty, lastValueFrom, take } from 'rxjs';
import { UploadReceiptsUseCase } from '../../../../../app/features/payments/application/usecases/upload-receipts.usecase';
import { ReceiptsRepository } from '../../../../../app/features/payments/domain/repositories/receipts.repository';

describe('UploadReceiptsUsecase', () => {
  let usecase: UploadReceiptsUseCase;
  let repository: jasmine.SpyObj<ReceiptsRepository>;
  let handleUsecaseError: jasmine.Spy;
  let notifyWarningSpy: jasmine.Spy;

  const request = {
    identifier: '20082024',
    files: [
      new File([''], 'receipt1.jpg', { type: 'image/jpeg' }),
      new File([''], 'receipt1.jpg', { type: 'image/jpeg' }),
    ],
    multiple: false,
    paymentDate: '2024-09-13',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        UploadReceiptsUseCase,
        provideUseCaseErrorHandlerTesting(),
        provideNotifierTesting(),
        {
          provide: ReceiptsRepository,
          useValue: jasmine.createSpyObj('ReceiptsRepository', ['uploadFile']),
        },
      ],
    });

    usecase = TestBed.inject(UploadReceiptsUseCase);
    repository = TestBed.inject(
      ReceiptsRepository
    ) as jasmine.SpyObj<ReceiptsRepository>;

    const notifier = TestBed.inject(NotifierService);
    notifyWarningSpy = spyOn(notifier, 'warning').and.callThrough();
    handleUsecaseError = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(UploadReceiptsUseCase);
  });

  it('should complete without stream when multipleEnabled is disabled and multiple files are sent ', async () => {
    const isCompletedWithoutStream = await lastValueFrom(
      usecase.execute(request).pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutStream).toBeTrue();
    expect(handleUsecaseError).toHaveBeenCalledTimes(1);
    expect(repository.uploadFile).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when multipleEnabled is undefined and multiple files are sent ', async () => {
    const isCompletedWithoutStream = await lastValueFrom(
      usecase
        .execute({ ...request, multiple: undefined })
        .pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutStream).toBeTrue();
    expect(handleUsecaseError).toHaveBeenCalledTimes(1);
    expect(repository.uploadFile).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when file has not name', async () => {
    const isCompletedWithoutStream = await lastValueFrom(
      usecase
        .execute({
          ...request,
          files: [new File([''], '', { type: 'image/jpeg' })],
        })
        .pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutStream).toBeTrue();
    expect(handleUsecaseError).toHaveBeenCalledTimes(1);
    expect(repository.uploadFile).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when file has not size', async () => {
    const isCompletedWithoutStream = await lastValueFrom(
      usecase
        .execute({
          ...request,
          files: [new File([''], 'receipt1.jpg', { type: 'image/jpeg' })],
        })
        .pipe(take(1), isEmpty())
    );

    expect(isCompletedWithoutStream).toBeTrue();
    expect(handleUsecaseError).toHaveBeenCalledTimes(1);
    expect(repository.uploadFile).toHaveBeenCalledTimes(0);
  });
});
