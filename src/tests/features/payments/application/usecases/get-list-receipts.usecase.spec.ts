import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import { provideLoaderTesting } from '@aplazo/merchant/shared-testing';
import { lastValueFrom, Observable } from 'rxjs';
import { ReceiptHistoryRequest } from '../../../../../app/features/payments/application/dtos/upload-receipts.dto';
import { GetListReceiptUseCase } from '../../../../../app/features/payments/application/usecases/get-list-receipts.usecase';
import { UploadReceiptsResponse } from '../../../../../app/features/payments/domain/entities/list-response';
import { ReceiptListRepository } from '../../../../../app/features/payments/domain/repositories/receipt-list.repository';
import { LocalUsecaseErrorHandler } from '../../../shared/infra/local-usecase-error-handler';
import { LocalGetReceiptsRepository } from '../../infra/repositories/local-get-receipt.repository';

describe('GetListReceiptUseCase', () => {
  let usecase: GetListReceiptUseCase;

  let repository: ReceiptListRepository<
    ReceiptHistoryRequest,
    Observable<UploadReceiptsResponse>
  >;
  let loader: LoaderService;
  let errorHandler: UseCaseErrorHandler;
  let spyLoaderShow: jasmine.Spy;
  let spyLoaderHide: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: ReceiptListRepository,
          useClass: LocalGetReceiptsRepository,
        },
        provideLoaderTesting(),
        { provide: UseCaseErrorHandler, useClass: LocalUsecaseErrorHandler },
        GetListReceiptUseCase,
      ],
    });

    usecase = TestBed.inject(GetListReceiptUseCase);
    repository = TestBed.inject(ReceiptListRepository);
    loader = TestBed.inject(LoaderService);
    errorHandler = TestBed.inject(UseCaseErrorHandler);

    spyLoaderShow = spyOn(loader, 'show').and.callThrough();
    spyLoaderHide = spyOn(loader, 'hide').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GetListReceiptUseCase);
  });

  it('should retrieve the list of receipts', async () => {
    const spyGetList = spyOn(repository, 'getList').and.callThrough();
    const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();
    const request: ReceiptHistoryRequest = {
      pageNum: 0,
      pageSize: 10,
    };

    const response = await lastValueFrom(usecase.execute(request));
    expect(response.hasContent).toBeTrue();
    expect(response.content.length).toBe(1);
    expect(response.totalElements).toBe(1);

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);
    expect(spyGetList).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(0);
  });

  it('should throw a RuntimeMerchantError when the pageNum is invalid', async () => {
    const spyGetList = spyOn(repository, 'getList').and.callThrough();
    const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();
    const request: ReceiptHistoryRequest = {
      // @ts-expect-error: testing invalid pageNum
      pageNum: 'a',
      pageSize: 10,
    };

    const response = await lastValueFrom(usecase.execute(request));

    expect(response.hasContent).toBeFalse();
    expect(response.content.length).toBe(0);
    expect(response.totalElements).toBe(0);

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);

    expect(spyGetList).toHaveBeenCalledTimes(0);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
  });

  it('should throw a RuntimeMerchantError when the pageSize is invalid', async () => {
    const spyGetList = spyOn(repository, 'getList').and.callThrough();
    const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();
    const request: ReceiptHistoryRequest = {
      date: '12/03/2024',
      pageNum: 0,
      // @ts-expect-error: testing invalid pageSize
      pageSize: 'a',
    };

    const response = await lastValueFrom(usecase.execute(request));

    expect(response.hasContent).toBeFalse();
    expect(response.content.length).toBe(0);
    expect(response.totalElements).toBe(0);

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);

    expect(spyGetList).toHaveBeenCalledTimes(0);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
  });

  it('should handle error from error response from repository', async () => {
    const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();
    const spyGetList = spyOn(repository, 'getList').and.throwError(
      'error test'
    );
    const request: ReceiptHistoryRequest = {
      pageNum: 0,
      pageSize: 10,
    };

    const response = await lastValueFrom(usecase.execute(request));

    expect(response.hasContent).toBeFalse();
    expect(response.content.length).toBe(0);
    expect(response.totalElements).toBe(0);

    expect(spyLoaderShow).toHaveBeenCalledTimes(1);
    expect(spyLoaderHide).toHaveBeenCalledTimes(1);

    expect(spyGetList).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
  });
});
