import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { GenerateInvoicesUseCase } from '../../../../../app/features/payments/application/usecases/generate-invoices.usecase';
import { InvoiceGenerationResponse } from '../../../../../app/features/payments/domain/entities/payment';
import { PaymentsRepository } from '../../../../../app/features/payments/domain/repositories/payments.repository';

describe('GenerateInvoicesUsecase', () => {
  let usecase: GenerateInvoicesUseCase;
  let repository: jasmine.SpyObj<PaymentsRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;
  let warningNotifierSpy: jasmine.Spy;
  let errorHandlerSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        GenerateInvoicesUseCase,
        {
          provide: PaymentsRepository,
          useValue: jasmine.createSpyObj('PaymentsRepository', [
            'generateInvoices',
          ]),
        },
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
      ],
    });

    usecase = TestBed.inject(GenerateInvoicesUseCase);
    repository = TestBed.inject(
      PaymentsRepository
    ) as jasmine.SpyObj<PaymentsRepository>;
    const loader = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();

    const notifier = TestBed.inject(NotifierService);
    warningNotifierSpy = spyOn(notifier, 'warning').and.callThrough();
    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();

    const errorHandler = TestBed.inject(UseCaseErrorHandler);
    errorHandlerSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GenerateInvoicesUseCase);
  });

  it('should generate invoices and show success and warning notifications', fakeAsync(() => {
    const response: InvoiceGenerationResponse = {
      error: 2,
      excluded: 0,
      invoiced: 0,
      pending: 0,
      processing: 0,
    };

    repository.generateInvoices.and.returnValue(of(response));

    let result: any;

    usecase.execute(new Date()).subscribe({
      next: res => {
        result = res;
      },
      error: fail,
    });

    flush();

    expect(result).toEqual(response);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.generateInvoices).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);
    expect(warningNotifierSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(0);
  }));

  it('should handle error when repository throws an error', fakeAsync(() => {
    repository.generateInvoices.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
          })
      )
    );

    let result: any;

    usecase.execute(new Date()).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    flush();

    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.generateInvoices).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
    expect(warningNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should handle error when parser throws an error', fakeAsync(() => {
    const response: InvoiceGenerationResponse = {
      error: 2,
      excluded: 0,
      invoiced: 0,
      pending: 0,
      processing: 0,
    };
    repository.generateInvoices.and.returnValue(of(response));

    let result: any;

    usecase
      .execute(
        // @ts-expect-error: testing purposes
        null
      )
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.generateInvoices).toHaveBeenCalledTimes(0);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
    expect(warningNotifierSpy).toHaveBeenCalledTimes(0);
  }));
});
