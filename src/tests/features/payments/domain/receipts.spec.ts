import {
  areAllStateSuccess,
  areAllStateTracked,
} from '../../../../app/features/payments/domain/entities/receipts';

describe('Receipts domain', () => {
  describe('areAllStateTracked', () => {
    it('should return false when states is empty', () => {
      expect(areAllStateTracked([]))
        .withContext('Empty states array should return false')
        .toBe(false);
    });

    it('should return false when states is not empty and does not include any tracked state', () => {
      expect(areAllStateTracked(['idle']))
        .withContext('Not including any tracked state should return false')
        .toBe(false);
    });

    it('should return true when states is not empty and includes all tracked states', () => {
      expect(
        areAllStateTracked([
          'uploadError',
          'ready',
          'unknown',
          'processingError',
        ])
      )
        .withContext('Including all tracked states should return true')
        .toBe(true);
    });
  });

  describe('areAllStateSuccess', () => {
    it('should return false when states is empty', () => {
      expect(areAllStateSuccess([]))
        .withContext('Empty states array should return false')
        .toBeFalse();
    });

    it('should return false when states is not empty and does not include any success state', () => {
      expect(areAllStateSuccess(['idle']))
        .withContext('Not including any success state should return false')
        .toBeFalse();
    });

    it('should return true when states is not empty and includes all success states', () => {
      expect(areAllStateSuccess(['ready', 'ready']))
        .withContext('Including all success states should return true')
        .toBeTrue();
    });
  });
});
