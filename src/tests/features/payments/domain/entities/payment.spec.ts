import { toInvoiceGenerationRepositoryRequest } from '../../../../../app/features/payments/domain/entities/payment';

describe('Payment', () => {
  describe('toInvoiceGenerationRepositoryRequest', () => {
    it('should return a valid InvoiceGenerationRepositoryRequest', () => {
      const date = new Date('2021-10-10');
      const result = toInvoiceGenerationRepositoryRequest(date);
      expect(result).toEqual({
        start: '2021-10-10T00:00:00.000Z',
        end: '2021-10-10T23:59:59.999Z',
      });
    });

    it('should throw an error when date is null', () => {
      expect(() =>
        toInvoiceGenerationRepositoryRequest(
          // @ts-expect-error: Testing null
          null
        )
      ).toThrowError('La fecha para generar las facturas no puede ser nula');
    });

    it('should throw an error when date is invalid', () => {
      expect(() =>
        toInvoiceGenerationRepositoryRequest(
          // @ts-expect-error: Testing invalid date
          'invalid date'
        )
      ).toThrowError(
        'La fecha para generar las facturas no es válida. Por favor verifique.'
      );
    });
  });
});
