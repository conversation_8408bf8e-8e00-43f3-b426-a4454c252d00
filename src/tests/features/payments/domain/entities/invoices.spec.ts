import { B2BDateRange } from '@aplazo/merchant/shared';
import {
  fromSummaryUIToRepositoryRequest,
  PaymentInvoicesRepositoryRequest,
} from '../../../../../app/features/payments/domain/entities/invoices';

describe('fromSummaryUIToRepositoryRequest', () => {
  it('should convert valid B2BDateRange to PaymentInvoiceSummaryRequest', () => {
    const daterange: B2BDateRange = {
      startDate: '01/01/2023',
      endDate: '31/12/2023',
    };

    const expected: PaymentInvoicesRepositoryRequest = {
      start: '2023-01-01',
      end: '2023-12-31',
    };

    expect(fromSummaryUIToRepositoryRequest(daterange)).toEqual(expected);
  });

  it('should throw RuntimeMerchantError for invalid startDate', () => {
    const daterange: B2BDateRange = {
      // @ts-expect-error: testing invalid date
      startDate: 'invalid-date',
      endDate: '31/12/2023',
    };

    expect(() => fromSummaryUIToRepositoryRequest(daterange)).toThrowError(
      'La fecha de inicio para consultar resumen de facturas no es válida'
    );
  });

  it('should throw RuntimeMerchantError for invalid endDate', () => {
    const daterange: B2BDateRange = {
      startDate: '01/01/2023',
      // @ts-expect-error: testing invalid date
      endDate: 'invalid-date',
    };

    expect(() => fromSummaryUIToRepositoryRequest(daterange)).toThrowError(
      'La fecha de fin para consultar resumen de facturas no es válida'
    );
  });
});
