import { Observable, of } from 'rxjs';
import { NewMerchantRequest } from '../../../app/features/admin/domain/dtos/new-merchant-request.dto';
import { NewMerchantResponse } from '../../../app/features/admin/domain/dtos/new-merchant-response.dto';
import { NewMerchantRespository } from '../../../app/features/admin/domain/repositories/new-merchant.repository';

export class LocalNewMerchant
  implements
    NewMerchantRespository<NewMerchantRequest, Observable<NewMerchantResponse>>
{
  create(request: NewMerchantRequest): Observable<NewMerchantResponse> {
    console.log('Creating new merchant', request);

    return of({
      id: 1,
      email: request.basic.email,
      status: 'CREATED',
    });
  }
}
