import { defaultAccountInfo } from '../../../../../app/features/admin/application/usecases/get-account-info.usecase';
import { defaultBasicsInfo } from '../../../../../app/features/admin/application/usecases/get-basics-info.usecase';
import { defaultGeneralsInfo } from '../../../../../app/features/admin/application/usecases/get-general-info.usecase';
import { defaultInvoiceInfo } from '../../../../../app/features/admin/application/usecases/get-invoice-info.usecase';
import { defaultMdrInfo } from '../../../../../app/features/admin/application/usecases/get-mdr-info.usecase';
import { MerchantBasicsUI } from '../../../../../app/features/admin/domain/dtos/basics.dto';
import { ContactUI } from '../../../../../app/features/admin/domain/dtos/contact.dto';
import { MerchantGeneralInfoUI } from '../../../../../app/features/admin/domain/dtos/general-info.dto';
import { InvoiceUI } from '../../../../../app/features/admin/domain/dtos/invoice.dto';
import { MdrList } from '../../../../../app/features/admin/domain/dtos/mdr.dto';
import { OperatorsUIList } from '../../../../../app/features/admin/domain/dtos/user.dto';
import { MerchantStoreService } from '../../../../../app/features/admin/infra/services/merchant-store.service';

describe('MerchantStoreService', () => {
  let store: MerchantStoreService;
  const basics: MerchantBasicsUI = {
    merchantId: 1,
    mComInfoId: 1,
    merchantName: 'Merchant',
    status: 'active',
    intType: 'type',
    createdAt: new Date(),
    updatedAt: new Date(),
    error: null,
  };
  const generals: MerchantGeneralInfoUI = {
    merchantId: 1,
    mComInfoId: 1,
    mQuestionnaireId: 1,
    merchantEmail: 'email',
    merchantAddress: 'address',
    merchantWebsite: 'website',
    merchantIndustry: 'industry',
    merchantAov: 'aov',
    merchantRevenue: 'revenue',
    merchantCategory: 'category',
    error: null,
  };

  beforeEach(() => {
    store = new MerchantStoreService();
  });

  it('should be created', () => {
    expect(store).toBeTruthy();
  });

  it('should set selected merchant id', done => {
    let result = 0;

    store.setSelectedMerchantId(1);

    store.selectedMerchantId$.subscribe(value => {
      result = value;
    });

    expect(result).toBe(1);
    done();
  });

  it('should not set selected merchant id if it is the same', done => {
    let result = 0;

    store.setSelectedMerchantId(1);

    store.selectedMerchantId$.subscribe(value => {
      result = value;
    });

    expect(result).toBe(1);

    store.setSelectedMerchantId(1);

    expect(result).toBe(1);
    done();
  });

  it('should set merchant basics', done => {
    let result: MerchantBasicsUI | null = null;

    store.setMerchantBasics(basics);

    store.merchantBasics$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(basics as any);
    done();
  });

  it('should set default empty merchant info when merchant is empty', done => {
    let result: MerchantBasicsUI | null = null;

    store.setMerchantBasics(basics);

    store.merchantBasics$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(basics as any);

    store.setMerchantBasics(null);

    expect(result).toEqual(defaultBasicsInfo as any);
    done();
  });

  it('should not set merchant if merchant is the same', done => {
    let result: MerchantBasicsUI | null = null;

    store.setMerchantBasics(basics);

    store.merchantBasics$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(basics as any);

    store.setMerchantBasics(basics);

    expect(result).toEqual(basics as any);
    done();
  });

  it('should set merchant generals', done => {
    let result: MerchantGeneralInfoUI | null = null;

    store.setMerchantGenerals(generals);

    store.merchantGenerals$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(generals as any);
    done();
  });

  it('should set default empty generals info when generals is empty', done => {
    let result: MerchantGeneralInfoUI | null = null;

    store.setMerchantGenerals(generals);

    store.merchantGenerals$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(generals as any);

    store.setMerchantGenerals(null);

    expect(result).toEqual(defaultGeneralsInfo as any);
    done();
  });

  it('should not set generals if generals is the same', done => {
    let result: MerchantGeneralInfoUI | null = null;

    store.setMerchantGenerals(generals);

    store.merchantGenerals$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(generals as any);

    store.setMerchantGenerals(generals);

    expect(result).toEqual(generals as any);
    done();
  });

  it('should set searchBy', done => {
    let result: any = null;

    store.setSearchBy({ id: 0, name: 'search' });

    store.searchBy$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual({ id: 0, name: 'search' });
    done();
  });

  it('should set searchBy with default values', done => {
    let result: any = null;

    store.setSearchBy(null);

    store.searchBy$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual({ id: 0, name: '' });
    done();
  });

  it('should not set searchBy if searchBy is the same', done => {
    let result: any = null;

    store.setSearchBy({ id: 0, name: 'search' });

    store.searchBy$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual({ id: 0, name: 'search' });

    store.setSearchBy({ id: 0, name: 'search' });

    expect(result).toEqual({ id: 0, name: 'search' });
    done();
  });

  it('should clear searchBy', done => {
    let result: any = null;

    store.setSearchBy({ id: 0, name: 'search' });

    store.searchBy$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual({ id: 0, name: 'search' });

    store.clearSearchBy();

    expect(result).toEqual({ id: 0, name: '' });
    done();
  });

  it('should set merchant account', done => {
    let result: any = null;

    store.setMerchantAccount({
      merchantId: 1,
      token: 'tokenTest',
      bank: 'bankTest',
      bankAccount: 'bankAccountTest',
      email: 'emailAccountTest',
    });

    store.merchantAccount$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual({
      merchantId: 1,
      token: 'tokenTest',
      bank: 'bankTest',
      bankAccount: 'bankAccountTest',
      email: 'emailAccountTest',
    });

    done();
  });

  it('should set default empty merchant account when account is empty', done => {
    let result: any = null;

    store.setMerchantAccount(null);

    store.merchantAccount$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(defaultAccountInfo);

    done();
  });

  it('should not set merchant account if account is the same', done => {
    let result: any = null;

    store.setMerchantAccount({
      merchantId: 1,
      token: 'tokenTest',
      bank: 'bankTest',
      bankAccount: 'bankAccountTest',
      email: 'emailAccountTest',
    });

    store.merchantAccount$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual({
      merchantId: 1,
      token: 'tokenTest',
      bank: 'bankTest',
      bankAccount: 'bankAccountTest',
      email: 'emailAccountTest',
    });

    store.setMerchantAccount({
      merchantId: 1,
      token: 'tokenTest',
      bank: 'bankTest',
      bankAccount: 'bankAccountTest',
      email: 'emailAccountTest',
    });

    expect(result).toEqual({
      merchantId: 1,
      token: 'tokenTest',
      bank: 'bankTest',
      bankAccount: 'bankAccountTest',
      email: 'emailAccountTest',
    });

    done();
  });

  it('should set mdrInfo', done => {
    let result: any = null;
    const test: MdrList = {
      5: {
        merchantId: 1,
        feeOps: 0.05,
        feePct: 0.05,
        id: 1,
        promoFee: 0.045,
        promoFeeEndDate: '2024-01-05T00:00:00',
        promoFeeEndIsoDate: new Date('2024-01-05T00:00:00').toISOString(),
        updatedAt: '2024-01-05T00:00:00',
        installmentFrequency: 5,
        installmentFrequencyId: 3,
      },
    };

    store.setMdrInfo(test);

    store.merchantMdr$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(test);

    done();
  });

  it('should set default empty mdrInfo when mdr is empty', done => {
    let result: any = null;

    store.setMdrInfo(null);

    store.merchantMdr$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(defaultMdrInfo);

    done();
  });

  it('should not set mdrInfo if mdr is the same', done => {
    let result: any = null;
    const test: MdrList = {
      5: {
        merchantId: 1,
        feeOps: 0.05,
        feePct: 0.05,
        id: 1,
        promoFee: 0.045,
        promoFeeEndDate: '2024-01-05T00:00:00',
        promoFeeEndIsoDate: new Date('2024-01-05T00:00:00').toISOString(),
        updatedAt: '2024-01-05T00:00:00',
        installmentFrequency: 5,
        installmentFrequencyId: 3,
      },
    };

    store.setMdrInfo(test);

    store.merchantMdr$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(test);

    store.setMdrInfo(test);

    expect(result).toEqual(test);

    done();
  });

  it('should set merchant contacts', done => {
    let result: any = null;
    const test: ContactUI[] = [
      {
        id: 1,
        email: '<EMAIL>',
        merchantId: 199,
        role: 'role',
        phone: '1234567890',
        businessArea: 'Support',
      },
    ];

    store.setMerchantContacts(test);

    store.merchantContacts$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual({
      merchantId: 199,
      data: test,
    });

    done();
  });

  it('should set default empty merchant contacts when contacts is empty', done => {
    let result: any = null;

    store.setMerchantContacts(null);

    store.merchantContacts$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual({
      merchantId: 0,
      data: [],
    });

    done();
  });

  it('should set default empty merchant contacts when contacts is an empty array', done => {
    let result: any = null;

    store.setMerchantContacts([]);

    store.merchantContacts$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual({
      merchantId: 0,
      data: [],
    });

    done();
  });

  it('should not set merchant contacts if contacts is the same', done => {
    let result: any = null;
    const test: ContactUI[] = [
      {
        id: 1,
        email: '<EMAIL>',
        merchantId: 199,
        role: 'role',
        phone: '1234567890',
        businessArea: 'Support',
      },
    ];

    store.setMerchantContacts(test);

    store.merchantContacts$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual({
      merchantId: 199,
      data: test,
    });

    store.setMerchantContacts(test);

    expect(result).toEqual({
      merchantId: 199,
      data: test,
    });

    done();
  });

  it('should set merchant invoice', done => {
    let result: any = null;
    const test: InvoiceUI = {
      id: 67,
      merchantId: 13,
      rfc: 'rfc',
      email: '<EMAIL>',
      paymentMethod: 'Pago en Una Exhibición (PUE)',
      zipCode: 'zipCode',
      invoiceRegime:
        '612 - Personas Físicas con Actividades Empresariales y Profesionales',
      name: 'name',
      currency: 'MXN',
      paymentForm: '03: Transferencia electrónica de fondos',
      paymentFormCode: 3,
      cfdi: 'G03: Gastos en General',
      cfdiCode: 15,
      facPubGral: false,
    };

    store.setMerchantInvoice(test);

    store.merchantInvoice$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(test);

    done();
  });

  it('should set default empty merchant invoice when invoice is empty', done => {
    let result: any = null;

    store.setMerchantInvoice(null);

    store.merchantInvoice$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(defaultInvoiceInfo);

    done();
  });

  it('should not set merchant invoice if invoice is the same', done => {
    let result: any = null;
    const test: InvoiceUI = {
      id: 67,
      merchantId: 13,
      rfc: 'rfc',
      email: '<EMAIL>',
      paymentMethod: 'Pago en Una Exhibición (PUE)',
      zipCode: 'zipCode',
      invoiceRegime: '601 - General de Ley Personas Morales',
      name: 'name',
      currency: 'MXN',
      paymentForm: '03: Transferencia electrónica de fondos',
      paymentFormCode: 3,
      cfdi: 'G03: Gastos en General',
      cfdiCode: 15,
      facPubGral: false,
    };

    store.setMerchantInvoice(test);

    store.merchantInvoice$.subscribe(value => {
      result = value;
    });

    expect(result).toEqual(test);

    store.setMerchantInvoice(test);

    expect(result).toEqual(test);

    done();
  });

  it('should set merchant branches', done => {
    let result: any = null;
    const test = {
      merchantId: 199,
      data: [
        {
          merchantConfigId: 2,
          branchId: 5,
          created: new Date('2024-05-05T00:00:00'),
          updated: new Date('2024-05-05T00:00:00'),
          deleted: null,
          branchName: 'Chalco 3',
          banned: false,
        },
      ],
    };

    store.merchantBranches$.subscribe(value => {
      console.log('val:::', value);
      result = value;
    });

    store.setBranches(test);

    expect(result).toEqual(test);

    done();
  });

  it('should set default empty merchant branches when branches is empty', done => {
    let result: any = null;

    store.merchantBranches$.subscribe(value => {
      result = value;
    });

    store.setBranches(null);

    expect(result).toEqual({ merchantId: 0, data: [] });

    done();
  });

  it('should not set merchant branches if branches is the same', done => {
    let result: any = null;
    const test = {
      merchantId: 199,
      data: [
        {
          merchantConfigId: 2,
          branchId: 5,
          created: new Date('2024-05-05T00:00:00'),
          updated: new Date('2024-05-05T00:00:00'),
          deleted: null,
          branchName: 'Chalco 3',
          banned: false,
        },
      ],
    };

    store.merchantBranches$.subscribe(value => {
      result = value;
    });

    store.setBranches(test);

    expect(result).toEqual(test);

    store.setBranches(test);

    expect(result).toEqual(test);

    done();
  });

  it('should set merchant users', done => {
    let result: any = null;
    const test: OperatorsUIList = {
      length: 2,
      merchantId: 199,
      data: [
        {
          merchantId: 199,
          idAccount: 20,
          username: 'columbia_centro_venta',
          role: 'Vendedor(a)',
          userStatus: 'Active',
          userEmail: null,
          platform: 'POSUI',
          branches: [
            {
              id: 150,
              name: 'León 8',
            },
          ],
          createdAt: new Date('2023-07-06T23:11:14.009525'),
          updatedAt: new Date('2023-07-06T23:11:14.009525'),
          deletedAt: null,
        },
        {
          merchantId: 199,
          idAccount: 21,
          username: 'columbia_centro_venta_1',
          role: 'Vendedor(a)',
          userStatus: 'Active',
          userEmail: null,
          platform: 'POSUI',
          branches: [
            {
              id: 151,
              name: 'León 8',
            },
          ],
          createdAt: new Date('2023-07-06T23:43:55.912376'),
          updatedAt: new Date('2023-07-06T23:43:55.912376'),
          deletedAt: null,
        },
      ],
    };

    store.merchantUsers$.subscribe(value => {
      result = value;
    });

    store.setUsers(test);

    expect(result).toEqual(test);

    done();
  });

  it('should set default empty merchant users when users is empty', done => {
    let result: any = null;

    store.merchantUsers$.subscribe(value => {
      result = value;
    });

    store.setUsers(null);

    expect(result).toBeNull();

    done();
  });

  it('should not set merchant users if users is the same', done => {
    let result: any = null;
    const test: OperatorsUIList = {
      length: 2,
      merchantId: 199,
      data: [
        {
          merchantId: 199,
          idAccount: 20,
          username: 'columbia_centro_venta',
          role: 'Vendedor(a)',
          userStatus: 'Active',
          userEmail: null,
          platform: 'POSUI',
          branches: [
            {
              id: 150,
              name: 'León 8',
            },
          ],
          createdAt: new Date('2023-07-06T23:11:14.009525'),
          updatedAt: new Date('2023-07-06T23:11:14.009525'),
          deletedAt: null,
        },
        {
          merchantId: 199,
          idAccount: 21,
          username: 'columbia_centro_venta_1',
          role: 'Vendedor(a)',
          userStatus: 'Active',
          userEmail: null,
          platform: 'POSUI',
          branches: [
            {
              id: 151,
              name: 'León 8',
            },
          ],
          createdAt: new Date('2023-07-06T23:43:55.912376'),
          updatedAt: new Date('2023-07-06T23:43:55.912376'),
          deletedAt: null,
        },
      ],
    };

    store.merchantUsers$.subscribe(value => {
      result = value;
    });

    store.setUsers(test);

    expect(result).toEqual(test);

    store.setUsers(test);

    expect(result).toEqual(test);

    done();
  });
});
