import { TestBed } from '@angular/core/testing';
import { NotifierService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { DialogService } from '@ngneat/dialog';
import { of } from 'rxjs';
import { CreateOperatorUseCase } from '../../../../../app/features/admin/application/usecases/new-user.usecase';
import { BranchUI } from '../../../../../app/features/admin/domain/dtos/branch.dto';
import {
  OperatorsUIList,
  OperatorWithPasswordUIDto,
} from '../../../../../app/features/admin/domain/dtos/user.dto';
import { CreateUserWithDialogFormService } from '../../../../../app/features/admin/infra/services/create-user-with-dialog-form.service';
import { MerchantStoreService } from '../../../../../app/features/admin/infra/services/merchant-store.service';

const setup = (args?: {
  store?: {
    merchantId?: number;
    merchantUsers?: OperatorsUIList | null;
    branches?: {
      merchantId: number;
      data: BranchUI[];
    } | null;
  };
  dialogResult?: OperatorWithPasswordUIDto;
}) => {
  const defaultConfig = {
    store: {
      merchantId: 123,
      merchantUsers: {
        data: {
          5: {
            branches: null,
            createdAt: new Date(),
            deletedAt: null,
            idAccount: 123,
            merchantId: 123,
            platform: 'POSUI',
            role: 'Vendedor(a)',
            updatedAt: new Date(),
            userEmail: null,
            username: 'columbia_centro_venta',
            userStatus: 'Active',
          },
        },
        length: 1,
        merchantId: 123,
      } satisfies OperatorsUIList,
      branches: {
        merchantId: 201,
        data: [
          {
            merchantConfigId: 491,
            branchId: 408,
            branchName: 'nuevecita',
            banned: false,
            created: '2024-10-24T01:07:12.180Z',
            updated: '2024-10-24T01:07:12.180Z',
            deleted: null,
          },
          {
            merchantConfigId: 491,
            branchId: 340,
            branchName: 'pos0001',
            banned: false,
            created: '2024-09-25T03:04:47.392Z',
            updated: '2024-09-25T03:04:47.392Z',
            deleted: null,
          },
          {
            merchantConfigId: 491,
            branchId: 339,
            branchName: 'xcsd',
            banned: false,
            created: '2024-09-24T22:57:28.756Z',
            updated: '2024-09-24T22:57:28.756Z',
            deleted: null,
          },
          {
            merchantConfigId: 491,
            branchId: 327,
            branchName: 'Alamos 01',
            banned: false,
            created: '2024-08-22T03:49:36.570Z',
            updated: '2024-10-23T21:36:03.945Z',
            deleted: null,
          },
          {
            merchantConfigId: 491,
            branchId: 326,
            branchName: 'Unshift one',
            banned: false,
            created: '2024-08-22T03:47:29.247Z',
            updated: '2024-08-22T03:47:29.247Z',
            deleted: '2024-08-22T03:47:29.247Z',
          },
          {
            merchantConfigId: 491,
            branchId: 325,
            branchName: 'Tienda with service',
            banned: false,
            created: '2024-08-22T03:45:24.433Z',
            updated: '2024-08-22T03:45:24.433Z',
            deleted: '2024-08-22T03:45:24.433Z',
          },
          {
            merchantConfigId: 491,
            branchId: 324,
            branchName: 'Tienda 04 abc',
            banned: false,
            created: '2024-08-21T23:15:40.461Z',
            updated: '2024-08-22T03:47:46.400Z',
            deleted: '2024-08-21T23:15:40.461Z',
          },
          {
            merchantConfigId: 491,
            branchId: 323,
            branchName: 'tienda 03',
            banned: false,
            created: '2024-08-21T23:09:43.127Z',
            updated: '2024-08-22T02:32:46.371Z',
            deleted: '2024-08-21T23:09:43.127Z',
          },
          {
            merchantConfigId: 491,
            branchId: 322,
            branchName: 'tienda 02',
            banned: false,
            created: '2024-08-21T22:59:59.544Z',
            updated: '2024-08-22T03:50:04.014Z',
            deleted: '2024-08-21T22:59:59.545Z',
          },
          {
            merchantConfigId: 491,
            branchId: 321,
            branchName: 'tienda 01',
            banned: false,
            created: '2024-08-21T22:59:25.020Z',
            updated: '2024-08-22T03:50:09.872Z',
            deleted: '2024-08-21T22:59:25.020Z',
          },
          {
            merchantConfigId: 491,
            branchId: 320,
            branchName: 'The very beginning',
            banned: false,
            created: '2024-08-21T22:45:33.417Z',
            updated: '2024-08-22T03:50:21.596Z',
            deleted: '2024-08-21T22:45:33.417Z',
          },
        ],
      },
    },
    dialogResult: {
      hasConfirmation: false,
    },
  };

  const config = {
    store: {
      selectedMerchantId$: of(
        args?.store?.merchantId ?? defaultConfig.store.merchantId
      ),
      merchantUsers$: of(
        args?.store?.merchantUsers ?? defaultConfig.store.merchantUsers
      ),
      merchantBranches$: of(
        args?.store?.branches ?? defaultConfig.store.branches
      ),
    },
    dialogResult: {
      ...defaultConfig.dialogResult,
      ...args?.dialogResult,
    },
  };

  TestBed.configureTestingModule({
    providers: [
      CreateUserWithDialogFormService,
      provideNotifierTesting(),
      provideUseCaseErrorHandlerTesting(),
      {
        provide: CreateOperatorUseCase,
        useValue: jasmine.createSpyObj('CreateOperatorUseCase', ['execute']),
      },
      {
        provide: MerchantStoreService,
        useValue: {
          selectedMerchantId$: config.store.selectedMerchantId$,
          merchantUsers$: config.store.merchantUsers$,
          merchantBranches$: config.store.merchantBranches$,
          setUsers: () => {
            void 0;
          },
        },
      },
      {
        provide: DialogService,
        useValue: {
          open: () => {
            return {
              afterClosed$: of(config.dialogResult),
            };
          },
        },
      },
    ],
  });

  const usecase = TestBed.inject(CreateUserWithDialogFormService);
  const store = TestBed.inject(MerchantStoreService);
  const notifier = TestBed.inject(NotifierService);
  const dialog = TestBed.inject(DialogService);
  const applicationUsecase = TestBed.inject(
    CreateOperatorUseCase
  ) as jasmine.SpyObj<CreateOperatorUseCase>;
  const errorHandler = TestBed.inject(UseCaseErrorHandler);
  const dialogSpy = spyOn(dialog, 'open').and.callThrough();
  const notifierErrorSpy = spyOn(notifier, 'error').and.callThrough();
  const notifierWarningSpy = spyOn(notifier, 'warning').and.callThrough();
  const notifierInfoSpy = spyOn(notifier, 'info').and.callThrough();
  const errorHandlerSpy = spyOn(errorHandler, 'handle').and.callThrough();

  return {
    usecase,
    store,
    dialogSpy,
    notifierErrorSpy,
    notifierInfoSpy,
    notifierWarningSpy,
    applicationUsecase,
    errorHandlerSpy,
  };
};

describe('CreateUserWithDialogFormService', () => {
  it('should be created', () => {
    const { usecase } = setup();
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(CreateUserWithDialogFormService);
  });

  it('should show a notification and cancel the creation', async () => {
    const { usecase, dialogSpy, notifierInfoSpy } = setup();
    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(notifierInfoSpy).toHaveBeenCalledTimes(1);
    expect(notifierInfoSpy).toHaveBeenCalledWith({
      title: 'Creación cancelada',
    });
  });

  it('should show a notification if the username is empty', async () => {
    const { usecase, dialogSpy, errorHandlerSpy } = setup({
      dialogResult: {
        hasConfirmation: true,
        username: '',
      },
    });

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
  });

  it('should show a notification if the password is empty', async () => {
    const { usecase, dialogSpy, errorHandlerSpy } = setup({
      dialogResult: {
        hasConfirmation: true,
        username: 'test',
        password: '',
      },
    });

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
  });

  it('should show a notification if the email is empty', async () => {
    const { usecase, dialogSpy, errorHandlerSpy } = setup({
      dialogResult: {
        hasConfirmation: true,
        username: 'test',
        password: 'test',
        userEmail: '',
      },
    });

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
  });

  it('should show a notification if the role is empty', async () => {
    const { usecase, dialogSpy, errorHandlerSpy } = setup({
      dialogResult: {
        hasConfirmation: true,
        username: 'test',
        password: 'test',
        userEmail: '<EMAIL>',
        // @ts-expect-error: testing purposes
        role: '',
      },
    });

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
  });

  it('should show a notification if the branchId is empty and platform is pos', async () => {
    const { usecase, dialogSpy, errorHandlerSpy } = setup({
      dialogResult: {
        hasConfirmation: true,
        username: 'test',
        password: 'test',
        userEmail: '<EMAIL>',
        role: 'Vendedor(a)',
        platform: 'POSUI',
        // @ts-expect-error: testing purposes
        branchId: null,
      },
    });

    await usecase.execute();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
  });
});
