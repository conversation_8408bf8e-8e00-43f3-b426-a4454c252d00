import { provideHttpClientTesting } from '@angular/common/http/testing';
import { Component, computed, signal } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { FormControl, FormGroup } from '@angular/forms';
import { provideRouter, Router } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { DialogService } from '@ngneat/dialog';
import { of } from 'rxjs';
import { exitCreationGuard } from '../../../../../app/features/admin/infra/guards/exit-creation.guard';
import {
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';

@Component({
  standalone: true,
  template: '',
})
export class TestComponent {
  testControl = new FormControl('');
  form = new FormGroup({
    test: this.testControl,
  });

  #merchantCreated = signal(false);

  merchantCreated = computed(() => this.#merchantCreated());

  toggleMerchantCreated() {
    this.#merchantCreated.update(val => !val);
  }
}

const setup = async (dialogResult: { confirmation: boolean } | undefined) => {
  TestBed.configureTestingModule({
    imports: [],
    providers: [
      provideRouter([
        {
          path: '',
          component: TestComponent,
          canDeactivate: [exitCreationGuard],
        },
        {
          path: 'success-navigation',
          component: TestComponent,
        },
      ]),
      {
        provide: DialogService,
        useValue: {
          open: () => ({
            afterClosed$: of(dialogResult),
          }),
        },
      },
      provideHttpClient(withInterceptorsFromDi()),
      provideHttpClientTesting(),
    ],
  });

  const routerHarness = await RouterTestingHarness.create();
  const component = await routerHarness.navigateByUrl('/', TestComponent);

  routerHarness.detectChanges();

  const router = TestBed.inject(Router);

  return { routerHarness, component, router };
};

describe('ExitCreationGuard', () => {
  it('should allow navigation if the form is pristine', async () => {
    const { routerHarness, component, router } = await setup(undefined);

    expect(router.url)
      .withContext('The router should be at the root')
      .toBe('/');
    expect(component.form.pristine)
      .withContext('The form should be pristine')
      .toBeTrue();

    await router.navigate(['success-navigation']);

    routerHarness.detectChanges();

    expect(router.url)
      .withContext('The router should navigate to the success-navigation')
      .toBe('/success-navigation');
  });

  it('should allow navigation if the merchant has been created', async () => {
    const { routerHarness, component, router } = await setup(undefined);

    expect(router.url)
      .withContext('The router should be at the root')
      .toBe('/');
    expect(component.form.pristine)
      .withContext('The form should be pristine')
      .toBeTrue();

    component.testControl.markAsDirty();

    component.toggleMerchantCreated();

    await router.navigate(['success-navigation']);

    routerHarness.detectChanges();

    expect(router.url)
      .withContext('The router should navigate to the success-navigation')
      .toBe('/success-navigation');
  });

  it('should allow navigation if the user confirms the exit', async () => {
    const { routerHarness, component, router } = await setup({
      confirmation: true,
    });

    expect(router.url)
      .withContext('The router should be at the root')
      .toBe('/');
    expect(component.form.pristine)
      .withContext('The form should be pristine')
      .toBeTrue();

    component.testControl.markAsDirty();

    await router.navigate(['success-navigation']);

    routerHarness.detectChanges();

    expect(router.url)
      .withContext('The router should navigate to the success-navigation')
      .toBe('/success-navigation');
  });

  it('should not allow navigation if the user cancels the exit', async () => {
    const { routerHarness, component, router } = await setup({
      confirmation: false,
    });

    expect(router.url)
      .withContext('The router should be at the root')
      .toBe('/');
    expect(component.form.pristine)
      .withContext('The form should be pristine')
      .toBeTrue();

    component.testControl.markAsDirty();

    await router.navigate(['success-navigation']);

    routerHarness.detectChanges();

    expect(router.url)
      .withContext('The router should not navigate to the success-navigation')
      .toBe('/');
  });

  it('should not allow navigation if the form is dirty', async () => {
    const { routerHarness, component, router } = await setup(undefined);

    expect(router.url)
      .withContext('The router should be at the root')
      .toBe('/');
    expect(component.form.pristine)
      .withContext('The form should be pristine')
      .toBeTrue();

    component.testControl.markAsDirty();

    await router.navigate(['success-navigation']);

    routerHarness.detectChanges();

    expect(router.url)
      .withContext('The router should not navigate to the success-navigation')
      .toBe('/');
  });
});
