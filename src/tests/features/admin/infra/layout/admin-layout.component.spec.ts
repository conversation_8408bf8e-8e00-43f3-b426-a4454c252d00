import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { Component } from '@angular/core';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { provideRouter, Route, Router, RouterOutlet } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoCommonMessageComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoTabsComponents } from '@aplazo/shared-ui/tabs';
import { provideNgxMask } from 'ngx-mask';
import { of } from 'rxjs';
import { GetBasicsInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-basics-info.usecase';
import { GetBranchesInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-branches-info.usecase';
import { GetGeneralInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-general-info.usecase';
import { SearchByIdOrNameUseCase } from '../../../../../app/features/admin/application/usecases/search-by-id-name.usecase';
import { MerchantBasicInfoRepository } from '../../../../../app/features/admin/domain/repositories/basic-info.repository';
import { MerchantGeneralInfoRepository } from '../../../../../app/features/admin/domain/repositories/general-info.repository';
import { SearchMerchantByRepository } from '../../../../../app/features/admin/domain/repositories/search-merchant-by.repository';
import { CreateOneButtonComponent } from '../../../../../app/features/admin/infra/components/create-one-button.component';
import { SearchByIdOrNameComponent } from '../../../../../app/features/admin/infra/components/search-by-id-name.component';
import { AdminLayoutComponent } from '../../../../../app/features/admin/infra/layout/admin-layout.component';
import { CreateContactWithDialogFormService } from '../../../../../app/features/admin/infra/services/create-contact-with-dialog-form.service';
import { CreateUserWithDialogFormService } from '../../../../../app/features/admin/infra/services/create-user-with-dialog-form.service';
import { MerchantStoreService } from '../../../../../app/features/admin/infra/services/merchant-store.service';
import { UpsertStorefrontWithDialogFormService } from '../../../../../app/features/admin/infra/services/upsert-with-dialog-form.service';
import { ROUTES_CONFIG } from '../../../../../app/features/core/domain/route-names';
import { UserStore } from '../../../../../app/features/login/application/services/user.store';
import { LocalBasicsInfoRepository } from '../../local-basics-info.repository';
import { LocalGeneralsInfoRepository } from '../../local-generals-info.repository';
import { LocalSearchBy } from '../../local-search-by.repository';

@Component({ standalone: true, template: '' })
export class TestComponent {}

const routes: Route[] = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: ROUTES_CONFIG.onboarding,
  },
  {
    path: ROUTES_CONFIG.onboarding,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: ROUTES_CONFIG.merchantAdmin,
      },
      {
        path: ROUTES_CONFIG.merchantAdmin,
        component: AdminLayoutComponent,
        children: [
          {
            path: ROUTES_CONFIG.merchantSettingsSearch,
            pathMatch: 'full',
            component: TestComponent,
          },
          {
            path: ROUTES_CONFIG.merchantSettingsBasics,
            pathMatch: 'full',
            component: TestComponent,
          },
          {
            path: ROUTES_CONFIG.merchantSettingsInfo,
            pathMatch: 'full',
            component: TestComponent,
          },
        ],
      },
    ],
  },
];

describe('AdminLayoutComponent', () => {
  let routerHarness: RouterTestingHarness;
  let component: AdminLayoutComponent;
  let router: Router;
  let store: MerchantStoreService;
  let createContactSpy: jasmine.Spy;
  let createUserSpy: jasmine.Spy;
  let upsertStorefrontSpy: jasmine.Spy;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [
        TestComponent,
        ReactiveFormsModule,
        AplazoButtonComponent,
        SearchByIdOrNameComponent,
        AplazoSimpleTableComponents,
        AplazoCommonMessageComponent,
        AplazoTabsComponents,
        AplazoCardComponent,
        CreateOneButtonComponent,
        RouterOutlet,
        AsyncPipe,
        NgFor,
      ],
      providers: [
        provideNgxMask(),
        SearchByIdOrNameUseCase,
        {
          provide: SearchMerchantByRepository,
          useClass: LocalSearchBy,
        },
        GetBasicsInfoUseCase,
        {
          provide: MerchantBasicInfoRepository,
          useClass: LocalBasicsInfoRepository,
        },
        GetGeneralInfoUseCase,
        {
          provide: MerchantGeneralInfoRepository,
          useClass: LocalGeneralsInfoRepository,
        },
        MerchantStoreService,
        {
          provide: UserStore,
          useValue: {
            roles$: of(['ROLE_CONTROL_TOWER_ADMIN']),
          },
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: UpsertStorefrontWithDialogFormService,
          useValue: {
            execute: async () => {
              void 0;
            },
          },
        },
        {
          provide: CreateUserWithDialogFormService,
          useValue: {
            execute: async () => {
              void 0;
            },
          },
        },
        {
          provide: CreateContactWithDialogFormService,
          useValue: {
            execute: async () => {
              void 0;
            },
          },
        },
        {
          provide: GetBranchesInfoUseCase,
          useValue: {
            execute: () =>
              of({
                merchantId: 201,
                data: [
                  {
                    merchantConfigId: 491,
                    branchId: 408,
                    branchName: 'nuevecita',
                    banned: false,
                    created: '2024-10-24T01:07:12.180Z',
                    updated: '2024-10-24T01:07:12.180Z',
                    deleted: null,
                  },
                  {
                    merchantConfigId: 491,
                    branchId: 340,
                    branchName: 'pos0001',
                    banned: false,
                    created: '2024-09-25T03:04:47.392Z',
                    updated: '2024-09-25T03:04:47.392Z',
                    deleted: null,
                  },
                  {
                    merchantConfigId: 491,
                    branchId: 339,
                    branchName: 'xcsd',
                    banned: false,
                    created: '2024-09-24T22:57:28.756Z',
                    updated: '2024-09-24T22:57:28.756Z',
                    deleted: null,
                  },
                  {
                    merchantConfigId: 491,
                    branchId: 327,
                    branchName: 'Alamos 01',
                    banned: false,
                    created: '2024-08-22T03:49:36.570Z',
                    updated: '2024-10-23T21:36:03.945Z',
                    deleted: null,
                  },
                  {
                    merchantConfigId: 491,
                    branchId: 326,
                    branchName: 'Unshift one',
                    banned: false,
                    created: '2024-08-22T03:47:29.247Z',
                    updated: '2024-08-22T03:47:29.247Z',
                    deleted: '2024-08-22T03:47:29.247Z',
                  },
                  {
                    merchantConfigId: 491,
                    branchId: 325,
                    branchName: 'Tienda with service',
                    banned: false,
                    created: '2024-08-22T03:45:24.433Z',
                    updated: '2024-08-22T03:45:24.433Z',
                    deleted: '2024-08-22T03:45:24.433Z',
                  },
                  {
                    merchantConfigId: 491,
                    branchId: 324,
                    branchName: 'Tienda 04 abc',
                    banned: false,
                    created: '2024-08-21T23:15:40.461Z',
                    updated: '2024-08-22T03:47:46.400Z',
                    deleted: '2024-08-21T23:15:40.461Z',
                  },
                  {
                    merchantConfigId: 491,
                    branchId: 323,
                    branchName: 'tienda 03',
                    banned: false,
                    created: '2024-08-21T23:09:43.127Z',
                    updated: '2024-08-22T02:32:46.371Z',
                    deleted: '2024-08-21T23:09:43.127Z',
                  },
                  {
                    merchantConfigId: 491,
                    branchId: 322,
                    branchName: 'tienda 02',
                    banned: false,
                    created: '2024-08-21T22:59:59.544Z',
                    updated: '2024-08-22T03:50:04.014Z',
                    deleted: '2024-08-21T22:59:59.545Z',
                  },
                  {
                    merchantConfigId: 491,
                    branchId: 321,
                    branchName: 'tienda 01',
                    banned: false,
                    created: '2024-08-21T22:59:25.020Z',
                    updated: '2024-08-22T03:50:09.872Z',
                    deleted: '2024-08-21T22:59:25.020Z',
                  },
                  {
                    merchantConfigId: 491,
                    branchId: 320,
                    branchName: 'The very beginning',
                    banned: false,
                    created: '2024-08-21T22:45:33.417Z',
                    updated: '2024-08-22T03:50:21.596Z',
                    deleted: '2024-08-21T22:45:33.417Z',
                  },
                ],
              }),
          },
        },
        provideRouter(routes),
      ],
    });

    routerHarness = await RouterTestingHarness.create();
    component = await routerHarness.navigateByUrl('/', AdminLayoutComponent);

    routerHarness.fixture.detectChanges();

    router = TestBed.inject(Router);
    store = TestBed.inject(MerchantStoreService);

    createContactSpy = spyOn(
      TestBed.inject(CreateContactWithDialogFormService),
      'execute'
    ).and.callThrough();

    createUserSpy = spyOn(
      TestBed.inject(CreateUserWithDialogFormService),
      'execute'
    ).and.callThrough();

    upsertStorefrontSpy = spyOn(
      TestBed.inject(UpsertStorefrontWithDialogFormService),
      'execute'
    ).and.callThrough();
  });

  it('should be created', async () => {
    expect(component).toBeTruthy();
  });

  it('should display search by id or name component', async () => {
    const searchComponent = await routerHarness.fixture.debugElement.query(
      By.directive(SearchByIdOrNameComponent)
    );

    expect(searchComponent).toBeTruthy();
  });

  it('should not display search by id or name component', fakeAsync(async () => {
    store.setSearchBy({ id: 199, name: '' });

    routerHarness.fixture.detectChanges();

    tick();

    const button = await routerHarness.fixture.debugElement.query(
      By.css(
        'table > tbody > tr > td[aplzsimpletablebodycell] > button[aplzappearance="stroked"]'
      )
    );

    button.nativeElement.click();

    routerHarness.fixture.detectChanges();

    tick();

    const searchComponent = await routerHarness.fixture.debugElement.query(
      By.directive(SearchByIdOrNameComponent)
    );

    expect(searchComponent).toBeFalsy();
  }));

  it('should execute setSearchBy when search control change value', () => {
    const searchBySpy = spyOn(store, 'setSearchBy');

    component.searchBy.setValue({ id: 199, name: '' });

    routerHarness.fixture.detectChanges();

    expect(searchBySpy)
      .withContext('setSearchBy should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should trigger router navigation when changeTab is called', () => {
    const navigateSpy = spyOn(router, 'navigate').and.callThrough();

    component.changeTab({ index: 1 });

    routerHarness.fixture.detectChanges();

    expect(navigateSpy)
      .withContext('navigate should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should change currentTabIdx when changeTab is called', () => {
    component.changeTab({ index: 2 });

    expect(component.currentTabIdx())
      .withContext('currentTabIdx should be changed when changeTab is called')
      .toBe(2);
  });

  it('should clear merchant store and navigate to search route when clearMerchantSelection is called', () => {
    const clearAllSpy = spyOn(store, 'clearAll').and.callThrough();
    const navigateSpy = spyOn(router, 'navigate').and.callThrough();

    component.clearMerchantSelection();

    expect(clearAllSpy)
      .withContext('clearAll should be called')
      .toHaveBeenCalledTimes(1);

    expect(navigateSpy)
      .withContext('navigate should be called')
      .toHaveBeenCalledTimes(1);

    expect(navigateSpy)
      .withContext('navigate should be called with search route')
      .toHaveBeenCalledWith([ROUTES_CONFIG.merchantSettingsBasics], {
        relativeTo: component.route,
      });
  });

  it('should call create storefront use case when createOneStoreFront is called', () => {
    component.createOneStoreFront();

    expect(upsertStorefrontSpy)
      .withContext('createOneStoreFront should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should call create user use case when createOneOperator is called', () => {
    component.createOneOperator();

    expect(createUserSpy)
      .withContext('createOneOperator should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should call create contact use case when createOneContact is called', () => {
    component.createOneContact();

    expect(createContactSpy)
      .withContext('createOneContact should be called')
      .toHaveBeenCalledTimes(1);
  });
});
