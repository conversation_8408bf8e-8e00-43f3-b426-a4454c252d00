import { Component, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { SearchByIdOrNameComponent } from '../../../../../app/features/admin/infra/components/search-by-id-name.component';

@Component({
  template: `
    <app-search-by-id-name
      [formControl]="searchControl"></app-search-by-id-name>
  `,
  imports: [ReactiveFormsModule, SearchByIdOrNameComponent],
})
class HostComponent {
  searchControl = new FormControl<{ id: number; name: string } | null>(null);
}

describe('SearchByIdOrNameComponent', () => {
  let hostFixture: ComponentFixture<HostComponent>;
  let hostComponent: HostComponent;
  let searchByIdNameComponent: DebugElement;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        SearchByIdOrNameComponent,
        HostComponent,
        AplazoFormFieldDirectives,
        NgxMaskDirective,
      ],
      providers: [provideNgxMask()],
    });

    hostFixture = TestBed.createComponent(HostComponent);
    hostComponent = hostFixture.componentInstance;

    hostFixture.detectChanges();

    searchByIdNameComponent = hostFixture.debugElement.query(
      By.directive(SearchByIdOrNameComponent)
    );
  });

  it('should create a host component with SearchByIdOrNameComponent', () => {
    expect(searchByIdNameComponent).toBeTruthy();
  });

  it('should reflect changes in currentControl when merchantId is updated', () => {
    const merchantId = searchByIdNameComponent.query(By.css('#merchantId'));

    merchantId.nativeElement.value = 123;

    merchantId.nativeElement.dispatchEvent(new Event('input'));

    hostFixture.detectChanges();

    searchByIdNameComponent
      .query(By.css('button[type="submit"]'))
      .nativeElement.click();

    hostFixture.detectChanges();
    expect(hostComponent.searchControl.value).toEqual({ id: 123, name: '' });
  });

  it('should reflect changes in currentControl when merchantName is updated', () => {
    const merchantName = searchByIdNameComponent.query(By.css('#merchantName'));

    merchantName.nativeElement.value = 'Merchant Name';

    merchantName.nativeElement.dispatchEvent(new Event('input'));

    hostFixture.detectChanges();

    searchByIdNameComponent
      .query(By.css('button[type="submit"]'))
      .nativeElement.click();

    hostFixture.detectChanges();
    expect(hostComponent.searchControl.value).toEqual({
      id: 0,
      name: 'Merchant Name',
    });
  });

  it('should clear merchantId when merchantName is updated', () => {
    const merchantId = searchByIdNameComponent.query(By.css('#merchantId'));
    const merchantName = searchByIdNameComponent.query(By.css('#merchantName'));

    merchantId.nativeElement.value = 123;
    merchantId.nativeElement.dispatchEvent(new Event('input'));
    hostFixture.detectChanges();

    merchantName.nativeElement.value = 'Merchant Name';
    merchantName.nativeElement.dispatchEvent(new Event('input'));
    hostFixture.detectChanges();

    expect(searchByIdNameComponent.componentInstance.merchantId.value).toBe(
      null
    );
  });

  it('should clear merchantName when merchantId is updated', () => {
    const merchantId = searchByIdNameComponent.query(By.css('#merchantId'));
    const merchantName = searchByIdNameComponent.query(By.css('#merchantName'));

    merchantName.nativeElement.value = 'Merchant Name';
    merchantName.nativeElement.dispatchEvent(new Event('input'));
    hostFixture.detectChanges();

    merchantId.nativeElement.value = 123;
    merchantId.nativeElement.dispatchEvent(new Event('input'));
    hostFixture.detectChanges();

    expect(searchByIdNameComponent.componentInstance.merchantName.value).toBe(
      ''
    );
  });
});
