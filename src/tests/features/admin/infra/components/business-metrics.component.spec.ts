import { Component } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  ControlContainer,
  FormGroup,
  ReactiveFormsModule,
} from '@angular/forms';
import { By } from '@angular/platform-browser';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoButtonGroupComponent } from '@aplazo/shared-ui/button-group';
import { AplazoSelectComponent } from '@aplazo/shared-ui/forms';
import { BusinessMetricsComponent } from '../../../../../app/features/admin/infra/components/business-metrics.component';

@Component({
  imports: [ReactiveFormsModule, BusinessMetricsComponent],
  template: ` <form [formGroup]="form" (ngSubmit)="submit()">
    <app-business-metrics></app-business-metrics>
  </form>`,
})
export class TestComponent {
  form = new FormGroup({});

  submit() {
    console.log('submit');
    void 0;
  }
}

describe('BusinessMetricsComponent', () => {
  let fixture: ComponentFixture<TestComponent>;
  let component: TestComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, TestComponent],
      providers: [ControlContainer],
    }).compileComponents();

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component)
      .withContext('Component has been created returns true')
      .toBeTruthy();
  });

  it('should have 2 ui select components', () => {
    const selects = fixture.debugElement.queryAll(
      By.directive(AplazoSelectComponent)
    );

    expect(selects.length)
      .withContext('Select components length should be 2')
      .toBe(2);
  });

  it('should have 2 button group components', () => {
    const buttonGroups = fixture.debugElement.queryAll(
      By.directive(AplazoButtonGroupComponent)
    );

    expect(buttonGroups.length)
      .withContext('Button group components length should be 2')
      .toBe(2);
  });

  it('should show error when click next step button but no industry selected', () => {
    const button = fixture.debugElement.query(
      By.css('button[aplzappearance="solid"]')
    );
    const businessComp = fixture.debugElement.query(
      By.directive(BusinessMetricsComponent)
    );
    const nextSpy = spyOn(
      businessComp.componentInstance,
      'next'
    ).and.callThrough();

    expect(button)
      .withContext('Next step button should be defined')
      .toBeDefined();

    button.nativeElement.click();

    fixture.detectChanges();

    const error = fixture.debugElement.query(
      By.css('#industry-required-error')
    );

    expect(error)
      .withContext('Industry required error should be shown')
      .toBeTruthy();
    expect(nextSpy)
      .withContext('Next method should be called once')
      .toHaveBeenCalledTimes(1);
  });

  it('should reflect value correctly in the parent form', () => {
    const [industrySelect, integrationSelect] = fixture.debugElement.queryAll(
      By.directive(AplazoSelectComponent)
    );

    const openIndustryOptions = industrySelect.query(
      By.directive(AplazoButtonComponent)
    );
    openIndustryOptions.nativeElement.click();
    fixture.detectChanges();

    const industryOptions = industrySelect.queryAll(
      By.css('li.aplazo-dropdown__item')
    );
    const toysLabel = industryOptions[4];
    const clickableToys = toysLabel.query(By.css('label'));
    clickableToys.nativeElement.click();
    fixture.detectChanges();

    const openIntegrationOptions = integrationSelect.query(
      By.directive(AplazoButtonComponent)
    );
    openIntegrationOptions.nativeElement.click();
    fixture.detectChanges();

    const integrationOptions = integrationSelect.queryAll(
      By.css('li.aplazo-dropdown__item')
    );
    const clickableProscai = integrationOptions[2].query(By.css('label'));
    clickableProscai.nativeElement.click();
    fixture.detectChanges();

    expect(component.form.value)
      .withContext('Form value should be reflected correctly')
      .toEqual({
        'business-metrics': {
          industry: 'Juguetes y Hobbies',
          averageTicket: '800$ - 3,000$',
          salesVolume: '0 - $25M',
          integrationType: 'PROSCAI',
        },
      });
  });

  it('should call back method when click back button', () => {
    const button = fixture.debugElement.query(
      By.css('button[aplzappearance="stroked"]')
    );
    const businessComp = fixture.debugElement.query(
      By.directive(BusinessMetricsComponent)
    );
    const backSpy = spyOn(
      businessComp.componentInstance,
      'back'
    ).and.callThrough();

    expect(button).withContext('Back button should be defined').toBeDefined();

    button.nativeElement.click();

    fixture.detectChanges();

    expect(backSpy)
      .withContext('Back method should be called once')
      .toHaveBeenCalledTimes(1);
  });
});
