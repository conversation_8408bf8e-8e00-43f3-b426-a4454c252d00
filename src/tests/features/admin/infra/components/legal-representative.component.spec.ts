import { Component } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  ControlContainer,
  FormGroup,
  ReactiveFormsModule,
} from '@angular/forms';
import { By } from '@angular/platform-browser';
import { AplazoFormFieldComponent } from '@aplazo/shared-ui/forms';
import { LegalRepresentativeComponent } from '../../../../../app/features/admin/infra/components/legal-representative.component';

@Component({
  imports: [ReactiveFormsModule, LegalRepresentativeComponent],
  template: ` <form [formGroup]="form" (ngSubmit)="submit()">
    <app-legal-representative></app-legal-representative>
  </form>`,
})
export class TestComponent {
  form = new FormGroup({});

  submit() {
    console.log('submit');
    void 0;
  }
}

describe('LegalRepresentativeComponent', () => {
  let fixture: ComponentFixture<TestComponent>;
  let component: TestComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, TestComponent],
      providers: [ControlContainer],
    });

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component)
      .withContext('Component has been created returns true')
      .toBeTruthy();
  });

  it('should have 3 form fields', () => {
    const formFields = fixture.debugElement.queryAll(
      By.directive(AplazoFormFieldComponent)
    );

    expect(formFields.length)
      .withContext('Form fields length should be 3')
      .toBe(3);
  });

  it('should containt a legal-representative group within the parent formGroup', () => {
    expect(component.form.value)
      .withContext('Form value should contain a legal-representative group')
      .toEqual({
        'legal-representative': { name: '', position: '', phone: '' },
      });
  });

  it('should show errors when click next step button without filling the form', () => {
    const button = fixture.debugElement.query(
      By.css('button[aplzappearance="solid"]')
    );
    const legalComp = fixture.debugElement.query(
      By.directive(LegalRepresentativeComponent)
    );
    const nextSpy = spyOn(
      legalComp.componentInstance,
      'next'
    ).and.callThrough();

    expect(button)
      .withContext('Should contain one only button with solid appearance')
      .toBeTruthy();

    button.nativeElement.click();

    fixture.detectChanges();

    const inputs = fixture.debugElement.queryAll(By.css('input'));

    const inputsHasErrorClass = inputs
      .map(input => input.nativeElement)
      .map(i => i.classList.contains('ng-invalid'))
      .every(Boolean);

    const errorMessagesContainer = fixture.debugElement.queryAll(
      By.css('.aplazo-form-field__message')
    );
    const errorMessagesContainerHasErrorClass = errorMessagesContainer
      .map(error => error.nativeElement)
      .map(e => e.classList.contains('error'))
      .every(Boolean);

    expect(inputsHasErrorClass)
      .withContext('All inputs should have ng-invalid class')
      .toBeTrue();
    expect(errorMessagesContainerHasErrorClass)
      .withContext('All error messages should have error class')
      .toBeTrue();
    expect(nextSpy)
      .withContext('Next method should be called once')
      .toHaveBeenCalledTimes(1);
  });

  it('should reflect value correctly in the parent form', () => {
    const button = fixture.debugElement.query(
      By.css('button[aplzappearance="solid"]')
    );
    const nameInput = fixture.debugElement.query(
      By.css('input[formControlName="name"]')
    );
    const positionInput = fixture.debugElement.query(
      By.css('input[formControlName="position"]')
    );
    const phoneInput = fixture.debugElement.query(
      By.css('input[formControlName="phone"]')
    );
    const legalComp = fixture.debugElement.query(
      By.directive(LegalRepresentativeComponent)
    );
    const nextSpy = spyOn(
      legalComp.componentInstance,
      'next'
    ).and.callThrough();

    nameInput.nativeElement.value = 'John Doe';
    nameInput.nativeElement.dispatchEvent(new Event('input'));

    positionInput.nativeElement.value = 'CEO';
    positionInput.nativeElement.dispatchEvent(new Event('input'));

    phoneInput.nativeElement.value = '1234567890';
    phoneInput.nativeElement.dispatchEvent(new Event('input'));

    fixture.detectChanges();

    button.nativeElement.click();

    fixture.detectChanges();

    expect(component.form.value)
      .withContext('Form value should reflect the input values')
      .toEqual({
        'legal-representative': {
          name: 'John Doe',
          position: 'CEO',
          phone: '1234567890',
        },
      });
    expect(nextSpy)
      .withContext('Next method should be called once')
      .toHaveBeenCalledTimes(1);
  });
});
