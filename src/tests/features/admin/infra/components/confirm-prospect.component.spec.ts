import { Component } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { DialogRef } from '@ngneat/dialog';
import { ConfirmProspectComponent } from '../../../../../app/features/admin/infra/components/confirm-prospect.component';

@Component({
  imports: [ReactiveFormsModule, ConfirmProspectComponent],
  template: ` <form [formGroup]="form" (ngSubmit)="submit()">
    <app-confirm-prospect></app-confirm-prospect>
  </form>`,
})
export class TestComponent {
  form = new FormGroup({});

  submit() {
    console.log('submit');
    void 0;
  }
}

describe('ConfirmProspectComponent', () => {
  let fixture: ComponentFixture<TestComponent>;
  let component: TestComponent;
  let dialog: DialogRef;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, TestComponent],
      providers: [
        {
          provide: DialogRef,
          useValue: {
            close: () => {
              void 0;
            },
            data: {
              'business-metrics': {
                industry: 'industry',
                integrationType: 'integrationType',
                averageTicket: 'averageTicket',
                salesVolume: 'salesVolume',
              },
              'general-info': {
                merchantName: 'merchantName',
                email: 'email',
                webPage: 'webPage',
              },
              'legal-representative': {
                name: 'name',
                position: 'position',
                phone: '**********',
              },
            },
          },
        },
      ],
    });

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();

    dialog = TestBed.inject(DialogRef);
  });

  it('should be created', () => {
    expect(component)
      .withContext('Component has been created returns true')
      .toBeTruthy();
  });

  it('should call close method from dialogRef with confirm true', () => {
    const closeSpy = spyOn(dialog, 'close').and.callThrough();
    const button = fixture.debugElement.query(
      By.css('button[aplzappearance="solid"]')
    );

    button.nativeElement.click();

    expect(closeSpy)
      .withContext('Close method from the dialogRef should be called once')
      .toHaveBeenCalledTimes(1);

    expect(closeSpy)
      .withContext(
        'Close method from the dialogRef should be called with confirm true'
      )
      .toHaveBeenCalledWith({ confirm: true });
  });

  it('should call close method from dialogRef with confirm false', () => {
    const closeSpy = spyOn(dialog, 'close').and.callThrough();
    const button = fixture.debugElement.query(
      By.css('button[aplzappearance="stroked"]')
    );

    button.nativeElement.click();

    expect(closeSpy)
      .withContext('Close method from the dialogRef should be called once')
      .toHaveBeenCalledTimes(1);

    expect(closeSpy)
      .withContext(
        'Close method from the dialogRef should be called with confirm false'
      )
      .toHaveBeenCalledWith({ confirm: false });
  });
});
