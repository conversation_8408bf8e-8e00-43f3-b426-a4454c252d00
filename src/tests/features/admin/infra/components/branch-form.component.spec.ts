import { TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { AplazoTrimSpacesDirective } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import { BranchFormComponent } from '../../../../../app/features/admin/infra/components/branch-form.component';

const def = {
  data: {
    name: 'Storefront',
    id: 1,
    active: true,
  },
  refReturn: {
    hasConfirmation: false,
  },
};

const setup = (config?: { data?: any; refReturn?: any }) => {
  const s = {
    data: {
      ...def.data,
      ...config?.data,
    },
    refReturn: {
      ...def.refReturn,
      ...config?.refReturn,
    },
  };

  TestBed.configureTestingModule({
    imports: [
      AplazoCardComponent,
      AplazoButtonComponent,
      AplazoFormFieldDirectives,
      AplazoTrimSpacesDirective,
      ReactiveFormsModule,
    ],
    providers: [
      {
        provide: DialogRef,
        useValue: {
          data: s.data,
          close: () => {
            return s.refReturn;
          },
        },
      },
    ],
  });

  const fixture = TestBed.createComponent(BranchFormComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  const dialogRef = TestBed.inject(DialogRef);

  return { fixture, component, dialogRef };
};

describe('BranchFormComponent', () => {
  it('should be created', () => {
    const { component } = setup();
    expect(component).toBeTruthy();
  });

  it('should close dialog', () => {
    const { component, dialogRef } = setup();

    spyOn(dialogRef, 'close');

    component.close();

    expect(dialogRef.close).toHaveBeenCalledWith({ hasConfirmation: false });
  });

  it('should create branch', () => {
    const { fixture, component, dialogRef } = setup();

    spyOn(dialogRef, 'close');

    component.name.setValue('test');
    component.name.markAsDirty();

    fixture.detectChanges();

    component.create();

    fixture.detectChanges();

    expect(dialogRef.close).toHaveBeenCalledWith({
      hasConfirmation: true,
      name: 'test',
      id: 1,
      active: true,
    });
  });
});
