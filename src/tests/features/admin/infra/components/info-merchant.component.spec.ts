import { Component } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  ControlContainer,
  FormGroup,
  ReactiveFormsModule,
} from '@angular/forms';
import { By } from '@angular/platform-browser';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldComponent } from '@aplazo/shared-ui/forms';
import { InfoMerchantComponent } from '../../../../../app/features/admin/infra/components/info-merchant.component';

@Component({
  imports: [ReactiveFormsModule, InfoMerchantComponent],
  template: ` <form [formGroup]="form" (ngSubmit)="submit()">
    <app-merchant-info></app-merchant-info>
  </form>`,
})
export class TestComponent {
  form = new FormGroup({});

  submit() {
    console.log('submit');
    void 0;
  }
}

describe('InfoMerchantComponent', () => {
  let fixture: ComponentFixture<TestComponent>;
  let component: TestComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, TestComponent],
      providers: [ControlContainer],
    });

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component)
      .withContext('Component has been created returns true')
      .toBeTruthy();
  });

  it('should have 4 form fields', () => {
    const formFields = fixture.debugElement.queryAll(
      By.directive(AplazoFormFieldComponent)
    );

    expect(formFields.length)
      .withContext('Form fields length should be 4')
      .toBe(4);
  });

  it('should contain a general-info group within the parent FormGroup', () => {
    expect(component.form.value)
      .withContext(
        'Form should contain a general-info group within the parent FormGroup'
      )
      .toEqual({
        'general-info': {
          email: '',
          merchantName: '',
          webPage: '',
          address: '',
        },
      });
  });

  it('should show errors when click next step button but no values are provided', () => {
    const button = fixture.debugElement.query(
      By.directive(AplazoButtonComponent)
    );
    const infoComp = fixture.debugElement.query(
      By.directive(InfoMerchantComponent)
    );
    const nextSpy = spyOn(infoComp.componentInstance, 'next').and.callThrough();

    button.nativeElement.click();

    fixture.detectChanges();

    const inputs = fixture.debugElement.queryAll(By.css('input'));

    const inputsHasErrorClass = inputs
      .map(input => input.nativeElement)
      .map(i => i.classList.contains('ng-invalid'))
      .every(Boolean);

    const errorMessagesContainer = fixture.debugElement.queryAll(
      By.css('.aplazo-form-field__message')
    );

    const errorMessageContainerHasErrorClass = errorMessagesContainer
      .map(container => container.nativeElement)
      .map(c => c.classList.contains('error'))
      .every(Boolean);

    expect(inputsHasErrorClass)
      .withContext('All inputs should have ng-invalid class')
      .toBeTrue();

    expect(errorMessageContainerHasErrorClass)
      .withContext('All error messages container should have error class')
      .toBeTrue();
    expect(nextSpy)
      .withContext('Next method should be called once')
      .toHaveBeenCalledTimes(1);
  });

  it('should reflect value correctly in the parent component', () => {
    const nameInput = fixture.debugElement.query(
      By.css('input[formControlName="merchantName"]')
    );
    const emailInput = fixture.debugElement.query(
      By.css('input[formControlName="email"]')
    );
    const webPageInput = fixture.debugElement.query(
      By.css('input[formControlName="webPage"]')
    );
    const addressInput = fixture.debugElement.query(
      By.css('input[formControlName="address"]')
    );
    const button = fixture.debugElement.query(
      By.directive(AplazoButtonComponent)
    );
    const infoComp = fixture.debugElement.query(
      By.directive(InfoMerchantComponent)
    );
    const nextSpy = spyOn(infoComp.componentInstance, 'next').and.callThrough();

    nameInput.nativeElement.value = 'Aplazo';
    nameInput.nativeElement.dispatchEvent(new Event('input'));
    fixture.detectChanges();

    emailInput.nativeElement.value = '<EMAIL>';
    emailInput.nativeElement.dispatchEvent(new Event('input'));
    fixture.detectChanges();

    webPageInput.nativeElement.value = 'www.aplazo.mx';
    webPageInput.nativeElement.dispatchEvent(new Event('input'));
    fixture.detectChanges();

    addressInput.nativeElement.value = 'Calle 123';
    addressInput.nativeElement.dispatchEvent(new Event('input'));
    fixture.detectChanges();

    button.nativeElement.click();

    fixture.detectChanges();

    expect(component.form.value)
      .withContext('Form should contain the values provided')
      .toEqual({
        'general-info': {
          merchantName: 'Aplazo',
          email: '<EMAIL>',
          webPage: 'www.aplazo.mx',
          address: 'Calle 123',
        },
      });
    expect(nextSpy)
      .withContext('Next method should be called once')
      .toHaveBeenCalledTimes(1);
  });
});
