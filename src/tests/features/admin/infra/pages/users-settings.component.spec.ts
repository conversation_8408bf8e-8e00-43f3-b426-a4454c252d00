import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoCommonMessageComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { of } from 'rxjs';
import { GetUsersInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-users-info.usecase';
import { MerchantOperatorsRepository } from '../../../../../app/features/admin/domain/repositories/users-info.repository';
import { MerchantUsersSettingsComponent } from '../../../../../app/features/admin/infra/pages/users/users-settings.component';
import { UpdateUserWithDialogFormService } from '../../../../../app/features/admin/infra/services/edit-user-with-dialog-form.service';
import { MerchantStoreService } from '../../../../../app/features/admin/infra/services/merchant-store.service';
import { UserStore } from '../../../../../app/features/login/application/services/user.store';

describe('Users Settings Component', () => {
  let fixture: ComponentFixture<MerchantUsersSettingsComponent>;
  let component: MerchantUsersSettingsComponent;
  let store: MerchantStoreService;

  let repositorySpy: jasmine.SpyObj<MerchantOperatorsRepository>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        AplazoCardComponent,
        AplazoSimpleTableComponents,
        AplazoButtonComponent,
        AplazoCommonMessageComponent,
        AplazoDynamicPipe,
        AplazoIconComponent,
        AsyncPipe,
        NgClass,
        NgFor,
      ],
      providers: [
        AplazoIconRegistryService,
        MerchantStoreService,
        GetUsersInfoUseCase,
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        {
          provide: MerchantOperatorsRepository,
          useValue: jasmine.createSpyObj('MerchantUsersInfoRepository', [
            'getInfo',
          ]),
        },
        {
          provide: UserStore,
          useValue: {
            roles$: of(['ROLE_CONTROL_TOWER_ADMIN']),
          },
        },
        {
          provide: UpdateUserWithDialogFormService,
          useValue: jasmine.createSpyObj('UpdateUserWithDialogFormService', [
            'execute',
          ]),
        },
      ],
    });

    fixture = TestBed.createComponent(MerchantUsersSettingsComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();

    store = TestBed.inject(MerchantStoreService);
    repositorySpy = TestBed.inject(
      MerchantOperatorsRepository
    ) as jasmine.SpyObj<MerchantOperatorsRepository>;
  });

  afterEach(() => {
    store.clearAll();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should call setInfo on init', () => {
    const setInfoSpy = spyOn(component, 'setInfo');

    component.ngOnInit();

    expect(setInfoSpy)
      .withContext('setInfo should be called on init')
      .toHaveBeenCalledTimes(1);
  });

  it('should not call usecase execute if merchantId is not set', async () => {
    const usecaseSpy = spyOn(
      TestBed.inject(GetUsersInfoUseCase),
      'execute'
    ).and.callThrough();

    await component.setInfo();

    expect(usecaseSpy)
      .withContext('usecase execute should not be called')
      .toHaveBeenCalledTimes(0);
  });

  it('should set users if merchantId is set', async () => {
    repositorySpy.getInfo.and.returnValue(
      of([
        {
          idAccount: 20,
          username: 'columbia_centro_venta',
          role: 'ROLE_SELL_AGENT',
          userStatus: 'Active',
          userEmail: null,
          platform: 'POSUI',
          branches: [
            {
              id: 150,
              name: 'León 8',
            },
          ],
          createdAt: '2023-07-06T23:11:14.009525',
          updatedAt: '2023-07-06T23:11:14.009525',
          deletedAt: null,
        },
        {
          idAccount: 21,
          username: 'columbia_centro_venta_1',
          role: 'ROLE_SELL_AGENT',
          userStatus: 'Active',
          userEmail: null,
          platform: 'POSUI',
          branches: [
            {
              id: 151,
              name: 'León 8',
            },
          ],
          createdAt: '2023-07-06T23:43:55.912376',
          updatedAt: '2023-07-06T23:43:55.912376',
          deletedAt: null,
        },
      ])
    );
    const usecaseSpy = spyOn(
      TestBed.inject(GetUsersInfoUseCase),
      'execute'
    ).and.callThrough();

    store.setSelectedMerchantId(8);

    await component.setInfo();

    expect(usecaseSpy)
      .withContext('usecase execute should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should not call usecase execute if branches are already set for the merchantId', async () => {
    repositorySpy.getInfo.and.returnValue(of([]));
    const usecaseSpy = spyOn(
      TestBed.inject(GetUsersInfoUseCase),
      'execute'
    ).and.callThrough();

    store.setSelectedMerchantId(199);
    store.setUsers({
      merchantId: 199,
      length: 1,
      data: [
        {
          merchantId: 199,
          idAccount: 20,
          username: 'columbia_centro_venta',
          role: 'Vendedor(a)',
          userStatus: 'Active',
          userEmail: null,
          platform: 'POSUI',
          branches: [
            {
              id: 150,
              name: 'León 8',
            },
          ],
          createdAt: new Date('2023-07-06T23:11:14.009525'),
          updatedAt: new Date('2023-07-06T23:11:14.009525'),
          deletedAt: null,
        },
      ],
    });

    await component.setInfo();

    fixture.detectChanges();

    expect(usecaseSpy)
      .withContext('usecase execute should not be called')
      .toHaveBeenCalledTimes(0);
  });
});
