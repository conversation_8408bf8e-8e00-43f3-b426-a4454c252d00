import { AsyncPipe } from '@angular/common';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { of } from 'rxjs';
import {
  defaultInvoiceInfo,
  GetInvoiceInfoUseCase,
} from '../../../../../app/features/admin/application/usecases/get-invoice-info.usecase';
import { UpdateInvoiceInfoUsecase } from '../../../../../app/features/admin/application/usecases/update-invoice-info.usecase';
import { InvoiceUI } from '../../../../../app/features/admin/domain/dtos/invoice.dto';
import { MerchantInvoiceInfoRepository } from '../../../../../app/features/admin/domain/repositories/invoice-info.repository';
import { MerchantBillingSettingsComponent } from '../../../../../app/features/admin/infra/pages/billing/billing-settings.component';
import { MerchantStoreService } from '../../../../../app/features/admin/infra/services/merchant-store.service';
import { UserStore } from '../../../../../app/features/login/application/services/user.store';

const invoiceUI: InvoiceUI = {
  id: 1,
  merchantId: 199,
  name: 'Aplazo',
  rfc: 'CCO8605231N4',
  email: '<EMAIL>',
  paymentMethod: 'Pago en Una Exhibición (PUE)',
  zipCode: '39924',
  invoiceRegime:
    '612 - Personas Físicas con Actividades Empresariales y Profesionales',
  currency: 'MXN',
  paymentForm: '03: Transferencia electrónica de fondos',
  paymentFormCode: 3,
  cfdi: 'G03: Gastos en General',
  cfdiCode: 15,
  facPubGral: false,
};

const setup = (config?: {
  store?: {
    merchantId: number;
    invoice: InvoiceUI;
  };
  userStore?: {
    roles: string[];
  };
}) => {
  const defaultConf = {
    store: {
      merchantId: 0,
      invoice: defaultInvoiceInfo,
    },
    userStore: {
      roles: ['ROLE_CONTROL_TOWER_ADMIN'],
    },
  };

  const finalConfig = {
    store: {
      merchantId: config?.store?.merchantId || defaultConf.store.merchantId,
      invoice: config?.store?.invoice || defaultConf.store.invoice,
    },
    userStore: {
      roles: config?.userStore?.roles || defaultConf.userStore.roles,
    },
  };

  TestBed.configureTestingModule({
    imports: [
      MerchantBillingSettingsComponent,
      AsyncPipe,
      ReactiveFormsModule,
      AplazoButtonComponent,
      AplazoFormFieldDirectives,
      AplazoCardComponent,
    ],
    providers: [
      provideLoaderTesting(),
      provideUseCaseErrorHandlerTesting(),
      provideNotifierTesting(),
      {
        provide: MerchantStoreService,
        useValue: {
          selectedMerchantId: finalConfig.store.merchantId,
          selectedMerchantId$: of(finalConfig.store.merchantId),
          merchantInvoice$: of(finalConfig.store.invoice),
          setMerchantInvoice: () => {
            void 0;
          },
        },
      },
      {
        provide: MerchantInvoiceInfoRepository,
        useValue: jasmine.createSpyObj('MerchantInvoiceInfoRepository', [
          'getInfo',
        ]),
      },
      {
        provide: GetInvoiceInfoUseCase,
        useValue: jasmine.createSpyObj('GetInvoiceInfoUseCase', ['execute']),
      },
      {
        provide: UserStore,
        useValue: {
          roles$: of(finalConfig.userStore.roles),
        },
      },
      {
        provide: UpdateInvoiceInfoUsecase,
        useValue: jasmine.createSpyObj('UpdateInvoiceInfoUsecase', ['execute']),
      },
    ],
  });

  const fixture = TestBed.createComponent(MerchantBillingSettingsComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  const getUsecase = TestBed.inject(
    GetInvoiceInfoUseCase
  ) as jasmine.SpyObj<GetInvoiceInfoUseCase>;
  const updateUsecase = TestBed.inject(
    UpdateInvoiceInfoUsecase
  ) as jasmine.SpyObj<UpdateInvoiceInfoUsecase>;

  return { fixture, component, getUsecase, updateUsecase };
};

describe('Merchant Billing Component', () => {
  it('should be created', () => {
    const { getUsecase, component } = setup();
    getUsecase.execute.and.returnValue(of(invoiceUI));

    expect(component).toBeTruthy();
  });

  it('should call setInfo method on init', () => {
    const { getUsecase, component } = setup();
    getUsecase.execute.and.returnValue(of(invoiceUI));

    const setInfoSpy = spyOn(component, 'setInfo');
    component.ngOnInit();
    expect(setInfoSpy).toHaveBeenCalled();
  });

  it('should enable edition', fakeAsync(() => {
    const { component, getUsecase } = setup({
      store: {
        merchantId: 0,
        invoice: defaultInvoiceInfo,
      },
    });
    getUsecase.execute.and.returnValue(of(invoiceUI));

    component.ngOnInit();
    tick();

    expect(component.editing()).toBeFalse();
    expect(component.name.enabled)
      .withContext('Name should be disabled by default')
      .toBeFalse();

    component.enableEdition();

    expect(component.editing()).toBeTrue();
    expect(component.name.enabled)
      .withContext('Name should be enabled after calling enableEdition')
      .toBeTrue();
  }));

  it('should execute getInvoiceInfo usecase and hydrateInfo', fakeAsync(() => {
    const { component, getUsecase } = setup({
      store: {
        merchantId: 199,
        invoice: defaultInvoiceInfo,
      },
    });
    getUsecase.execute.and.returnValue(of(invoiceUI));
    const setInfoSpy = spyOn(component, 'setInfo').and.callThrough();
    const hydrateInfoSpy = spyOn(component, 'hydrateInfo').and.callThrough();

    component.ngOnInit();
    tick();

    expect(setInfoSpy).toHaveBeenCalledTimes(1);
    expect(hydrateInfoSpy).toHaveBeenCalled();
    expect(getUsecase.execute).toHaveBeenCalled();
  }));

  it('should save changes', fakeAsync(() => {
    const { component, updateUsecase, getUsecase } = setup({
      store: {
        merchantId: 199,
        invoice: invoiceUI,
      },
    });
    const modifiedName = 'Aplazo 123';
    getUsecase.execute.and.returnValue(of(invoiceUI));
    updateUsecase.execute.and.returnValue(
      of({
        ...invoiceUI,
        name: modifiedName,
      })
    );

    component.ngOnInit();
    tick();

    component.enableEdition();
    tick();

    component.name.setValue(modifiedName);
    tick();

    component.saveChanges();
    tick();

    expect(updateUsecase.execute).toHaveBeenCalledTimes(1);
  }));
});
