import { AsyncPipe } from '@angular/common';
import {
  discardPeriodicTasks,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NotifierService } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { of } from 'rxjs';
import {
  defaultAccountInfo,
  GetAccountInfoUseCase,
} from '../../../../../app/features/admin/application/usecases/get-account-info.usecase';
import { UpdateAccountUsecase } from '../../../../../app/features/admin/application/usecases/update-account.usecase';
import { AccountInfoUIResponse } from '../../../../../app/features/admin/domain/dtos/account.dto';
import { BANK_CODES } from '../../../../../app/features/admin/domain/entities/bank-code';
import { MerchantAccountRepository } from '../../../../../app/features/admin/domain/repositories/account-info.repository';
import { MerchantAccountSettingsComponent } from '../../../../../app/features/admin/infra/pages/account/account-settings.component';
import { MerchantStoreService } from '../../../../../app/features/admin/infra/services/merchant-store.service';
import { UserStore } from '../../../../../app/features/login/application/services/user.store';
import {
  formWithErrorsNotifierMessage,
  formWithErrorsNotifierTitle,
} from '../../../../../app/features/shared/domain/messages-text';
import { LocalAccountInfoRepository } from '../../local-account-info.repository';

const defaultConfig: {
  roles: string[];
  data: AccountInfoUIResponse;
  merchantId: number;
} = {
  roles: ['ROLE_CONTROL_TOWER_ADMIN'],
  data: defaultAccountInfo,
  merchantId: 199,
};

const setup = (args?: {
  roles?: string[];
  data?: AccountInfoUIResponse;
  merchantId?: number;
}) => {
  const config = {
    roles: args?.roles ?? defaultConfig.roles,
    data: args?.data ?? defaultConfig.data,
    merchantId: args?.merchantId ?? defaultConfig.merchantId,
  };

  TestBed.configureTestingModule({
    imports: [
      AsyncPipe,
      ReactiveFormsModule,
      AplazoButtonComponent,
      AplazoFormFieldDirectives,
      AplazoCardComponent,
    ],
    providers: [
      provideNotifierTesting(),
      provideLoaderTesting(),
      provideUseCaseErrorHandlerTesting(),
      {
        provide: MerchantStoreService,
        useValue: {
          selectedMerchantId$: of(config.merchantId),
          merchantAccount$: of(config.data),
          setMerchantAccount: () => {
            void 0;
          },
        },
      },
      {
        provide: MerchantAccountRepository,
        useClass: LocalAccountInfoRepository,
      },
      {
        provide: UserStore,
        useValue: {
          roles$: of(config.roles),
        },
      },
      {
        provide: GetAccountInfoUseCase,
        useValue: jasmine.createSpyObj('GetAccountInfoUseCase', ['execute']),
      },
      {
        provide: UpdateAccountUsecase,
        useValue: jasmine.createSpyObj('UpdateAccountUsecase', ['execute']),
      },
    ],
  });

  const fixture = TestBed.createComponent(MerchantAccountSettingsComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  const getUseCase = TestBed.inject(
    GetAccountInfoUseCase
  ) as jasmine.SpyObj<GetAccountInfoUseCase>;
  const updateUseCase = TestBed.inject(
    UpdateAccountUsecase
  ) as jasmine.SpyObj<UpdateAccountUsecase>;
  const userStore = TestBed.inject(UserStore);
  const merchantStoreService = TestBed.inject(MerchantStoreService);
  const notifier = TestBed.inject(NotifierService);
  const warnNotifierSpy = spyOn(notifier, 'warning');

  return {
    fixture,
    component,
    getUseCase,
    updateUseCase,
    userStore,
    merchantStoreService,
    warnNotifierSpy,
  };
};

describe('AccountSettingsComponent', () => {
  it('should be created', () => {
    const { component } = setup();
    expect(component).toBeTruthy();
  });

  it('should call hydrateForm after initialize the component', fakeAsync(() => {
    const { component, getUseCase, fixture } = setup({
      merchantId: 199,
    });

    fixture.detectChanges();
    getUseCase.execute.and.returnValue(
      of({
        bank: 'bank',
        bankAccount: 'bankAccount',
        email: 'email',
        merchantId: 199,
        token: 'token',
      } satisfies AccountInfoUIResponse)
    );
    const hydrateFormSpy = spyOn(component, 'hydrateForm');

    tick();

    expect(hydrateFormSpy).toHaveBeenCalledTimes(1);
    expect(getUseCase.execute).toHaveBeenCalledTimes(1);
  }));

  it('should not call updateUseCase as form is invalid', fakeAsync(() => {
    const { component, updateUseCase, warnNotifierSpy } = setup({
      merchantId: 199,
      data: {
        merchantId: 199,
        token: 'token',
        email: 'email',
        bank: 'bank',
        bankAccount: 'bankAccount',
      },
    });
    tick();

    component.enableEdition();
    tick();

    expect(component.editing()).toBeTrue();

    component.bank.setValue('');
    tick();

    component.saveChanges();
    tick();

    expect(updateUseCase.execute).toHaveBeenCalledTimes(0);
    expect(warnNotifierSpy).toHaveBeenCalledTimes(1);
    expect(warnNotifierSpy).toHaveBeenCalledWith({
      title: formWithErrorsNotifierTitle,
      message: formWithErrorsNotifierMessage,
    });
  }));

  it('should call updateUseCase as form has changes with no errors', fakeAsync(() => {
    const { component, updateUseCase } = setup({
      merchantId: 199,
      data: {
        merchantId: 199,
        token: 'token',
        email: 'email@email',
        bank: 'bank',
        bankAccount: '012012012012012012',
      },
    });
    updateUseCase.execute.and.returnValue(
      of({
        bankName: 'newBank',
        bankAccount: '012012012012012013',
        merchantId: 199,
      })
    );
    tick();

    component.enableEdition();
    tick();

    expect(component.editing()).toBeTrue();

    component.bank.setValue('newBank');
    component.bankAccount.setValue('012012012012012013');
    tick();

    component.saveChanges();
    tick();

    expect(updateUseCase.execute).toHaveBeenCalledTimes(1);
    expect(updateUseCase.execute).toHaveBeenCalledWith({
      merchantId: 199,
      bankName: 'newBank',
      bankAccount: '012012012012012013',
    });
    discardPeriodicTasks();
  }));

  it('should set bankName when bankAccount is 18 digits long and a bankCode is found', fakeAsync(() => {
    const { component } = setup({
      merchantId: 199,
      data: {
        merchantId: 199,
        token: 'token',
        email: 'email',
        bank: '',
        bankAccount: 'bankAccount',
      },
    });
    tick();

    component.enableEdition();
    tick();

    component.bankAccount.setValue('044123123123121212');
    tick(450);

    expect(component.bank.value).toBe(BANK_CODES['044']);
  }));
});
