import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  provideTemporal,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoIconRegistryService } from '@aplazo/shared-ui/icon';
import { AplazoCommonMessageComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { of } from 'rxjs';
import { GetBranchesInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-branches-info.usecase';
import { MerchantBranchesRepository } from '../../../../../app/features/admin/domain/repositories/branches-info.repository';
import { MerchantBranchesSettingsComponent } from '../../../../../app/features/admin/infra/pages/branches/branch-settings.component';
import { MerchantStoreService } from '../../../../../app/features/admin/infra/services/merchant-store.service';
import { UpsertStorefrontWithDialogFormService } from '../../../../../app/features/admin/infra/services/upsert-with-dialog-form.service';

describe('MerchantBranchesSettingsComponent', () => {
  let fixture: ComponentFixture<MerchantBranchesSettingsComponent>;
  let component: MerchantBranchesSettingsComponent;
  let store: MerchantStoreService;

  let repositorySpy: jasmine.SpyObj<MerchantBranchesRepository>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        AplazoCardComponent,
        AplazoSimpleTableComponents,
        AplazoButtonComponent,
        AplazoCommonMessageComponent,
        AsyncPipe,
        NgFor,
      ],
      providers: [
        MerchantStoreService,
        AplazoIconRegistryService,
        GetBranchesInfoUseCase,
        provideTemporal(),
        {
          provide: MerchantBranchesRepository,
          useValue: jasmine.createSpyObj('MerchantBranchesInfoRepository', [
            'getInfo',
          ]),
        },
        {
          provide: UpsertStorefrontWithDialogFormService,
          useValue: jasmine.createSpyObj('UpsertWithDialogFormService', [
            'execute',
          ]),
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
      ],
    });

    fixture = TestBed.createComponent(MerchantBranchesSettingsComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();

    store = TestBed.inject(MerchantStoreService);
    repositorySpy = TestBed.inject(
      MerchantBranchesRepository
    ) as jasmine.SpyObj<MerchantBranchesRepository>;
  });

  afterEach(() => {
    store.clearAll();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should call setInfo on init', () => {
    const setInfoSpy = spyOn(component, 'setInfo');

    component.ngOnInit();

    expect(setInfoSpy)
      .withContext('setInfo should be called on init')
      .toHaveBeenCalledTimes(1);
  });

  it('should not call usecase execute if merchantId is not set', async () => {
    const usecaseSpy = spyOn(
      TestBed.inject(GetBranchesInfoUseCase),
      'execute'
    ).and.callThrough();

    await component.setInfo();

    expect(usecaseSpy)
      .withContext('usecase execute should not be called')
      .toHaveBeenCalledTimes(0);
  });

  it('should set branches if merchantId is set', async () => {
    repositorySpy.getInfo.and.returnValue(
      of([
        {
          merchantConfigId: 2,
          branchId: 5,
          created: '2021-03-17T16:13:09',
          updated: '2021-05-05T22:58:12.782914',
          deleted: null,
          branchName: 'Chalco 3',
          banned: false,
        },
        {
          merchantConfigId: 2,
          branchId: 6,
          created: '2021-03-17T16:13:09',
          updated: '2021-05-05T22:58:12.787112',
          deleted: null,
          branchName: 'Chalco 4',
          banned: false,
        },
      ])
    );
    const usecaseSpy = spyOn(
      TestBed.inject(GetBranchesInfoUseCase),
      'execute'
    ).and.callThrough();

    store.setSelectedMerchantId(8);

    await component.setInfo();

    expect(usecaseSpy)
      .withContext('usecase execute should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should not call usecase execute if branches are already set for the merchantId', async () => {
    repositorySpy.getInfo.and.returnValue(of([]));
    const usecaseSpy = spyOn(
      TestBed.inject(GetBranchesInfoUseCase),
      'execute'
    ).and.callThrough();

    store.setSelectedMerchantId(199);
    store.setBranches({
      merchantId: 199,
      data: [
        {
          merchantConfigId: 2,
          branchId: 5,
          created: new Date('2021-03-17T16:13:09'),
          updated: new Date('2021-05-05T22:58:12.782914'),
          deleted: null,
          branchName: 'Chalco 3',
          banned: false,
        },
        {
          merchantConfigId: 2,
          branchId: 6,
          created: new Date('2021-03-17T16:13:09'),
          updated: new Date('2021-05-05T22:58:12.787112'),
          deleted: null,
          branchName: 'Chalco 4',
          banned: false,
        },
      ],
    });

    await component.setInfo();

    fixture.detectChanges();

    expect(usecaseSpy)
      .withContext('usecase execute should not be called')
      .toHaveBeenCalledTimes(0);
  });
});
