import { Async<PERSON>ipe, NgClass } from '@angular/common';
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NotifierService } from '@aplazo/merchant/shared';
import { LocalNotifier } from '@aplazo/merchant/shared-testing';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoDatepickerComponent } from '@aplazo/shared-ui/datepicker';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { of } from 'rxjs';
import { UpdateMerchantBasicsUsecase } from '../../../../../app/features/admin/application/usecases/update-merchant-basics.usecase';
import { MerchantBasicsSettingsComponent } from '../../../../../app/features/admin/infra/pages/basics/basics-settings.component';
import { MerchantStoreService } from '../../../../../app/features/admin/infra/services/merchant-store.service';
import { UserStore } from '../../../../../app/features/login/application/services/user.store';

describe('Basics Settings Component', () => {
  let fixture: ComponentFixture<MerchantBasicsSettingsComponent>;
  let component: MerchantBasicsSettingsComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NgClass,
        AsyncPipe,
        ReactiveFormsModule,
        AplazoButtonComponent,
        AplazoFormFieldDirectives,
        AplazoCardComponent,
        AplazoDatepickerComponent,
        AplazoDynamicPipe,
      ],
      providers: [
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        {
          provide: MerchantStoreService,
          useValue: {
            setMerchantBasics: () => {
              void 0;
            },
            merchantBasics$: of({
              merchantId: 199,
              mComInfoId: 123,
              merchantName: 'Test Merchant',
              status: 'CREATED',
              intType: 'API',
              createdAt: new Date('2024-05-10T00:00:00Z'),
              updatedAt: new Date('2024-05-10T00:00:00Z'),
              error: null,
            }),
          },
        },
        {
          provide: UpdateMerchantBasicsUsecase,
          useValue: {
            execute: () => {
              void 0;
            },
          },
        },
        {
          provide: UserStore,
          useValue: {
            roles$: of(['ROLE_CONTROL_TOWER_ADMIN']),
          },
        },
      ],
    });

    fixture = TestBed.createComponent(MerchantBasicsSettingsComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should enable form control "status"', () => {
    component.form.setValue({
      merchantId: 1,
      name: 'test',
      status: 'test',
      integrationType: 'test',
    });

    expect(component.status.enabled)
      .withContext('the form control "status" should be disabled')
      .toBeFalse();

    component.enableEdition();
    fixture.detectChanges();

    expect(component.status.enabled)
      .withContext('the form control "status" should be enabled')
      .toBeTrue();
  });

  it('should disable form control "status"', fakeAsync(() => {
    component.form.setValue({
      merchantId: 1,
      name: 'test',
      status: 'test',
      integrationType: 'test',
    });

    expect(component.status.enabled)
      .withContext('the form control "status" should be disabled')
      .toBeFalse();

    component.enableEdition();
    fixture.detectChanges();

    expect(component.status.enabled)
      .withContext('the form control "status" should be enabled')
      .toBeTrue();

    component.cancelEdition();
    fixture.detectChanges();
    tick();

    expect(component.status.enabled)
      .withContext('the form control "status" should be disabled again')
      .toBeFalse();
  }));
});
