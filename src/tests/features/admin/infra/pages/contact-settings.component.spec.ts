import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoCommonMessageComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { of } from 'rxjs';
import { GetContactInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-contact-info.usecase';
import { MerchantContactsRepository } from '../../../../../app/features/admin/domain/repositories/contact-info.repository';
import { MerchantContactSettingsComponent } from '../../../../../app/features/admin/infra/pages/contact/contact-settings.component';
import { UpdateContactWithDialogFormService } from '../../../../../app/features/admin/infra/services/edit-contact-with-dialog-form.service';
import { MerchantStoreService } from '../../../../../app/features/admin/infra/services/merchant-store.service';

describe('ContactSettingsComponent', () => {
  let fixture: ComponentFixture<MerchantContactSettingsComponent>;
  let component: MerchantContactSettingsComponent;
  let store: MerchantStoreService;

  let repositorySpy: jasmine.SpyObj<MerchantContactsRepository>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        AplazoCardComponent,
        AplazoSimpleTableComponents,
        AplazoButtonComponent,
        AplazoCommonMessageComponent,
        AsyncPipe,
        NgFor,
      ],
      providers: [
        MerchantStoreService,
        GetContactInfoUseCase,

        {
          provide: MerchantContactsRepository,
          useValue: jasmine.createSpyObj('MerchantContactInfoRepository', [
            'getInfo',
          ]),
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: UpdateContactWithDialogFormService,
          useValue: {
            execute: async () => {
              return void 0;
            },
          },
        },
      ],
    });

    fixture = TestBed.createComponent(MerchantContactSettingsComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();

    store = TestBed.inject(MerchantStoreService);
    repositorySpy = TestBed.inject(
      MerchantContactsRepository
    ) as jasmine.SpyObj<MerchantContactsRepository>;
  });

  afterEach(() => {
    store.clearAll();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should call setInfo on init', () => {
    const setInfoSpy = spyOn(component, 'setInfo');

    component.ngOnInit();

    expect(setInfoSpy)
      .withContext('setInfo should be called on init')
      .toHaveBeenCalledTimes(1);
  });

  it('should not call usecase execute if merchantId is not set', async () => {
    const usecaseSpy = spyOn(
      TestBed.inject(GetContactInfoUseCase),
      'execute'
    ).and.callThrough();

    await component.setInfo();

    expect(usecaseSpy)
      .withContext('usecase execute should not be called')
      .toHaveBeenCalledTimes(0);
  });

  it('should set contacts if merchantId is set', async () => {
    repositorySpy.getInfo.and.returnValue(
      of([
        {
          id: 7,
          email: '<EMAIL>',
          role: '1',
          businessArea: 'Finance',
          merchantId: 8,
          phone: '123456789',
          businessAreaId: 1,
          createdAt: '2021-08-13T17:26:41.000Z',
          updateAt: '2021-08-13T17:26:41.000Z',
        },
        {
          id: 34,
          email: '<EMAIL>',
          role: '1',
          businessArea: 'Finance',
          merchantId: 8,
          phone: '123456789',
          businessAreaId: 1,
          createdAt: '2021-08-13T17:26:41.000Z',
          updateAt: '2021-08-13T17:26:41.000Z',
        },
        {
          id: 6,
          email: '<EMAIL>',
          role: '1',
          businessArea: 'Finance',
          merchantId: 8,
          phone: '123456789',
          businessAreaId: 1,
          createdAt: '2021-08-13T17:26:41.000Z',
          updateAt: '2021-08-13T17:26:41.000Z',
        },
      ])
    );
    const usecaseSpy = spyOn(
      TestBed.inject(GetContactInfoUseCase),
      'execute'
    ).and.callThrough();

    store.setSelectedMerchantId(8);

    await component.setInfo();

    expect(usecaseSpy)
      .withContext('usecase execute should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should not call usecase execute if contacts are already set for the merchantId', async () => {
    repositorySpy.getInfo.and.returnValue(of([]));
    const usecaseSpy = spyOn(
      TestBed.inject(GetContactInfoUseCase),
      'execute'
    ).and.callThrough();

    store.setSelectedMerchantId(8);
    store.setMerchantContacts([
      {
        id: 7,
        email: '<EMAIL>',
        role: '1',
        phone: '1234567890',
        businessArea: 'Finance',
        merchantId: 8,
      },
      {
        id: 34,
        email: '<EMAIL>',
        role: '1',
        phone: '1234567890',
        businessArea: 'Finance',
        merchantId: 8,
      },
      {
        id: 6,
        email: '<EMAIL>',
        role: '1',
        phone: '1234567890',
        businessArea: 'Finance',
        merchantId: 8,
      },
    ]);

    await component.setInfo();

    fixture.detectChanges();

    expect(usecaseSpy)
      .withContext('usecase execute should not be called')
      .toHaveBeenCalledTimes(0);
  });
});
