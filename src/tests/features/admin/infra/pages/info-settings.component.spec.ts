import { TestBed } from '@angular/core/testing';
import { NotifierService } from '@aplazo/merchant/shared';
import { LocalNotifier } from '@aplazo/merchant/shared-testing';
import { BehaviorSubject, of } from 'rxjs';
import { UpdateMerchantGeneralsUsecase } from '../../../../../app/features/admin/application/usecases/update-merchant-generals.usecase';
import { MerchantGeneralInfoUI } from '../../../../../app/features/admin/domain/dtos/general-info.dto';
import { MerchantInfoSettingsComponent } from '../../../../../app/features/admin/infra/pages/generals/info-settings.component';
import { MerchantStoreService } from '../../../../../app/features/admin/infra/services/merchant-store.service';
import { UserStore } from '../../../../../app/features/login/application/services/user.store';

const defaultConfig: {
  role: string;
  merchantStoreGenerals: MerchantGeneralInfoUI;
  merchantId: number;
} = {
  role: 'ROLE_CONTROL_TOWER_MERCHANT_OPS',
  merchantStoreGenerals: {
    merchantId: 201,
    merchantEmail: '<EMAIL>',
    mComInfoId: 198,
    merchantAddress: 'lololo',
    merchantWebsite: 'www.artecktienda.com.mx',
    mQuestionnaireId: 196,
    merchantIndustry: 'Zapaterías',
    merchantAov: '3,000$ - 10,000$',
    merchantRevenue: '$25M - $250M',
    merchantCategory: 'Virtual Card',
    error: null,
  },
  merchantId: 1,
};

const setup = (args?: {
  role?: string;
  merchantStoreGenerals?: MerchantGeneralInfoUI;
  merchantId?: number;
}) => {
  const config = {
    ...defaultConfig,
    ...args,
  };

  const gs = new BehaviorSubject<MerchantGeneralInfoUI>(
    config.merchantStoreGenerals
  );

  TestBed.configureTestingModule({
    imports: [MerchantInfoSettingsComponent],
    providers: [
      {
        provide: NotifierService,
        useClass: LocalNotifier,
      },
      {
        provide: MerchantStoreService,
        useValue: {
          selectedMerchantId: config.merchantId,
          merchantGenerals$: gs.asObservable(),
          setMerchantGenerals: (generals: any) => {
            gs.next(generals);
          },
        },
      },
      {
        provide: UserStore,
        useValue: {
          roles$: of([config.role]),
        },
      },
      {
        provide: UpdateMerchantGeneralsUsecase,
        useValue: jasmine.createSpyObj('UpdateMerchantGeneralsUsecase', [
          'execute',
        ]),
      },
    ],
  });

  const fixture = TestBed.createComponent(MerchantInfoSettingsComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  return { fixture, component };
};

describe('Merchant Info Settings Component', () => {
  it('should be created', () => {
    const { component } = setup();

    expect(component).toBeTruthy();
  });
});
