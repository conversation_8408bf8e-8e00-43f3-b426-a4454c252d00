import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DialogService } from '@ngneat/dialog';
import { of } from 'rxjs';
import { NewMerchantUseCase } from '../../../../../app/features/admin/application/usecases/new-merchant.usecase';
import { InfoMerchantComponent } from '../../../../../app/features/admin/infra/components/info-merchant.component';
import { LegalRepresentativeComponent } from '../../../../../app/features/admin/infra/components/legal-representative.component';
import { MerchantCreationComponent } from '../../../../../app/features/admin/infra/pages/creation.component';

describe('Merchant Creation Component', () => {
  let fixture: ComponentFixture<MerchantCreationComponent>;
  let component: MerchantCreationComponent;
  let usecase: jasmine.SpyObj<NewMerchantUseCase>;
  let dialog: DialogService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [MerchantCreationComponent],
      providers: [
        DialogService,
        {
          provide: NewMerchantUseCase,
          useValue: jasmine.createSpyObj('NewMerchantUseCase', ['execute']),
        },
      ],
    });

    fixture = TestBed.createComponent(MerchantCreationComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();

    usecase = TestBed.inject(
      NewMerchantUseCase
    ) as jasmine.SpyObj<NewMerchantUseCase>;
    dialog = TestBed.inject(DialogService);
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should have 3 tabs menu buttons', () => {
    const tabs = fixture.debugElement.queryAll(By.css('.aplazo-tab__item'));

    expect(tabs.length)
      .withContext('There should be 3 tabs menu buttons')
      .toBe(3);
  });

  it('should render app-merchant-info component at the starting point', () => {
    const infoMerchant = fixture.debugElement.query(
      By.directive(InfoMerchantComponent)
    );
    const legalRepresentative = fixture.debugElement.query(
      By.directive(LegalRepresentativeComponent)
    );

    expect(infoMerchant)
      .withContext('The app-merchant-info component should be rendered')
      .toBeTruthy();

    expect(legalRepresentative)
      .withContext(
        'The app-legal-representative component should not be rendered'
      )
      .toBeFalsy();
  });

  it('should reflect changes when changeTab is called', () => {
    expect(component.step())
      .withContext('The initial step should be 0')
      .toBe(0);

    component.changeTab({ index: 1 });

    fixture.detectChanges();

    expect(component.step())
      .withContext('The step should be 1 after calling changeTab')
      .toBe(1);
  });

  it('should reflect changes when nextStep is called', () => {
    expect(component.step())
      .withContext('The initial step should be 0')
      .toBe(0);

    component.nextStep();

    fixture.detectChanges();

    expect(component.step())
      .withContext('The step should be 1 after calling nextStep')
      .toBe(1);
  });

  it('should reflect changes when prevStep is called', () => {
    component.nextStep();

    fixture.detectChanges();

    expect(component.step())
      .withContext('The initial step should be 1')
      .toBe(1);

    component.prevStep();

    fixture.detectChanges();

    expect(component.step())
      .withContext('The step should be 0 after calling prevStep')
      .toBe(0);
  });

  it('should reflect changes when finish is called', () => {
    usecase.execute.and.returnValue(
      of({ id: 123, email: '<EMAIL>', status: 'CREATED' })
    );
    const dialogOpenSpy = spyOn(dialog, 'open').and.returnValue({
      afterClosed$: of({ confirm: true }),
    } as any);

    expect(component.merchantCreated())
      .withContext('The merchantCreated signal should be false')
      .toBeFalse();

    component.finish();

    fixture.detectChanges();

    expect(usecase.execute)
      .withContext('The usecase execute method should be called')
      .toHaveBeenCalledTimes(1);
    expect(dialogOpenSpy)
      .withContext('The dialog open method should be called')
      .toHaveBeenCalledTimes(1);
    expect(component.merchantCreated())
      .withContext('The merchantCreated signal should be true')
      .toBeTrue();
  });

  it('should not call usecase execute when dialog is closed', () => {
    const dialogOpenSpy = spyOn(dialog, 'open').and.returnValue({
      afterClosed$: of({ confirm: false }),
    } as any);

    component.finish();

    fixture.detectChanges();

    expect(usecase.execute)
      .withContext('The usecase execute method should not be called')
      .toHaveBeenCalledTimes(0);
    expect(dialogOpenSpy)
      .withContext('The dialog open method should be called')
      .toHaveBeenCalledTimes(1);
    expect(component.merchantCreated())
      .withContext('The merchantCreated signal should be false')
      .toBeFalse();
  });
});
