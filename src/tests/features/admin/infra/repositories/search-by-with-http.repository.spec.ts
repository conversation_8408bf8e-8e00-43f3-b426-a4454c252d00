import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SearchByWithHttpRepository } from '../../../../../app/features/admin/infra/respositories/search-by-with-http.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';

describe('SearchByWithHttpRepository', () => {
  let httpController: HttpTestingController;
  let repository: SearchByWithHttpRepository;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        SearchByWithHttpRepository,
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://shield-back.aplazo.net',
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    repository = TestBed.inject(SearchByWithHttpRepository);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(SearchByWithHttpRepository);
  });

  it('should retrieve a success response when search by id', () => {
    const merchantId = 199;
    const expectedResponse = [
      {
        merchantId: merchantId,
        merchantName: 'Zapaterías ',
        status: 'APPROVED',
        intType: 'POSUI',
      },
    ];

    repository.searchBy({ id: merchantId }).subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://shield-back.aplazo.net/api/v1/merchant/search?id=199'
    );

    expect(req.request.method).toBe('GET');
    req.flush(expectedResponse);
  });

  it('should retrieve a success response when search by name', () => {
    const merchantName = 'Zapate';
    const expectedResponse = [
      {
        merchantId: 199,
        merchantName: merchantName,
        status: 'APPROVED',
        intType: 'POSUI',
      },
    ];

    repository.searchBy({ name: merchantName }).subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://shield-back.aplazo.net/api/v1/merchant/search?name=Zapate'
    );

    expect(req.request.method).toBe('GET');
    req.flush(expectedResponse);
  });

  it('should throw an error when the request fails', () => {
    const merchantId = 199;
    const expectedErrorMsg = 'Unable to find';

    repository.searchBy({ id: merchantId }).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpController.expectOne(
      'https://shield-back.aplazo.net/api/v1/merchant/search?id=199'
    );

    expect(req.request.method).toBe('GET');
    req.flush(expectedErrorMsg, { status: 500, statusText: 'Not Found' });
  });
});
