import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import {
  NewContactDto,
  RetrievedContactResponse,
} from '../../../../../app/features/admin/domain/dtos/contact.dto';
import { MerchantContactsWithHttp } from '../../../../../app/features/admin/infra/respositories/contact-with-http.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';

describe('MerchantContactsWithHttp', () => {
  let httpController: HttpTestingController;
  let repository: MerchantContactsWithHttp;
  let spyGetHttp: jasmine.Spy;
  let spyPostHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        MerchantContactsWithHttp,
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://shield-back.aplazo.net',
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    repository = TestBed.inject(MerchantContactsWithHttp);
    const http = TestBed.inject(HttpClient);
    spyGetHttp = spyOn(http, 'get').and.callThrough();
    spyPostHttp = spyOn(http, 'post').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(MerchantContactsWithHttp);
  });

  describe('getInfo', () => {
    it('should retrieve a success response for merchant contacts request', () => {
      const merchantId = 8;
      const expectedResponse = [
        {
          id: 7,
          email: '<EMAIL>',
          role: '1',
          businessArea: 'Finance',
          merchantId: 8,
          phone: '123456789',
          businessAreaId: 1,
          createdAt: '2021-08-13T17:26:41.000Z',
          updateAt: '2021-08-13T17:26:41.000Z',
        },
        {
          id: 34,
          email: '<EMAIL>',
          role: '1',
          businessArea: 'Finance',
          merchantId: 8,
          phone: '123456789',
          businessAreaId: 1,
          createdAt: '2021-08-13T17:26:41.000Z',
          updateAt: '2021-08-13T17:26:41.000Z',
        },
        {
          id: 6,
          email: '<EMAIL>',
          role: '1',
          businessArea: 'Finance',
          merchantId: 8,
          phone: '123456789',
          businessAreaId: 1,
          createdAt: '2021-08-13T17:26:41.000Z',
          updateAt: '2021-08-13T17:26:41.000Z',
        },
      ];

      repository.getInfo(merchantId).subscribe({
        next: response => {
          expect(response).toEqual(expectedResponse);
          expect(spyGetHttp).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/contact-info`
      );

      expect(req.request.method).toBe('GET');

      req.flush(expectedResponse);
    });

    it('should throw an error when the request fails', () => {
      const merchantId = 8;
      const expectedErrorMsg = 'Unable to find';

      repository.getInfo(merchantId).subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.status).toBe(500);
          expect(spyGetHttp).toHaveBeenCalledTimes(1);
        },
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/contact-info`
      );

      expect(req.request.method).toBe('GET');

      req.flush(expectedErrorMsg, { status: 500, statusText: 'Not Found' });
    });
  });

  describe('createOne', () => {
    it('should retrieve a success response for merchant contact creation', () => {
      const newContact: NewContactDto = {
        email: '<EMAIL>',
        role: 'test role',
        merchantId: 8,
        phone: '123456789',
        businessAreaId: 1,
      };
      const expected: RetrievedContactResponse = {
        id: 1,
        email: '<EMAIL>',
        role: 'test role',
        merchantId: 8,
        phone: '123456789',
        businessAreaId: 1,
        createdAt: '2021-08-13T17:26:41.000Z',
        updateAt: '2021-08-13T17:26:41.000Z',
        businessArea: 'Finance',
      };

      repository.createOne(newContact).subscribe({
        next: response => {
          expect(response).toEqual(expected);
          expect(spyPostHttp).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        'https://shield-back.aplazo.net/api/v1/merchant-contact-info'
      );

      expect(req.request.method).toBe('POST');

      req.flush(expected);
    });

    it('should throw an error when the request fails', () => {
      const newContact: NewContactDto = {
        email: '<EMAIL>',
        role: 'test role',
        merchantId: 8,
        phone: '123456789',
        businessAreaId: 1,
      };

      repository.createOne(newContact).subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.status).toBe(500);
          expect(spyPostHttp).toHaveBeenCalledTimes(1);
        },
      });

      const req = httpController.expectOne(
        'https://shield-back.aplazo.net/api/v1/merchant-contact-info'
      );

      expect(req.request.method).toBe('POST');

      req.flush('Unable to create', {
        status: 500,
        statusText: 'Internal Server Error',
      });
    });
  });
});
