import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MerchantBranchesWithHttp } from '../../../../../app/features/admin/infra/respositories/branches-with-http.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';

describe('MerchantBranchesWithHttp', () => {
  let httpController: HttpTestingController;
  let repository: MerchantBranchesWithHttp;
  let httpGetSpy: jasmine.Spy;
  let httpPostSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        MerchantBranchesWithHttp,
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://shield-back.aplazo.net',
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    repository = TestBed.inject(MerchantBranchesWithHttp);
    httpGetSpy = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
    httpPostSpy = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(MerchantBranchesWithHttp);
  });

  describe('getInfo', () => {
    it('should retrieve a success response for merchant branches request', () => {
      const merchantId = 8;
      const expectedResponse = [
        {
          merchantConfigId: 2,
          branchId: 5,
          created: '2021-03-17T16:13:09',
          updated: '2021-05-05T22:58:12.782914',
          deleted: null,
          branchName: 'Chalco 3',
          banned: false,
        },
        {
          merchantConfigId: 2,
          branchId: 6,
          created: '2021-03-17T16:13:09',
          updated: '2021-05-05T22:58:12.787112',
          deleted: null,
          branchName: 'Chalco 4',
          banned: false,
        },
      ];

      repository.getInfo(merchantId).subscribe({
        next: response => {
          expect(response).toEqual(expectedResponse);
          expect(httpGetSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/branches`
      );

      expect(req.request.method).toBe('GET');

      req.flush(expectedResponse);
    });

    it('should throw an error when the request fails', () => {
      const merchantId = 8;
      const expectedErrorMsg = 'Unable to find';

      repository.getInfo(merchantId).subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.status).toBe(500);
          expect(httpGetSpy).toHaveBeenCalledTimes(1);
        },
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/branches`
      );

      expect(req.request.method).toBe('GET');

      req.flush(expectedErrorMsg, { status: 500, statusText: 'Not Found' });
    });
  });

  describe('upsertOne', () => {
    it('should retrieve a success response for merchant branch upsert request', () => {
      const branch = {
        name: 'Chalco 3',
        merchantId: 2,
      };

      const successResponse = {
        merchantConfigId: branch.merchantId,
        branchId: 123,
        created: '2024-08-21T23:25:30.981425',
        updated: '2024-08-22T14:34:09.913471',
        deleted: null,
        branchName: branch.name,
        banned: false,
      };

      repository.upsertOne(branch).subscribe({
        next: response => {
          expect(response).toEqual(successResponse);
          expect(httpPostSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/${branch.merchantId}/branches`
      );

      expect(req.request.method).toBe('POST');

      req.flush(successResponse);
    });

    it('should throw an error when the request fails', () => {
      const branch = {
        name: 'Chalco 3',
        merchantId: 2,
        id: 5,
      };

      repository.upsertOne(branch).subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.status).toBe(500);
          expect(httpPostSpy).toHaveBeenCalledTimes(1);
        },
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/${branch.merchantId}/branches`
      );

      expect(req.request.method).toBe('POST');

      req.flush('Internal Server Error', {
        status: 500,
        statusText: 'Internal Server Error',
      });
    });
  });
});
