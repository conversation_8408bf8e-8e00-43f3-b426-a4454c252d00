import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { NewMerchantRequest } from '../../../../../app/features/admin/domain/dtos/new-merchant-request.dto';
import { NewMerchantWithHttpRepository } from '../../../../../app/features/admin/infra/respositories/new-merchant-with-http.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';

describe('NewMerchantWithHttpRepository', () => {
  let httpController: HttpTestingController;
  let service: NewMerchantWithHttpRepository;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://shield-back.aplazo.net',
          },
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(NewMerchantWithHttpRepository);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(NewMerchantWithHttpRepository);
  });

  it('should retrieve a success response for new merchant request', () => {
    const request: NewMerchantRequest = {
      basic: {
        email: '<EMAIL>',
        businessName: 'Business name',
        businessWebpage: 'https://www.test.com',
        businessAddress: 'address 1234',
        contactName: 'contact name',
        position: 'position',
        phone: '522222222225',
        industry: 'Zapaterías',
        avgTicket: '800$ - 3,000$',
        yearlySales: '0 - $25M',
        intType: 'POSUI',
      },
    };
    const expectedResponse = {
      id: 1,
      email: request.basic.email,
      status: 'CREATED',
    };

    service.create(request).subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://shield-back.aplazo.net/api/v1/merchant'
    );

    expect(req.request.method).toEqual('POST');

    req.flush(expectedResponse);
  });

  it('should retrieve a error response for new merchant request', () => {
    const request: NewMerchantRequest = {
      basic: {
        email: '<EMAIL>',
        businessName: 'Business name',
        businessWebpage: 'https://www.test.com',
        businessAddress: 'address 1234',
        contactName: 'contact name',
        position: 'position',
        phone: '522222222225',
        industry: 'Zapaterías',
        avgTicket: '800$ - 3,000$',
        yearlySales: '0 - $25M',
        intType: 'POSUI',
      },
    };
    const errorMsg = 'Deliberate 404 error';

    service.create(request).subscribe({
      next: fail,
      error: err => {
        expect(err).toBeInstanceOf(HttpErrorResponse);
        expect(err.error).toBe('Deliberate 404 error');
        expect(err.status).toBe(404);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpController.expectOne(
      'https://shield-back.aplazo.net/api/v1/merchant'
    );

    expect(req.request.method).toEqual('POST');

    req.flush(errorMsg, { status: 404, statusText: 'Not Found' });
  });
});
