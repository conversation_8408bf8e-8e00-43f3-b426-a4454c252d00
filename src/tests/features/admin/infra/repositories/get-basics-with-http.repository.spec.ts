import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { GetBasicsWithHttpRepository } from '../../../../../app/features/admin/infra/respositories/get-basics-with-http.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';

describe('GetBasicsWithHttpRepository', () => {
  let httpController: HttpTestingController;
  let repository: GetBasicsWithHttpRepository;
  let spyHttpGet: jasmine.Spy;
  let spyHttpPost: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        GetBasicsWithHttpRepository,
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://shield-back.aplazo.net',
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    repository = TestBed.inject(GetBasicsWithHttpRepository);
    spyHttpGet = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
    spyHttpPost = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(GetBasicsWithHttpRepository);
  });

  it('should retrieve a success response for merchant basics request', () => {
    const merchantId = 199;
    const expectedResponse = {
      id: merchantId,
      idMComInfo: 196,
      name: 'Zapaterías ',
      status: 'APPROVED',
      intType: 'POSUI',
      created: '2024-05-01T17:00:00.000Z',
      updated: '2024-05-01T17:00:00.000Z',
    };

    repository.getInfo(merchantId).subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttpGet).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}`
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throw an error when the request fails', () => {
    const merchantId = 199;
    const expectedErrorMsg = 'Unable to find';

    repository.getInfo(merchantId).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.status).toBe(500);
        expect(spyHttpGet).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpController.expectOne(
      `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}`
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedErrorMsg, {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });

  it('should update the merchant status successfully', () => {
    const merchantId = 199;
    const status = 'REJECTED';

    repository
      .updateByMerchantId({ merchantId, merchantStatus: status })
      .subscribe({
        next: response => {
          expect(response).toBeFalsy();
          expect(spyHttpPost).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

    const req = httpController.expectOne(
      `https://shield-back.aplazo.net/api/v1/merchant/info/${merchantId}`
    );

    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({ merchantStatus: status });

    req.flush(null);
  });

  it('should throw an error when the request fails', () => {
    const merchantId = 199;
    const status = 'REJECTED';
    const expectedErrorMsg = 'Unable to find';

    repository
      .updateByMerchantId({ merchantId, merchantStatus: status })
      .subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.status).toBe(500);
          expect(spyHttpPost).toHaveBeenCalledTimes(1);
        },
      });

    const req = httpController.expectOne(
      `https://shield-back.aplazo.net/api/v1/merchant/info/${merchantId}`
    );

    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({ merchantStatus: status });

    req.flush(expectedErrorMsg, {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
