import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MdrResponseDto } from '../../../../../app/features/admin/domain/dtos/mdr.dto';
import { GetMdrInfoWithHttpRepository } from '../../../../../app/features/admin/infra/respositories/get-mdr-with-http.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';

describe('GetMdrInfoWithHttpRepository', () => {
  let httpController: HttpTestingController;
  let repository: GetMdrInfoWithHttpRepository;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        GetMdrInfoWithHttpRepository,
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://shield-back.aplazo.net',
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    repository = TestBed.inject(GetMdrInfoWithHttpRepository);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(GetMdrInfoWithHttpRepository);
  });

  it('should retrieve a success response for merchant mdr request', () => {
    const merchantId = 199;
    const expectedResponse: MdrResponseDto = [
      {
        id: 391,
        merchantId: 199,
        feeOps: 0,
        feePct: 0.0358,
        promoFee: 0.1,
        promoFeeEndDate: '2022-03-24T00:00:00',
        updated: '2022-07-12T17:50:07.449879',
        installmentFrequencyId: 3,
      },
    ];

    repository.getInfo(merchantId).subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      `https://shield-back.aplazo.net/api/v1/merchant-loan-fee-frequency/merchant/${merchantId}`
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throw an error when the request fails', () => {
    const merchantId = 199;
    const expectedErrorMsg = 'Unable to find';

    repository.getInfo(merchantId).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.status).toBe(500);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpController.expectOne(
      `https://shield-back.aplazo.net/api/v1/merchant-loan-fee-frequency/merchant/${merchantId}`
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedErrorMsg, { status: 500, statusText: 'Not Found' });
  });
});
