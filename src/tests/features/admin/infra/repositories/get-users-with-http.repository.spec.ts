import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { OperatorsRepositoryWithHttp } from '../../../../../app/features/admin/infra/respositories/users-with-http.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';

describe('GetUsersWithHttpRepository', () => {
  let httpController: HttpTestingController;
  let repository: OperatorsRepositoryWithHttp;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        OperatorsRepositoryWithHttp,
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://shield-back.aplazo.net',
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    repository = TestBed.inject(OperatorsRepositoryWithHttp);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(OperatorsRepositoryWithHttp);
  });

  it('should retrieve a success response for merchant branches request', () => {
    const merchantId = 8;
    const expectedResponse = [
      {
        idAccount: 20,
        username: 'columbia_centro_venta',
        role: 'ROLE_SELL_AGENT',
        userStatus: 'Active',
        userEmail: null,
        platform: 'POSUI',
        branches: [
          {
            id: 150,
            name: 'León 8',
          },
        ],
        createdAt: '2023-07-06T23:11:14.009525',
        updatedAt: '2023-07-06T23:11:14.009525',
        deletedAt: null,
      },
      {
        idAccount: 21,
        username: 'columbia_centro_venta_1',
        role: 'ROLE_SELL_AGENT',
        userStatus: 'Active',
        userEmail: null,
        platform: 'POSUI',
        branches: [
          {
            id: 151,
            name: 'León 8',
          },
        ],
        createdAt: '2023-07-06T23:43:55.912376',
        updatedAt: '2023-07-06T23:43:55.912376',
        deletedAt: null,
      },
    ];

    repository.getInfo(merchantId).subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/users`
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throw an error when the request fails', () => {
    const merchantId = 8;
    const expectedErrorMsg = 'Unable to find';

    repository.getInfo(merchantId).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.status).toBe(500);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpController.expectOne(
      `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/users`
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedErrorMsg, { status: 500, statusText: 'Not Found' });
  });
});
