import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { AccountWithHttpRepository } from '../../../../../app/features/admin/infra/respositories/account-with-http.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';

describe('GetAccountWithHttpRepository', () => {
  let httpController: HttpTestingController;
  let repository: AccountWithHttpRepository;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        AccountWithHttpRepository,
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://shield-back.aplazo.net',
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    repository = TestBed.inject(AccountWithHttpRepository);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(AccountWithHttpRepository);
  });

  it('should retrieve a success response for merchant account request', () => {
    const merchantId = 199;
    const expectedResponse = {
      bank: 'bank',
      bankAccount: 'bankAccount',
      emailAccount: '<EMAIL>',
      idAccount: 17840,
      idApiToken: 145,
      idBilling: 17840,
      merchantId: 199,
      token: '148f69e2-fade-4e97-9cb3-6481c67e1962',
    };

    repository.getInfo(merchantId).subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/token-account`
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throw an error when the request fails', () => {
    const merchantId = 199;
    const expectedErrorMsg = 'Unable to find';

    repository.getInfo(merchantId).subscribe({
      next: fail,
      error: error => {
        expect(error).toBeInstanceOf(HttpErrorResponse);
        expect(error.status).toBe(500);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpController.expectOne(
      `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/token-account`
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedErrorMsg, { status: 500, statusText: 'Not Found' });
  });
});
