import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import {
  InvoiceRepositoryRequest,
  MerchantInvoiceResponse,
} from '../../../../../app/features/admin/domain/dtos/invoice.dto';
import { InvoiceWithHttpRepository } from '../../../../../app/features/admin/infra/respositories/invoice-with-http.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';

describe('InvoiceWithHttpRepository', () => {
  let httpController: HttpTestingController;
  let repository: InvoiceWithHttpRepository;
  let getHttpSpy: jasmine.Spy;
  let postHttpSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        InvoiceWithHttpRepository,
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://shield-back.aplazo.net',
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    repository = TestBed.inject(InvoiceWithHttpRepository);
    const http = TestBed.inject(HttpClient);
    getHttpSpy = spyOn(http, 'get').and.callThrough();
    postHttpSpy = spyOn(http, 'post').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(InvoiceWithHttpRepository);
  });

  describe('getInfo', () => {
    it('should retrieve a success response for merchant invoice request', () => {
      const merchantId = 13;
      const expectedResponse: MerchantInvoiceResponse = {
        id: 67,
        merchantId: 13,
        rfc: 'rfc',
        email: '<EMAIL>',
        paymentMethod: 'PUE',
        zipCode: 'zipCode',
        invoiceRegime: '601',
        name: 'name',
        currency: 'MXN',
        catalogPaymentForm: {
          id: 1,
          code: '01',
          name: 'Efectivo',
        },
        catalogCfdi: {
          id: 1,
          code: 'I08',
          name: 'Otras máquinas y equipo',
        },
        facPubGral: false,
      };

      repository.getInfo(merchantId).subscribe({
        next: response => {
          expect(response).toEqual(expectedResponse);
          expect(getHttpSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/invoice`
      );

      expect(req.request.method).toBe('GET');

      req.flush(expectedResponse);
    });

    it('should throw an error when the request fails', () => {
      const merchantId = 199;
      const expectedErrorMsg = 'Unable to find';

      repository.getInfo(merchantId).subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.status).toBe(500);
          expect(getHttpSpy).toHaveBeenCalledTimes(1);
        },
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/invoice`
      );

      expect(req.request.method).toBe('GET');

      req.flush(expectedErrorMsg, { status: 500, statusText: 'Not Found' });
    });
  });

  describe('updateOneBy', () => {
    it('should update a merchant invoice', () => {
      const request: InvoiceRepositoryRequest = {
        merchantId: 13,
        cfdiId: 15,
        paymentFormId: 12,
        currency: 'MXN',
        email: '<EMAIL>',
        paymentMethodId: 'PUE',
        zipCode: '12345',
        invoiceRegimeId: '601',
        rfc: 'ABAB010101GH1',
        name: 'name',
        facPubGral: false,
      };

      repository.updateOneBy(request).subscribe({
        next: response => {
          expect(response).toBeNull();
          expect(postHttpSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/invoice/${request.merchantId}`
      );

      expect(req.request.method).toBe('POST');

      req.flush(null);
    });

    it('should throw an error when the request fails', () => {
      const request: InvoiceRepositoryRequest = {
        merchantId: 13,
        cfdiId: 15,
        paymentFormId: 12,
        currency: 'MXN',
        email: '<EMAIL>',
        paymentMethodId: 'PUE',
        zipCode: '12345',
        invoiceRegimeId: '601',
        rfc: 'ABAB010101GH1',
        name: 'name',
        facPubGral: false,
      };

      repository.updateOneBy(request).subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.status).toBe(500);
          expect(postHttpSpy).toHaveBeenCalledTimes(1);
        },
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/invoice/${request.merchantId}`
      );

      expect(req.request.method).toBe('POST');

      req.flush('Internal Server Error', {
        status: 500,
        statusText: 'Internal Server Error',
      });
    });
  });
});
