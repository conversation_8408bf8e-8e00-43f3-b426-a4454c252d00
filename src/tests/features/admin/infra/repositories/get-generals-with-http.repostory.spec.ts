import {
  HttpClient,
  HttpErrorResponse,
  provideHttpClient,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MerchantGeneralUpdateRepositoryRequest } from '../../../../../app/features/admin/domain/dtos/general-info.dto';
import { GeneralsWithHttpRepository } from '../../../../../app/features/admin/infra/respositories/get-generals-with-htt.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';

describe('GeneralsWithHttpRepository', () => {
  let httpController: HttpTestingController;
  let repository: GeneralsWithHttpRepository;
  let getHttpSpy: jasmine.Spy;
  let postHttpSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        GeneralsWithHttpRepository,
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'https://shield-back.aplazo.net',
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    repository = TestBed.inject(GeneralsWithHttpRepository);
    const http = TestBed.inject(HttpClient);
    getHttpSpy = spyOn(http, 'get').and.callThrough();
    postHttpSpy = spyOn(http, 'post').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(GeneralsWithHttpRepository);
  });

  describe('getInfo', () => {
    it('should retrieve a success response for merchant generals request', () => {
      const merchantId = 199;
      const expectedResponse = {
        id: 199,
        idMComInfo: 196,
        idMQuestionnaire: 194,
        merchantEmail: '<EMAIL>',
        merchantAddress: 'Centro Histórico 1234 esq. Norte',
        merchantWebsite: 'zapateriasleon.com.mx',
        merchantIndustry: 'Calzado',
        merchantAov: '800-3000',
        merchantRevenue: '0-25M',
        merchantCategory: 'Moda y accesorios',
      };

      repository.getInfo(merchantId).subscribe({
        next: response => {
          expect(response).toEqual(expectedResponse);
          expect(getHttpSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/info`
      );

      expect(req.request.method).toBe('GET');

      req.flush(expectedResponse);
    });

    it('should throw an error when the request fails', () => {
      const merchantId = 199;
      const expectedErrorMsg = 'Unable to find';

      repository.getInfo(merchantId).subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.status).toBe(500);
          expect(getHttpSpy).toHaveBeenCalledTimes(1);
        },
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/${merchantId}/info`
      );

      expect(req.request.method).toBe('GET');

      req.flush(expectedErrorMsg, { status: 500, statusText: 'Not Found' });
    });
  });

  describe('updateOneBy', () => {
    it('should update merchant generals', () => {
      const request: MerchantGeneralUpdateRepositoryRequest = {
        merchantId: 201,
        merchant: {
          email: '<EMAIL>',
        },
        companyInformation: {
          website: 'www.artecktienda.com.mx',
          address: 'Calle 12 34',
        },
        questionnaire: {
          industry: 'Zapaterías',
          category: 'Automotriz y Motocicletas',
          average_order: '3,000$ - 10,000$',
          revenue: '$25M - $250M',
        },
      };

      repository.updateOneBy(request).subscribe({
        next: response => {
          expect(response).toBeTrue();
          expect(postHttpSpy).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/general/${request.merchantId}`
      );

      expect(req.request.method).toBe('POST');

      req.flush(true);
    });

    it('should throw an error when the request fails', () => {
      const request: MerchantGeneralUpdateRepositoryRequest = {
        merchantId: 201,
        merchant: {
          email: '<EMAIL>',
        },
        companyInformation: {
          website: 'www.artecktienda.com.mx',
          address: 'Calle 12 34',
        },
        questionnaire: {
          industry: 'Zapaterías',
          category: 'Automotriz y Motocicletas',
          average_order: '3,000$ - 10,000$',
          revenue: '$25M - $250M',
        },
      };

      repository.updateOneBy(request).subscribe({
        next: fail,
        error: error => {
          expect(error).toBeInstanceOf(HttpErrorResponse);
          expect(error.status).toBe(500);
          expect(postHttpSpy).toHaveBeenCalledTimes(1);
        },
      });

      const req = httpController.expectOne(
        `https://shield-back.aplazo.net/api/v1/merchant/general/${request.merchantId}`
      );

      expect(req.request.method).toBe('POST');

      req.flush('Internal Server Error', {
        status: 500,
        statusText: 'Internal Server Error',
      });
    });
  });
});
