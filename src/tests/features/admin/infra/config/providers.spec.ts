import { provideHttpClient } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalNotifier,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { GetBasicsInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-basics-info.usecase';
import { MerchantBasicInfoRepository } from '../../../../../app/features/admin/domain/repositories/basic-info.repository';
import { MerchantGeneralInfoRepository } from '../../../../../app/features/admin/domain/repositories/general-info.repository';
import { NewMerchantRespository } from '../../../../../app/features/admin/domain/repositories/new-merchant.repository';
import {
  provideCreationRepositories,
  provideMerchantAdminRepositories,
  provideMerchantAdminUseCases,
} from '../../../../../app/features/admin/infra/config/providers';
import { GetBasicsWithHttpRepository } from '../../../../../app/features/admin/infra/respositories/get-basics-with-http.repository';
import { GeneralsWithHttpRepository } from '../../../../../app/features/admin/infra/respositories/get-generals-with-htt.repository';
import { NewMerchantWithHttpRepository } from '../../../../../app/features/admin/infra/respositories/new-merchant-with-http.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';

describe('Admin Infra Config Providers', () => {
  let newMerchantRepo: NewMerchantRespository<any, any>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {},
        },
        provideCreationRepositories(),
      ],
    });

    newMerchantRepo = TestBed.inject(NewMerchantRespository);
  });

  it('should provide an instance of NewMerchantWithHttpRepository', () => {
    expect(newMerchantRepo).toBeTruthy();
    expect(newMerchantRepo).toBeInstanceOf(NewMerchantWithHttpRepository);
  });
});

describe('Admin Infra Merchant Repositories', () => {
  let basicInfoRepo: any;
  let generalInfoRepo: any;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {},
        },
        provideMerchantAdminRepositories(),
      ],
    });

    basicInfoRepo = TestBed.inject(MerchantBasicInfoRepository);
    generalInfoRepo = TestBed.inject(MerchantGeneralInfoRepository);
  });

  it('should provide an instance of GetBasicsWithHttpRepository', () => {
    expect(basicInfoRepo).toBeTruthy();
    expect(basicInfoRepo).toBeInstanceOf(GetBasicsWithHttpRepository);
  });

  it('should provide an instance of GetGeneralsWithHttpRepository', () => {
    expect(generalInfoRepo).toBeTruthy();
    expect(generalInfoRepo).toBeInstanceOf(GeneralsWithHttpRepository);
  });
});

describe('Admin Application Use Cases', () => {
  let getBasicUseCase: GetBasicsInfoUseCase;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {},
        },
        provideMerchantAdminRepositories(),
        provideHttpClient(),
        provideMerchantAdminUseCases(),
      ],
    });

    getBasicUseCase = TestBed.inject(GetBasicsInfoUseCase);
  });

  it('should provide getBasicUseCase', () => {
    expect(getBasicUseCase).toBeInstanceOf(GetBasicsInfoUseCase);
  });
});
