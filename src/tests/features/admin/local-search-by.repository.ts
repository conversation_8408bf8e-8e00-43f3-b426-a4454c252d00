import { Observable, of } from 'rxjs';
import {
  SearchByRepositoryRequest,
  SearchByRepositoryResponse,
} from '../../../app/features/admin/domain/dtos/search.dto';
import { SearchMerchantByRepository } from '../../../app/features/admin/domain/repositories/search-merchant-by.repository';
import data from './merchant.db.json';

export class LocalSearchBy
  implements
    SearchMerchantByRepository<
      SearchByRepositoryRequest,
      Observable<SearchByRepositoryResponse[]>
    >
{
  readonly #data = data;

  searchBy(
    req: SearchByRepositoryRequest
  ): Observable<SearchByRepositoryResponse[]> {
    const filtered = this.#data.filter(
      item =>
        item.id == req.id ||
        (req.name &&
          item.name.toLocaleLowerCase().includes(req.name.toLocaleLowerCase()))
    );

    const parsed: SearchByRepositoryResponse[] = filtered.map(item => {
      return {
        merchantId: item.id,
        merchantName: item.name,
        status: item.status,
        intType: item.intType,
      };
    });

    return of(parsed);
  }
}
