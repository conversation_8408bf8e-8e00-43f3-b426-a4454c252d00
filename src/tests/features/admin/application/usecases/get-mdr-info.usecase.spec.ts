import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, of, take, throwError } from 'rxjs';
import { GetMdrInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-mdr-info.usecase';
import { MerchantMdrInfoRepository } from '../../../../../app/features/admin/domain/repositories/mdr-info.repository';

describe('GetMdrInfoUseCase', () => {
  let usecase: GetMdrInfoUseCase;
  let repositorySpy: jasmine.SpyObj<MerchantMdrInfoRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MerchantMdrInfoRepository,
          useValue: jasmine.createSpyObj('MerchantMdrInfoRepository', [
            'getInfo',
          ]),
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        GetMdrInfoUseCase,
      ],
    });

    repositorySpy = TestBed.inject(
      MerchantMdrInfoRepository
    ) as jasmine.SpyObj<MerchantMdrInfoRepository>;

    const loaderService = TestBed.inject(LoaderService);
    usecase = TestBed.inject(GetMdrInfoUseCase);

    showLoaderSpy = spyOn(loaderService, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loaderService, 'hide').and.callThrough();
    handleErrorSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should return the mdr info', async () => {
    repositorySpy.getInfo.and.returnValue(
      of([
        {
          id: 391,
          merchantId: 199,
          feeOps: 0,
          feePct: 0.0358,
          promoFee: 0.1,
          promoFeeEndDate: '2022-03-24T00:00:00',
          updated: '2022-07-12T17:50:07.449879',
          installmentFrequencyId: 3,
        },
      ])
    );

    const result = await lastValueFrom(usecase.execute(199).pipe(take(1)));

    expect(result).toEqual({
      5: {
        id: 391,
        merchantId: 199,
        feeOps: 0,
        feePct: 3.58,
        promoFee: 10,
        promoFeeEndDate: '2022-03-24T00:00:00',
        promoFeeEndIsoDate: new Date('2022-03-24T00:00:00').toISOString(),
        updatedAt: '2022-07-12T17:50:07.449879',
        installmentFrequency: 5,
        installmentFrequencyId: 3,
      },
    });
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error when merchant id is not a number', fakeAsync(() => {
    repositorySpy.getInfo.and.returnValue(of({} as any));
    const id = '199';

    let result: any;

    usecase
      .execute(
        // @ts-expect-error: testing invalid input
        id
      )
      .subscribe({
        next: fail,
        error: error => {
          result = error;
        },
      });

    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(0);
  }));

  it('should handle error when merchant id is less than or equal to 0', fakeAsync(() => {
    repositorySpy.getInfo.and.returnValue(of({} as any));
    const id = 0;

    let result: any;

    usecase.execute(id).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));

  it('should handle error and return default value when repository throws an error', fakeAsync(() => {
    repositorySpy.getInfo.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: { error: 'Unable to find merchant with the provided id.' },
          })
      )
    );

    const id = 199;

    let result: any;

    usecase.execute(id).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));
});
