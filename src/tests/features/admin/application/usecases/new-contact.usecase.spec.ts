import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { CreateOneContactUseCase } from '../../../../../app/features/admin/application/usecases/new-contact.usecase';
import { ContactUI } from '../../../../../app/features/admin/domain/dtos/contact.dto';
import { MerchantContactsRepository } from '../../../../../app/features/admin/domain/repositories/contact-info.repository';

describe('CreateOneContactUsecase', () => {
  let usecase: CreateOneContactUseCase;
  let repository: jasmine.SpyObj<MerchantContactsRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;
  let errorHandlerSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        CreateOneContactUseCase,
        provideNotifierTesting(),
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: MerchantContactsRepository,
          useValue: jasmine.createSpyObj('MerchantContactsRepository', [
            'createOne',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(CreateOneContactUseCase);
    repository = TestBed.inject(
      MerchantContactsRepository
    ) as jasmine.SpyObj<MerchantContactsRepository>;

    const loader = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();

    const notifier = TestBed.inject(NotifierService);
    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();

    const errorHandler = TestBed.inject(UseCaseErrorHandler);
    errorHandlerSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(CreateOneContactUseCase);
  });

  it('should create a contact', fakeAsync(() => {
    const contact: ContactUI = {
      businessArea: 'Finance',
      merchantId: 123,
      role: 'Manager',
      id: 1,
      phone: '123456789',
      email: '<EMAIL>',
    };
    const expected = {
      businessArea: 'Finance',
      merchantId: 123,
      role: 'Manager',
      id: 1,
      email: '<EMAIL>',
      businessAreaId: 1,
      phone: '123456789',
      updateAt: '2021-09-01T00:00:00.000Z',
      createdAt: '2021-09-01T00:00:00.000Z',
    };

    repository.createOne.and.returnValue(of(expected));

    let result: any;

    usecase.execute(contact).subscribe({
      next: r => (result = r),
      error: fail,
    });

    flush();

    expect(result).toEqual(expected);
    expect(repository.createOne).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).not.toHaveBeenCalled();
  }));

  it('should handle error', fakeAsync(() => {
    const contact: ContactUI = {
      businessArea: 'Finance',
      merchantId: 123,
      role: 'Manager',
      phone: '123456789',
      id: 1,
      email: '<EMAIL>',
    };

    repository.createOne.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: 'Internal Server Error',
          })
      )
    );

    let result: any;

    usecase.execute(contact).subscribe({
      next: fail,
      error: r => (result = r),
    });

    flush();

    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(repository.createOne).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).not.toHaveBeenCalled();
  }));

  it('should handle error when an exception is thrown', fakeAsync(() => {
    const contact: ContactUI = {
      businessArea: 'Finance',
      merchantId: 123,
      role: 'Manager',
      phone: '123456789',
      id: 1,
      email: 't#kdkdk',
    };

    let result: any;

    usecase.execute(contact).subscribe({
      next: fail,
      error: r => (result = r),
    });

    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(repository.createOne).not.toHaveBeenCalled();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).not.toHaveBeenCalled();
  }));
});
