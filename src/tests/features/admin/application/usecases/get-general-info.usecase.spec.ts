import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, throwError } from 'rxjs';
import {
  defaultGeneralsInfo,
  GetGeneralInfoUseCase,
} from '../../../../../app/features/admin/application/usecases/get-general-info.usecase';
import { MerchantGeneralInfoRepository } from '../../../../../app/features/admin/domain/repositories/general-info.repository';
import { LocalGeneralsInfoRepository } from '../../local-generals-info.repository';

describe('GetGeneralInfoUsecase', () => {
  let repository: MerchantGeneralInfoRepository;
  let loader: LoaderService;
  let errorHandler: UseCaseErrorHandler;
  let usecase: GetGeneralInfoUseCase;

  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;
  let repositorySpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        {
          provide: MerchantGeneralInfoRepository,
          useClass: LocalGeneralsInfoRepository,
        },
        GetGeneralInfoUseCase,
      ],
    });

    repository = TestBed.inject(MerchantGeneralInfoRepository);
    loader = TestBed.inject(LoaderService);
    errorHandler = TestBed.inject(UseCaseErrorHandler);

    usecase = TestBed.inject(GetGeneralInfoUseCase);

    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
    repositorySpy = spyOn(repository, 'getInfo');
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should get generals info', async () => {
    repositorySpy.and.callThrough();
    const id = 199;

    const result = await lastValueFrom(usecase.execute(id));

    expect(result)
      .withContext('should return generals info as expected')
      .toEqual({
        merchantId: 199,
        mComInfoId: 196,
        mQuestionnaireId: 194,
        merchantEmail: '<EMAIL>',
        merchantAddress: 'Centro Histórico 1234 esq. Norte',
        merchantWebsite: 'zapateriasleon.com.mx',
        merchantIndustry: 'Calzado',
        merchantAov: '800-3000',
        merchantRevenue: '0-25M',
        merchantCategory: 'Moda y accesorios',
        error: null,
      });

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error when merchant id is not a number', async () => {
    repositorySpy.and.callThrough();
    const id = '199';

    const result = await lastValueFrom(
      // @ts-expect-error: testing purposes
      usecase.execute(id)
    );

    expect(result)
      .withContext(
        'should return default generals info when merchant id is not a number'
      )
      .toEqual(defaultGeneralsInfo);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error when merchant id is less than or equal to 0', async () => {
    repositorySpy.and.callThrough();
    const id = 0;

    const result = await lastValueFrom(usecase.execute(id));

    expect(result)
      .withContext(
        'should return default generals info when merchant id is less than or equal to 0'
      )
      .toEqual(defaultGeneralsInfo);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error and return default value when repository throws an error', async () => {
    repositorySpy.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: { error: 'Unable to find merchant with the provided id.' },
          })
      )
    );

    const id = 199;

    const result = await lastValueFrom(usecase.execute(id));

    expect(result)
      .withContext('should return default generals info when error')
      .toEqual(defaultGeneralsInfo);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  });
});
