import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { UpdateAccountUsecase } from '../../../../../app/features/admin/application/usecases/update-account.usecase';
import { AccountEditionRequest } from '../../../../../app/features/admin/domain/dtos/account.dto';
import { MerchantAccountRepository } from '../../../../../app/features/admin/domain/repositories/account-info.repository';

describe('UpdateAccountUsecase', () => {
  let usecase: UpdateAccountUsecase;
  let repository: jasmine.SpyObj<MerchantAccountRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;

  const accountEditionRequest: AccountEditionRequest = {
    merchantId: 1,
    bankName: 'Banco de la Nación',
    bankAccount: '123456789123456789',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        UpdateAccountUsecase,
        {
          provide: MerchantAccountRepository,
          useValue: jasmine.createSpyObj('MerchantAccountRepository', [
            'updateOneBy',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(UpdateAccountUsecase);
    repository = TestBed.inject(
      MerchantAccountRepository
    ) as jasmine.SpyObj<MerchantAccountRepository>;
    const loader = TestBed.inject(LoaderService);
    const notifier = TestBed.inject(NotifierService);
    const errorHandler = TestBed.inject(UseCaseErrorHandler);

    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(UpdateAccountUsecase);
  });

  it('should throw an error if the merchantId is undefined', fakeAsync(() => {
    const undefinedMerchantId = {
      ...accountEditionRequest,
      merchantId: undefined,
    } as any;

    let result: any;

    usecase.execute(undefinedMerchantId).subscribe({
      next: fail,
      error: error => (result = error),
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the bankName is undefined', fakeAsync(() => {
    const undefinedBankName = {
      ...accountEditionRequest,
      bankName: undefined,
    } as any;

    let result: any;

    usecase.execute(undefinedBankName).subscribe({
      next: fail,
      error: error => (result = error),
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the bankAccount is undefined', fakeAsync(() => {
    const undefinedBankAccount = {
      ...accountEditionRequest,
      bankAccount: undefined,
    } as any;

    let result: any;

    usecase.execute(undefinedBankAccount).subscribe({
      next: fail,
      error: error => (result = error),
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the merchantId is not a number', fakeAsync(() => {
    const notNumberMerchantId = {
      ...accountEditionRequest,
      merchantId: '1' as any,
    };

    let result: any;

    usecase.execute(notNumberMerchantId).subscribe({
      next: fail,
      error: error => (result = error),
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the merchantId is less than 0', fakeAsync(() => {
    const negativeMerchantId = {
      ...accountEditionRequest,
      merchantId: -1,
    };

    let result: any;

    usecase.execute(negativeMerchantId).subscribe({
      next: fail,
      error: error => (result = error),
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the bankName is empty', fakeAsync(() => {
    const emptyBankName = {
      ...accountEditionRequest,
      bankName: '',
    };

    let result: any;

    usecase.execute(emptyBankName).subscribe({
      next: fail,
      error: error => (result = error),
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the bankAccount is empty', fakeAsync(() => {
    const emptyBankAccount = {
      ...accountEditionRequest,
      bankAccount: '',
    };

    let result: any;

    usecase.execute(emptyBankAccount).subscribe({
      next: fail,
      error: error => (result = error),
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when repository throws an error', fakeAsync(() => {
    repository.updateOneBy.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            error: 'Internal Server Error',
            status: 500,
          })
      )
    );

    let result: any;

    usecase.execute(accountEditionRequest).subscribe({
      next: fail,
      error: error => (result = error),
    });

    tick();

    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should update the account successfully', fakeAsync(() => {
    repository.updateOneBy.and.returnValue(of(void 0));

    let result: any;

    usecase.execute(accountEditionRequest).subscribe({
      next: res => (result = res),
      error: fail,
    });

    tick();

    expect(result).toEqual(accountEditionRequest);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  }));
});
