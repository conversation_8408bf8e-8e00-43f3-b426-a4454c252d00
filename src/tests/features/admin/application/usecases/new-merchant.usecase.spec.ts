import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  provideUseCase<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { LocalLoader, LocalNotifier } from '@aplazo/merchant/shared-testing';
import { isEmpty, lastValueFrom, throwError } from 'rxjs';
import { NewMerchantUseCase } from '../../../../../app/features/admin/application/usecases/new-merchant.usecase';
import { Prospect } from '../../../../../app/features/admin/domain/entities/prospect';
import { NewMerchantRespository } from '../../../../../app/features/admin/domain/repositories/new-merchant.repository';
import { SHIELD_ENVIRONMENT } from '../../../../../app/features/core/infra/config/environments';
import {
  alreadyRegisteredEmailMessage,
  expectedErrorTitle,
} from '../../../../../app/features/shared/domain/messages-text';
import { LocalNewMerchant } from '../../local-new-merchant.repository';

describe('NewMerchantUseCase', () => {
  let useCase: NewMerchantUseCase;
  let repository: NewMerchantRespository<any, any>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let successSpy: jasmine.Spy;
  let warningSpy: jasmine.Spy;
  let usecaseErrorSpy: jasmine.Spy;

  const prospect: Prospect = {
    'general-info': {
      email: '<EMAIL>',
      merchantName: 'mi nuevo comercio',
      webPage: 'www.comercio.com',
      address: 'mi direccion 1234 CP 12345 CDMX',
    },
    'legal-representative': {
      name: 'Miguel Angel Taboada',
      position: 'CEO',
      phone: '**********',
    },
    'business-metrics': {
      industry: 'Belleza',
      averageTicket: '800$ - 3,000$',
      salesVolume: '0 - $25M',
      integrationType: 'PROSCAI',
    },
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        NewMerchantUseCase,
        {
          provide: NewMerchantRespository,
          useClass: LocalNewMerchant,
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        provideUseCaseErrorHandler(),
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            apiBaseUrl: 'http://localhost:3000',
          },
        },
      ],
    });

    repository = TestBed.inject(NewMerchantRespository);
    useCase = TestBed.inject(NewMerchantUseCase);
    const loader = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
    const notifier = TestBed.inject(NotifierService);
    successSpy = spyOn(notifier, 'success').and.callThrough();
    warningSpy = spyOn(notifier, 'warning').and.callThrough();
    const usecaseError = TestBed.inject(UseCaseErrorHandler);
    usecaseErrorSpy = spyOn(usecaseError, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(useCase).toBeTruthy();
    expect(useCase).toBeInstanceOf(NewMerchantUseCase);
  });

  it('should create a new merchant', async () => {
    const result = await lastValueFrom(useCase.execute(prospect));

    expect(result).toBeTruthy();
    expect(result.email).toBe(prospect['general-info'].email);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error and complete with no emission when mapper throws an error', async () => {
    const errorProspect: Partial<Prospect> = { ...prospect };
    delete errorProspect['general-info'];

    const isEmptyResult = await lastValueFrom(
      useCase.execute(errorProspect as Prospect).pipe(isEmpty())
    );

    expect(isEmptyResult)
      .withContext('should complete with no emission')
      .toBeTrue();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successSpy).toHaveBeenCalledTimes(0);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error and complete with no emission when repository throws an error', async () => {
    const repoSpy = spyOn(repository, 'create').and.returnValue(
      throwError(() => 'Deliberate Error')
    );

    const isEmptyResult = await lastValueFrom(
      useCase.execute(prospect).pipe(isEmpty())
    );

    expect(isEmptyResult)
      .withContext('should complete with no emission')
      .toBeTrue();
    expect(repoSpy).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successSpy).toHaveBeenCalledTimes(0);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error and complete with no emission when repository throws an error with registered email', async () => {
    const repoSpy = spyOn(repository, 'create').and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: { error: 'Error: El correo ya se encuentra registrado' },
          })
      )
    );

    const isEmptyResult = await lastValueFrom(
      useCase.execute(prospect).pipe(isEmpty())
    );

    expect(isEmptyResult)
      .withContext('should complete with no emission')
      .toBeTrue();
    expect(repoSpy).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(usecaseErrorSpy).toHaveBeenCalledTimes(1);
    expect(warningSpy).toHaveBeenCalledTimes(1);
    expect(warningSpy).toHaveBeenCalledWith({
      title: expectedErrorTitle,
      message: alreadyRegisteredEmailMessage,
    });
    expect(successSpy).toHaveBeenCalledTimes(0);
  });
});
