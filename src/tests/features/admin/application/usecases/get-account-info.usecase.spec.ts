import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, throwError } from 'rxjs';
import { GetAccountInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-account-info.usecase';
import { MerchantAccountRepository } from '../../../../../app/features/admin/domain/repositories/account-info.repository';
import { LocalAccountInfoRepository } from '../../local-account-info.repository';

describe('GetAccountInfoUseCase', () => {
  let repository: MerchantAccountRepository;
  let loader: LoaderService;
  let errorHandler: UseCaseErrorHandler;
  let usecase: GetAccountInfoUseCase;

  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;
  let repositorySpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: MerchantAccountRepository,
          useClass: LocalAccountInfoRepository,
        },
        GetAccountInfoUseCase,
      ],
    });

    repository = TestBed.inject(MerchantAccountRepository);
    loader = TestBed.inject(LoaderService);
    errorHandler = TestBed.inject(UseCaseErrorHandler);

    usecase = TestBed.inject(GetAccountInfoUseCase);

    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
    repositorySpy = spyOn(repository, 'getInfo');
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should get account info', async () => {
    repositorySpy.and.callThrough();
    const merchantId = 199;

    const result = await lastValueFrom(usecase.execute(merchantId));

    expect(result).toEqual({
      merchantId: 199,
      token: '148f69e2-fade-4e97-9cb3-6481c67e1962',
      email: '<EMAIL>',
      bank: 'bank',
      bankAccount: 'bankAccount',
    });
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error when merchantId is not a number', fakeAsync(() => {
    repositorySpy.and.callThrough();
    const merchantId = '199';

    let result: any;

    usecase
      .execute(
        // @ts-expect-error: Testing scenario
        merchantId
      )
      .subscribe({
        next: fail,
        error: error => {
          result = error;
        },
      });

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));

  it('should handle error when merchantId is less than 0', fakeAsync(() => {
    repositorySpy.and.callThrough();
    const merchantId = -199;

    let result: any;
    usecase.execute(merchantId).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));

  it('should handle error when merchantId is 0', fakeAsync(() => {
    repositorySpy.and.callThrough();
    const merchantId = 0;

    let result: any;

    usecase.execute(merchantId).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));

  it('should handle error when repository throws an error', fakeAsync(() => {
    repositorySpy.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: { error: 'Unable to find' },
          })
      )
    );
    const merchantId = 199;

    let result: any;

    usecase.execute(merchantId).subscribe({
      next: response => {
        result = response;
      },
      error: fail,
    });

    expect(result).toEqual({
      merchantId: 0,
      token: '',
      email: '',
      bank: '',
      bankAccount: '',
    });
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  }));
});
