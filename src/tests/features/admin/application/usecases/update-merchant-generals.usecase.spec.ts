import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { UpdateMerchantGeneralsUsecase } from '../../../../../app/features/admin/application/usecases/update-merchant-generals.usecase';
import { MerchantGeneralUpdateUIRequest } from '../../../../../app/features/admin/domain/dtos/general-info.dto';
import { MerchantGeneralInfoRepository } from '../../../../../app/features/admin/domain/repositories/general-info.repository';

describe('UpdateMerchantGeneralsUsecase', () => {
  let usecase: UpdateMerchantGeneralsUsecase;
  let repository: jasmine.SpyObj<MerchantGeneralInfoRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;
  let errorHandlerSpy: jasmine.Spy;

  const uiRequest: MerchantGeneralUpdateUIRequest = {
    merchantId: 201,
    email: '<EMAIL>',
    address: 'Calle 12 34 prohogar',
    averageOrder: '3,000$ - 10,000$',
    industry: 'Zapaterías',
    revenue: '$25M - $250M',
    website: 'www.artecktienda.com.mx',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        UpdateMerchantGeneralsUsecase,
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: MerchantGeneralInfoRepository,
          useValue: jasmine.createSpyObj('MerchantGeneralInfoRepository', [
            'updateOneBy',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(UpdateMerchantGeneralsUsecase);
    repository = TestBed.inject(
      MerchantGeneralInfoRepository
    ) as jasmine.SpyObj<MerchantGeneralInfoRepository>;
    const loader = TestBed.inject(LoaderService);
    const notifier = TestBed.inject(NotifierService);
    const errorHandler = TestBed.inject(UseCaseErrorHandler);

    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();
    errorHandlerSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should throw an error when email does not match the pattern', fakeAsync(() => {
    const invalidEmailReq = {
      ...uiRequest,
      email: 'invalid-email',
    };

    let result: unknown;

    usecase.execute(invalidEmailReq).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    tick();

    expect(result).toBeTruthy();
    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when webpage does not match the pattern', fakeAsync(() => {
    const invalidWebpageReq = {
      ...uiRequest,
      website: 'invalid-webpage',
    };

    let result: unknown;

    usecase.execute(invalidWebpageReq).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    tick();

    expect(result).toBeTruthy();
    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should throw error when industry is not one of the allowed values', fakeAsync(() => {
    const invalidIndustryReq = {
      ...uiRequest,
      industry: 'invalid-industry',
    } as any;

    let result: unknown;

    usecase.execute(invalidIndustryReq).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    tick();

    expect(result).toBeTruthy();
    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when average order is not one of the allowed values', fakeAsync(() => {
    const invalidAverageOrderReq = {
      ...uiRequest,
      averageOrder: 'invalid-average-order',
    } as any;

    let result: unknown;

    usecase.execute(invalidAverageOrderReq).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    tick();

    expect(result).toBeTruthy();
    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when revenue is not one of the allowed values', fakeAsync(() => {
    const invalidRevenueReq = {
      ...uiRequest,
      revenue: 'invalid-revenue',
    } as any;

    let result: unknown;

    usecase.execute(invalidRevenueReq).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    tick();

    expect(result).toBeTruthy();
    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when repository throws an error', fakeAsync(() => {
    repository.updateOneBy.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
          })
      )
    );

    let result: unknown;

    usecase.execute(uiRequest).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    tick();

    expect(result).toBeTruthy();
    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should update merchant general information', fakeAsync(() => {
    repository.updateOneBy.and.returnValue(of(true) as any);

    let result: unknown;
    const expectedResponse = {
      merchantId: 201,
      merchant: {
        email: '<EMAIL>',
      },
      companyInformation: {
        website: 'www.artecktienda.com.mx',
        address: 'Calle 12 34 prohogar',
      },
      questionnaire: {
        industry: 'Zapaterías',
        average_order: '3,000$ - 10,000$',
        revenue: '$25M - $250M',
      },
    };

    usecase.execute(uiRequest).subscribe({
      next: res => {
        result = res;
      },
      error: fail,
    });

    tick();

    expect(result).toEqual(expectedResponse);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(0);
  }));
});
