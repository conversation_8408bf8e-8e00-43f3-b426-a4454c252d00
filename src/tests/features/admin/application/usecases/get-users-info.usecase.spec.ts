import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, of, take, throwError } from 'rxjs';
import { GetUsersInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-users-info.usecase';
import { OperatorsUIList } from '../../../../../app/features/admin/domain/dtos/user.dto';
import { MerchantOperatorsRepository } from '../../../../../app/features/admin/domain/repositories/users-info.repository';

describe('GetUsersInfoUseCase', () => {
  let usecase: GetUsersInfoUseCase;
  let repositorySpy: jasmine.SpyObj<MerchantOperatorsRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MerchantOperatorsRepository,
          useValue: jasmine.createSpyObj('MerchantUsersInfoRepository', [
            'getInfo',
          ]),
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        GetUsersInfoUseCase,
      ],
    });

    repositorySpy = TestBed.inject(
      MerchantOperatorsRepository
    ) as jasmine.SpyObj<MerchantOperatorsRepository>;

    const loaderService = TestBed.inject(LoaderService);
    usecase = TestBed.inject(GetUsersInfoUseCase);

    showLoaderSpy = spyOn(loaderService, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loaderService, 'hide').and.callThrough();
    handleErrorSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should return the users info', async () => {
    repositorySpy.getInfo.and.returnValue(
      of([
        {
          idAccount: 20,
          username: 'columbia_centro_venta',
          role: 'ROLE_SELL_AGENT',
          userStatus: 'Active',
          userEmail: null,
          platform: 'POSUI',
          branches: [
            {
              id: 150,
              name: 'León 8',
            },
          ],
          createdAt: '2023-07-06T23:11:14.009525',
          updatedAt: '2023-07-06T23:11:14.009525',
          deletedAt: null,
        },
        {
          idAccount: 21,
          username: 'columbia_centro_venta_1',
          role: 'ROLE_SELL_AGENT',
          userStatus: 'Active',
          userEmail: null,
          platform: 'POSUI',
          branches: [
            {
              id: 151,
              name: 'León 8',
            },
          ],
          createdAt: '2023-07-06T23:43:55.912376',
          updatedAt: '2023-07-06T23:43:55.912376',
          deletedAt: null,
        },
      ])
    );

    const expected: OperatorsUIList = {
      data: {
        20: {
          merchantId: 199,
          idAccount: 20,
          username: 'columbia_centro_venta',
          role: 'Vendedor(a)',
          userStatus: 'Active',
          userEmail: null,
          platform: 'POSUI',
          branches: [
            {
              id: 150,
              name: 'León 8',
            },
          ],
          createdAt: new Date('2023-07-06T23:11:14.009525'),
          updatedAt: new Date('2023-07-06T23:11:14.009525'),
          deletedAt: null,
        },
        21: {
          merchantId: 199,
          idAccount: 21,
          username: 'columbia_centro_venta_1',
          role: 'Vendedor(a)',
          userStatus: 'Active',
          userEmail: null,
          platform: 'POSUI',
          branches: [
            {
              id: 151,
              name: 'León 8',
            },
          ],
          createdAt: new Date('2023-07-06T23:43:55.912376'),
          updatedAt: new Date('2023-07-06T23:43:55.912376'),
          deletedAt: null,
        },
      },
      merchantId: 199,
      length: 2,
    };

    const result = await lastValueFrom(usecase.execute(199).pipe(take(1)));

    expect(result).toEqual(expected);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error when merchant id is not a number', fakeAsync(() => {
    repositorySpy.getInfo.and.returnValue(of([]));
    const id = '199';

    let result: any;

    usecase
      .execute(
        // @ts-expect-error: testing purposes
        id
      )
      .subscribe({
        next: fail,
        error: error => (result = error),
      });
    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));

  it('should handle error when merchant id is less than or equal to 0', fakeAsync(() => {
    repositorySpy.getInfo.and.returnValue(of([]));
    const id = 0;

    let result: any;

    usecase.execute(id).subscribe({
      next: fail,
      error: error => (result = error),
    });
    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));

  it('should handle error and return default value when repository throws an error', fakeAsync(() => {
    repositorySpy.getInfo.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: { error: 'Unable to find merchant with the provided id.' },
          })
      )
    );

    const id = 199;

    let result: any;

    usecase.execute(id).subscribe({
      next: fail,
      error: error => (result = error),
    });
    flush();

    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));
});
