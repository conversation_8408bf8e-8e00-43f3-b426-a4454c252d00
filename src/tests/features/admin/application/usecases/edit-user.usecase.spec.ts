import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { UpdateOperatorUseCase } from '../../../../../app/features/admin/application/usecases/edit-user.usecase';
import {
  OperatorResponse,
  OperatorWithPasswordDto,
} from '../../../../../app/features/admin/domain/dtos/user.dto';
import { MerchantOperatorsRepository } from '../../../../../app/features/admin/domain/repositories/users-info.repository';

describe('UpdateOperatorUsecase', () => {
  let usecase: UpdateOperatorUseCase;
  let repository: jasmine.SpyObj<MerchantOperatorsRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;
  let warningNotifierSpy: jasmine.Spy;
  let errorHandlerSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        UpdateOperatorUseCase,
        {
          provide: MerchantOperatorsRepository,
          useValue: jasmine.createSpyObj('MerchantOperatorsRepository', [
            'updateOne',
          ]),
        },
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
      ],
    });

    usecase = TestBed.inject(UpdateOperatorUseCase);
    repository = TestBed.inject(
      MerchantOperatorsRepository
    ) as jasmine.SpyObj<MerchantOperatorsRepository>;
    const loader = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();

    const notifier = TestBed.inject(NotifierService);
    warningNotifierSpy = spyOn(notifier, 'warning').and.callThrough();
    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();

    const errorHandler = TestBed.inject(UseCaseErrorHandler);
    errorHandlerSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  const request: OperatorWithPasswordDto = {
    idAccount: 1,
    username: 'John Doe',
    userEmail: '<EMAIL>',
    role: 'PANEL_ADMIN',
    password: 'password',
    merchantId: 1,
  };

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(UpdateOperatorUseCase);
  });

  it('should update operator', fakeAsync(() => {
    const response: OperatorResponse = {
      idAccount: 1,
      branches: null,
      deletedAt: null,
      platform: 'POSUI',
      role: 'ROLE_PANEL_ADMIN',
      userEmail: '<EMAIL>',
      username: 'John Doe',
      createdAt: '2021-01-01T00:00:00.000Z',
      updatedAt: '2021-01-01T00:00:00.000Z',
      userStatus: 'Activo',
    };

    repository.updateOne.and.returnValue(of(response));

    let result: any;

    usecase.execute(request).subscribe({
      next: res => {
        result = res;
      },
      error: fail,
    });
    flush();

    expect(result).toEqual(response);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOne).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(0);
  }));

  it('should handle error when repository throws an error', fakeAsync(() => {
    repository.updateOne.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
          })
      )
    );

    let result: any;

    usecase.execute(request).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    flush();

    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOne).toHaveBeenCalledTimes(1);
    expect(warningNotifierSpy).toHaveBeenCalledTimes(0);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
  }));

  it('should handle error when parser throws an error', fakeAsync(() => {
    repository.updateOne.and.returnValue(of({} as any));

    let result: any;

    usecase
      .execute({
        ...request,
        // @ts-expect-error: Testing error handling
        merchantId: undefined,
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(errorHandlerSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOne).toHaveBeenCalledTimes(0);
    expect(warningNotifierSpy).toHaveBeenCalledTimes(0);
  }));
});
