import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalNotifier,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { EMPTY, isEmpty, lastValueFrom, take, throwError } from 'rxjs';
import { UpdateMerchantBasicsUsecase } from '../../../../../app/features/admin/application/usecases/update-merchant-basics.usecase';
import { MerchantUpdateBasicsUIRequest } from '../../../../../app/features/admin/domain/dtos/basics.dto';
import { MerchantBasicInfoRepository } from '../../../../../app/features/admin/domain/repositories/basic-info.repository';
import { LocalBasicsInfoRepository } from '../../local-basics-info.repository';

describe('UpdateMerchantBasicsUsecase', () => {
  let repository: MerchantBasicInfoRepository;
  let loader: LoaderService;
  let errorHandler: UseCaseErrorHandler;
  let usecase: UpdateMerchantBasicsUsecase;

  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;
  let repositorySpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;

  const request: MerchantUpdateBasicsUIRequest = {
    merchantId: 199,
    status: 'APPROVED',
    intType: null,
    merchantName: null,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        {
          provide: MerchantBasicInfoRepository,
          useClass: LocalBasicsInfoRepository,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        UpdateMerchantBasicsUsecase,
      ],
    });

    repository = TestBed.inject(MerchantBasicInfoRepository);
    loader = TestBed.inject(LoaderService);
    errorHandler = TestBed.inject(UseCaseErrorHandler);

    usecase = TestBed.inject(UpdateMerchantBasicsUsecase);

    successNotifierSpy = spyOn(
      TestBed.inject(NotifierService),
      'success'
    ).and.callThrough();
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
    repositorySpy = spyOn(repository, 'updateByMerchantId');
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should update merchant status', async () => {
    repositorySpy.and.callThrough();

    const req = { ...request, intType: 'API' };

    const result = await lastValueFrom(usecase.execute(req).pipe(take(1)));

    expect(result)
      .withContext('update merchant status should return void')
      .toEqual({
        merchantId: req.merchantId,
        merchantStatus: req.status as any,
        catIntegrationType: req.intType,
      });
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy)
      .withContext('should omit merchantName because it is null')
      .toHaveBeenCalledOnceWith({
        merchantId: req.merchantId,
        merchantStatus: req.status,
        catIntegrationType: req.intType,
      });
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when status is invalid', async () => {
    repositorySpy.and.callThrough();

    const req = { ...request, status: 'INVALID_STATUS' };

    const isEmptyResult = await lastValueFrom(
      usecase.execute(req).pipe(take(1), isEmpty())
    );

    expect(isEmptyResult)
      .withContext('should return empty when status is invalid')
      .toBeTrue();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(handleErrorSpy).toHaveBeenCalledTimes(1);

    expect(repositorySpy).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when service returns 400 and shows messages send by the backend', async () => {
    repositorySpy.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 400,
            error: { error: '[Message 1, message 2]' },
          })
      )
    );

    const isEmptyResult = await lastValueFrom(
      usecase.execute(request).pipe(take(1), isEmpty())
    );

    expect(isEmptyResult)
      .withContext('should return empty when service returns 400')
      .toBeTrue();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledWith(
      new RuntimeMerchantError(
        'Message 1, message 2',
        'UpdateMerchantStatusUsecase::badRequest'
      ),
      EMPTY
    );

    expect(repositorySpy).toHaveBeenCalledTimes(1);
  });

  it('should complete without stream when UI request is sent without status, intType and merchantName', async () => {
    repositorySpy.and.callThrough();

    const req = { merchantId: 199 } as any;

    const isEmptyResult = await lastValueFrom(
      usecase.execute(req).pipe(take(1), isEmpty())
    );

    expect(isEmptyResult)
      .withContext(
        'should return empty when UI request is sent without status, intType and merchantName'
      )
      .toBeTrue();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(0);
  });

  it('should complete without stream when UI request is sent with invalid intType', async () => {
    repositorySpy.and.callThrough();

    const req = { ...request, intType: 'INVALID_INT_TYPE' };

    const isEmptyResult = await lastValueFrom(
      usecase.execute(req).pipe(take(1), isEmpty())
    );

    expect(isEmptyResult)
      .withContext(
        'should return empty when UI request is sent with invalid intType'
      )
      .toBeTrue();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy).toHaveBeenCalledTimes(0);
  });
});
