import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, of, take, throwError } from 'rxjs';
import { GetInvoiceInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-invoice-info.usecase';
import { MerchantInvoiceResponse } from '../../../../../app/features/admin/domain/dtos/invoice.dto';
import { MerchantInvoiceInfoRepository } from '../../../../../app/features/admin/domain/repositories/invoice-info.repository';

describe('GetInvoiceInfoUseCase', () => {
  let usecase: GetInvoiceInfoUseCase;
  let repositorySpy: jasmine.SpyObj<MerchantInvoiceInfoRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MerchantInvoiceInfoRepository,
          useValue: jasmine.createSpyObj('MerchantInvoiceInfoRepository', [
            'getInfo',
          ]),
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        GetInvoiceInfoUseCase,
      ],
    });

    repositorySpy = TestBed.inject(
      MerchantInvoiceInfoRepository
    ) as jasmine.SpyObj<MerchantInvoiceInfoRepository>;

    const loaderService = TestBed.inject(LoaderService);
    usecase = TestBed.inject(GetInvoiceInfoUseCase);

    showLoaderSpy = spyOn(loaderService, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loaderService, 'hide').and.callThrough();
    handleErrorSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should return the invoice info', async () => {
    const response: MerchantInvoiceResponse = {
      id: 67,
      merchantId: 13,
      rfc: 'rfc',
      email: '<EMAIL>',
      paymentMethod: 'PUE',
      zipCode: 'zipCode',
      invoiceRegime: '601',
      name: 'name',
      currency: 'MXN',
      catalogPaymentForm: {
        id: 3,
        code: '03',
        name: 'Gastos en Genera',
      },
      catalogCfdi: {
        id: 15,
        code: 'G03',
        name: 'Gastos en General',
      },
      facPubGral: false,
    };

    repositorySpy.getInfo.and.returnValue(of(response));

    const result = await lastValueFrom(usecase.execute(13).pipe(take(1)));

    expect(result).toEqual({
      id: 67,
      merchantId: 13,
      rfc: 'rfc',
      email: '<EMAIL>',
      paymentMethod: 'Pago en Una Exhibición (PUE)',
      zipCode: 'zipCode',
      invoiceRegime: '601 - General de Ley Personas Morales',
      name: 'name',
      currency: 'MXN',
      paymentForm: '03: Transferencia electrónica de fondos',
      paymentFormCode: 3,
      cfdi: 'G03: Gastos en General',
      cfdiCode: 15,
      facPubGral: false,
    });
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should throw an error when merchant id is not a number', fakeAsync(() => {
    repositorySpy.getInfo.and.returnValue(of({} as any));
    const id = '13';

    let result: any;
    usecase.execute(id as any).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));

  it('should throw an error when merchant id is less than or equal to 0', fakeAsync(() => {
    repositorySpy.getInfo.and.returnValue(of({} as any));
    const id = 0;

    let result: any;

    usecase.execute(id).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));

  it('should throw error and return default value when repository throws an error', fakeAsync(() => {
    repositorySpy.getInfo.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: { error: 'Unable to find merchant with the provided id.' },
          })
      )
    );

    const id = 199;

    let result: any;
    usecase.execute(id).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));
});
