import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, of, throwError } from 'rxjs';
import { UpsertBranchUseCase } from '../../../../../app/features/admin/application/usecases/upsert-branch.usecase';
import { MerchantBranchesRepository } from '../../../../../app/features/admin/domain/repositories/branches-info.repository';

describe('UpsertBranchUsecase', () => {
  let usecase: UpsertBranchUseCase;
  let repository: jasmine.SpyObj<MerchantBranchesRepository>;
  let notifySuccessSpy: jasmine.Spy;
  let notifyErrorSpy: jasmine.Spy;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;

  const minimumRequest: {
    merchantId: number;
    name: string;
    id?: number;
    active?: boolean;
  } = {
    merchantId: 1,
    name: 'Branch Name',
  };

  const successResponse = (merchantId: number, name: string) => ({
    merchantConfigId: merchantId,
    branchId: 123,
    created: '2024-08-21T23:25:30.981425',
    updated: '2024-08-22T14:34:09.913471',
    deleted: null,
    branchName: name,
    banned: false,
  });

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideNotifierTesting(),
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        UpsertBranchUseCase,
        {
          provide: MerchantBranchesRepository,
          useValue: jasmine.createSpyObj('MerchantBranchesRepository', [
            'upsertOne',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(UpsertBranchUseCase);
    repository = TestBed.inject(
      MerchantBranchesRepository
    ) as jasmine.SpyObj<MerchantBranchesRepository>;
    const notifier = TestBed.inject(NotifierService);
    const loader = TestBed.inject(LoaderService);
    const usecaseError = TestBed.inject(UseCaseErrorHandler);

    notifySuccessSpy = spyOn(notifier, 'success').and.callThrough();
    notifyErrorSpy = spyOn(notifier, 'error').and.callThrough();
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    handleErrorSpy = spyOn(usecaseError, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(UpsertBranchUseCase);
  });

  it('should throw an error when name is null', done => {
    const reqWithoutName = { ...minimumRequest, name: undefined } as any;

    usecase.execute(reqWithoutName).subscribe({
      next: fail,
      error: err => {
        expect(err).toBeTruthy();
        expect(err).toBeInstanceOf(RuntimeMerchantError);
        expect(loaderShowSpy).toHaveBeenCalledTimes(1);
        expect(loaderHideSpy).toHaveBeenCalledTimes(1);
        expect(handleErrorSpy).toHaveBeenCalledTimes(1);
        expect(repository.upsertOne).toHaveBeenCalledTimes(0);

        done();
      },
    });
  });

  it('should throw an error when merchantId is null', done => {
    const reqWithoutMerchantId = {
      ...minimumRequest,
      merchantId: undefined,
    } as any;

    usecase.execute(reqWithoutMerchantId).subscribe({
      next: fail,
      error: err => {
        expect(err).toBeTruthy();
        expect(err).toBeInstanceOf(RuntimeMerchantError);
        expect(loaderShowSpy).toHaveBeenCalledTimes(1);
        expect(loaderHideSpy).toHaveBeenCalledTimes(1);
        expect(handleErrorSpy).toHaveBeenCalledTimes(1);
        expect(repository.upsertOne).toHaveBeenCalledTimes(0);

        done();
      },
    });
  });

  it('should throw an error when name is too short', done => {
    const reqWithShortName = { ...minimumRequest, name: 'ab' } as any;

    usecase.execute(reqWithShortName).subscribe({
      next: fail,
      error: err => {
        expect(err).toBeTruthy();
        expect(err).toBeInstanceOf(RuntimeMerchantError);
        expect(loaderShowSpy).toHaveBeenCalledTimes(1);
        expect(loaderHideSpy).toHaveBeenCalledTimes(1);
        expect(handleErrorSpy).toHaveBeenCalledTimes(1);
        expect(repository.upsertOne).toHaveBeenCalledTimes(0);

        done();
      },
    });
  });

  it('should create a branch successfully when only name is sent', async () => {
    const response = successResponse(
      minimumRequest.merchantId,
      minimumRequest.name
    );

    repository.upsertOne.and.returnValue(of(response));

    const result = await lastValueFrom(usecase.execute(minimumRequest));

    expect(result).toEqual(response);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.upsertOne).toHaveBeenCalledTimes(1);
    expect(repository.upsertOne).toHaveBeenCalledWith({
      name: minimumRequest.name,
      merchantId: minimumRequest.merchantId,
    });
    expect(notifySuccessSpy).toHaveBeenCalledTimes(1);
    expect(notifySuccessSpy).toHaveBeenCalledWith({
      title: 'Storefront creada exitosamente',
    });
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should update a branch successfully when id and active are sent', async () => {
    const request = { ...minimumRequest, id: 123, active: true };
    const response = successResponse(request.merchantId, request.name);

    repository.upsertOne.and.returnValue(of(response));

    const result = await lastValueFrom(usecase.execute(request));

    expect(result).toEqual(response);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.upsertOne).toHaveBeenCalledTimes(1);
    expect(repository.upsertOne).toHaveBeenCalledWith({
      name: request.name,
      merchantId: request.merchantId,
      id: request.id,
      active: request.active,
    });
    expect(notifySuccessSpy).toHaveBeenCalledTimes(1);
    expect(notifySuccessSpy).toHaveBeenCalledWith({
      title: 'Storefront actualizada exitosamente',
    });
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should update branch when id is sent', async () => {
    const request = { ...minimumRequest, id: 123 };
    const response = successResponse(request.merchantId, request.name);

    repository.upsertOne.and.returnValue(of(response));

    const result = await lastValueFrom(usecase.execute(request));

    expect(result).toEqual(response);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.upsertOne).toHaveBeenCalledTimes(1);
    expect(repository.upsertOne).toHaveBeenCalledWith({
      name: request.name,
      merchantId: request.merchantId,
      id: request.id,
    });
    expect(notifySuccessSpy).toHaveBeenCalledTimes(1);
    expect(notifySuccessSpy).toHaveBeenCalledWith({
      title: 'Storefront actualizada exitosamente',
    });
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should show an error when repository throws a "Merchant not found" error', fakeAsync(() => {
    repository.upsertOne.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 400,
            error: { error: 'Merchant not found' },
          })
      )
    );

    let result: any;

    usecase.execute(minimumRequest).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    tick();

    expect(result).toBeTruthy();
    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(result.status).toBe(400);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.upsertOne).toHaveBeenCalledTimes(1);
    expect(notifyErrorSpy).toHaveBeenCalledTimes(1);
    expect(notifyErrorSpy).toHaveBeenCalledWith({
      title: 'Detectamos un error',
      message: 'Revise la configuración del merchant',
    });
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when repository throws an unknown error', fakeAsync(() => {
    repository.upsertOne.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
            error: 'Internal Server Error',
          })
      )
    );

    let result: any;

    usecase.execute(minimumRequest).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    tick();

    expect(result).toBeTruthy();
    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(result.status).toBe(500);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.upsertOne).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(notifyErrorSpy).toHaveBeenCalledTimes(0);
  }));
});
