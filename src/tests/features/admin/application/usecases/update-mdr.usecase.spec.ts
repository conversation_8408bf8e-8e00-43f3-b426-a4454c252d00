import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { UpdateMdrUsecase } from '../../../../../app/features/admin/application/usecases/update-mdr.usecase';
import { MdrUIDto } from '../../../../../app/features/admin/domain/dtos/mdr.dto';
import { MerchantMdrInfoRepository } from '../../../../../app/features/admin/domain/repositories/mdr-info.repository';

describe('UpdateMdrUsecase', () => {
  let usecase: UpdateMdrUsecase;
  let repository: jasmine.SpyObj<MerchantMdrInfoRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        UpdateMdrUsecase,
        {
          provide: MerchantMdrInfoRepository,
          useValue: jasmine.createSpyObj('MerchantMdrInfoRepository', [
            'updateInfo',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(UpdateMdrUsecase);
    repository = TestBed.inject(
      MerchantMdrInfoRepository
    ) as jasmine.SpyObj<MerchantMdrInfoRepository>;
    const loader = TestBed.inject(LoaderService);
    const notifier = TestBed.inject(NotifierService);
    const errorHandler = TestBed.inject(UseCaseErrorHandler);

    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(UpdateMdrUsecase);
  });

  it('should throw an error if the request is invalid', fakeAsync(() => {
    let result: any;

    usecase
      .execute({
        feeOps: 0,
        feePct: 0.0358,
        promoFee: 0.1,
        id: 391,
        installmentFrequencyId: 3,
      })
      .subscribe({
        next: fail,
        error: error => (result = error),
      });

    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateInfo).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the repository throws an error', fakeAsync(() => {
    repository.updateInfo.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
          })
      )
    );

    let result: any;

    usecase
      .execute({
        feeOps: 0,
        feePct: 0.0358,
        promoFee: 0.1,
        id: 391,
        merchantId: 199,
        installmentFrequencyId: 3,
        promoFeeEndDate: '2022-03-24T00:00:00',
      })
      .subscribe({
        next: fail,
        error: error => (result = error),
      });

    flush();

    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  }));

  it('should update the mdr info', fakeAsync(() => {
    const request: Partial<MdrUIDto> = {
      feeOps: 0,
      feePct: 0.0358,
      promoFee: 0.1,
      id: 391,
      merchantId: 199,
      installmentFrequencyId: 3,
      promoFeeEndDate: '2022-03-24T00:00:00',
      promoFeeEndIsoDate: '2022-03-24T00:00:00Z',
    };

    repository.updateInfo.and.returnValue(of(void 0));

    let result: any;

    usecase.execute(request).subscribe({
      next: res => (result = res),
      error: fail,
    });

    flush();

    expect(result).toEqual(request);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  }));
});
