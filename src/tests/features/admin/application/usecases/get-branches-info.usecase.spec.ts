import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import {
  LoaderService,
  provideTemporal,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, of, take, throwError } from 'rxjs';
import { GetBranchesInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-branches-info.usecase';
import { MerchantBranchesRepository } from '../../../../../app/features/admin/domain/repositories/branches-info.repository';

describe('GetBranchesInfoUseCase', () => {
  let usecase: GetBranchesInfoUseCase;
  let repositorySpy: jasmine.SpyObj<MerchantBranchesRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideTemporal(),
        {
          provide: MerchantBranchesRepository,
          useValue: jasmine.createSpyObj('MerchantBranchesInfoRepository', [
            'getInfo',
          ]),
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        GetBranchesInfoUseCase,
      ],
    });

    repositorySpy = TestBed.inject(
      MerchantBranchesRepository
    ) as jasmine.SpyObj<MerchantBranchesRepository>;

    const loaderService = TestBed.inject(LoaderService);
    usecase = TestBed.inject(GetBranchesInfoUseCase);

    showLoaderSpy = spyOn(loaderService, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loaderService, 'hide').and.callThrough();
    handleErrorSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should return the branches info', async () => {
    repositorySpy.getInfo.and.returnValue(
      of([
        {
          merchantConfigId: 2,
          branchId: 5,
          created: '2021-03-17T16:13:09',
          updated: '2021-05-05T22:58:12.782914',
          deleted: null,
          branchName: 'Chalco 3',
          banned: false,
        },
        {
          merchantConfigId: 2,
          branchId: 6,
          created: '2021-03-17T16:13:09',
          updated: '2021-05-05T22:58:12.787112',
          deleted: null,
          branchName: 'Chalco 4',
          banned: false,
        },
      ])
    );

    const result = await lastValueFrom(usecase.execute(199).pipe(take(1)));

    expect(result.data[0].branchId)
      .withContext('should return sorted results in descending order')
      .toBe(6);
    expect(result.data[1].branchId)
      .withContext('should return sorted results in descending order')
      .toBe(5);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error when merchant id is not a number', async () => {
    repositorySpy.getInfo.and.returnValue(of([]));
    const id = '199';

    const result = await lastValueFrom(
      // @ts-expect-error: testing purposes
      usecase.execute(id)
    );

    expect(result)
      .withContext(
        'should return default branches info when merchant id is not a number'
      )
      .toEqual({ merchantId: 0, data: [] });

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error when merchant id is less than or equal to 0', async () => {
    repositorySpy.getInfo.and.returnValue(of([]));
    const id = 0;

    const result = await lastValueFrom(usecase.execute(id));

    expect(result)
      .withContext(
        'should return default branches info when merchant id is less than or equal to 0'
      )
      .toEqual({ merchantId: 0, data: [] });

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error and return default value when repository throws an error', async () => {
    repositorySpy.getInfo.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: { error: 'Unable to find merchant with the provided id.' },
          })
      )
    );

    const id = 199;

    const result = await lastValueFrom(usecase.execute(id));

    expect(result)
      .withContext('should return default branches info when error')
      .toEqual({ merchantId: 0, data: [] });
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  });
});
