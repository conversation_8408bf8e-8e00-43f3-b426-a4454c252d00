import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, of, take, throwError } from 'rxjs';
import { GetContactInfoUseCase } from '../../../../../app/features/admin/application/usecases/get-contact-info.usecase';
import { MerchantContactsRepository } from '../../../../../app/features/admin/domain/repositories/contact-info.repository';

describe('GetContactInfoUseCase', () => {
  let usecase: GetContactInfoUseCase;
  let repositorySpy: jasmine.SpyObj<MerchantContactsRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MerchantContactsRepository,
          useValue: jasmine.createSpyObj('MerchantContactInfoRepository', [
            'getInfo',
          ]),
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        GetContactInfoUseCase,
      ],
    });

    repositorySpy = TestBed.inject(
      MerchantContactsRepository
    ) as jasmine.SpyObj<MerchantContactsRepository>;

    const loaderService = TestBed.inject(LoaderService);
    usecase = TestBed.inject(GetContactInfoUseCase);

    showLoaderSpy = spyOn(loaderService, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loaderService, 'hide').and.callThrough();
    handleErrorSpy = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should return the contacts info', async () => {
    repositorySpy.getInfo.and.returnValue(
      of([
        {
          id: 7,
          email: '<EMAIL>',
          role: '1',
          businessArea: 'Finance',
          businessAreaId: 1,
          createdAt: '2021-09-01T00:00:00.000Z',
          updateAt: '2021-09-01T00:00:00.000Z',
          phone: '1234567890',
          merchantId: 8,
        },
        {
          id: 34,
          email: '<EMAIL>',
          role: '1',
          businessArea: 'Finance',
          businessAreaId: 1,
          createdAt: '2021-09-01T00:00:00.000Z',
          updateAt: '2021-09-01T00:00:00.000Z',
          phone: '1234567890',
          merchantId: 8,
        },
        {
          id: 6,
          email: '<EMAIL>',
          role: '1',
          businessArea: 'Finance',
          businessAreaId: 1,
          createdAt: '2021-09-01T00:00:00.000Z',
          updateAt: '2021-09-01T00:00:00.000Z',
          phone: '1234567890',
          merchantId: 8,
        },
      ])
    );

    const result = await lastValueFrom(usecase.execute(8).pipe(take(1)));

    expect(result).toEqual([
      {
        id: 7,
        email: '<EMAIL>',
        role: '1',
        businessArea: 'Finance',
        phone: '1234567890',
        merchantId: 8,
        updateAt: '2021-09-01T00:00:00.000Z',
      },
      {
        id: 34,
        email: '<EMAIL>',
        role: '1',
        businessArea: 'Finance',
        phone: '1234567890',
        merchantId: 8,
        updateAt: '2021-09-01T00:00:00.000Z',
      },
      {
        id: 6,
        email: '<EMAIL>',
        role: '1',
        businessArea: 'Finance',
        phone: '1234567890',
        merchantId: 8,
        updateAt: '2021-09-01T00:00:00.000Z',
      },
    ]);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle error when merchant id is not a number', async () => {
    repositorySpy.getInfo.and.returnValue(of({} as any));
    const id = '8';

    const result = await lastValueFrom(
      // @ts-expect-error: testing purposes
      usecase.execute(id)
    );

    expect(result)
      .withContext(
        'should return default contacts info when merchant id is not a number'
      )
      .toEqual([]);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error when merchant id is less than or equal to 0', async () => {
    repositorySpy.getInfo.and.returnValue(of({} as any));
    const id = 0;

    const result = await lastValueFrom(usecase.execute(id));

    expect(result)
      .withContext(
        'should return default contacts info when merchant id is less than or equal to 0'
      )
      .toEqual([]);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(0);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error and return default value when repository throws an error', async () => {
    repositorySpy.getInfo.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: { error: 'Unable to find merchant with the provided id.' },
          })
      )
    );

    const id = 199;

    const result = await lastValueFrom(usecase.execute(id));

    expect(result)
      .withContext('should return default contacts info when error')
      .toEqual([]);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repositorySpy.getInfo).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
  });
});
