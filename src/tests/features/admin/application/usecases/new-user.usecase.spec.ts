import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { CreateOperatorUseCase } from '../../../../../app/features/admin/application/usecases/new-user.usecase';
import { OperatorWithPasswordDto } from '../../../../../app/features/admin/domain/dtos/user.dto';
import { MerchantOperatorsRepository } from '../../../../../app/features/admin/domain/repositories/users-info.repository';

describe('CreateOperatorUseCase', () => {
  let usecase: CreateOperatorUseCase;
  let repository: jasmine.SpyObj<MerchantOperatorsRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        CreateOperatorUseCase,
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: MerchantOperatorsRepository,
          useValue: jasmine.createSpyObj('MerchantOperatorsRepository', [
            'createOne',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(CreateOperatorUseCase);
    repository = TestBed.inject(
      MerchantOperatorsRepository
    ) as jasmine.SpyObj<MerchantOperatorsRepository>;

    const loader = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();

    const errorHandler = TestBed.inject(UseCaseErrorHandler);
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();

    const notifier = TestBed.inject(NotifierService);
    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();
  });

  const requestUI: OperatorWithPasswordDto = {
    merchantId: 199,
    username: 'username',
    password: 'password',
    role: 'Vendedor(a)',
    userEmail: '<EMAIL>',
    branchesId: [23],
  };

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(CreateOperatorUseCase);
  });

  it('should throws an error when no merchant id is provided', fakeAsync(() => {
    const req = {
      ...requestUI,

      merchantId: undefined,
    } as any;

    let result: any;

    usecase.execute(req).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });
    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should throws an error when the repository throws an error by email already exists', fakeAsync(() => {
    repository.createOne.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 400,
            error: { error: 'Email already exists' },
          })
      )
    );

    let result: any;

    usecase.execute(requestUI).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });
    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.message).toBe('El correo ya fue registrado');
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should throws an error when the repository throws an error by username already exists', fakeAsync(() => {
    repository.createOne.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 400,
            error: { error: 'Username already exists' },
          })
      )
    );

    let result: any;

    usecase.execute(requestUI).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });
    flush();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.message).toBe('El usuario ya existe');
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should throws an error when the repository throws an error', fakeAsync(() => {
    repository.createOne.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: 'Internal server error',
          })
      )
    );

    let result: any;

    usecase.execute(requestUI).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });
    flush();

    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));

  it('should create a new operator', fakeAsync(() => {
    const expected = {
      idAccount: 123,
      username: requestUI.username,
      role: requestUI.role,
      userStatus: 'Activo',
      userEmail: requestUI.userEmail,
      platform: 'POSUI',
      branches: null,
      createdAt: '2024-08-21T23:25:30.981425',
      updatedAt: '2024-08-21T23:25:30.981425',
      deletedAt: null,
    };
    repository.createOne.and.returnValue(of(expected));

    let result: any;

    usecase.execute(requestUI).subscribe({
      next: res => {
        result = res;
      },
      error: fail,
    });
    flush();

    expect(result).toEqual(expected);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  }));
});
