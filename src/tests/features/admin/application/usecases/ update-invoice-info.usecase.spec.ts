import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { UpdateInvoiceInfoUsecase } from '../../../../../app/features/admin/application/usecases/update-invoice-info.usecase';
import { InvoiceUI } from '../../../../../app/features/admin/domain/dtos/invoice.dto';
import { MerchantInvoiceInfoRepository } from '../../../../../app/features/admin/domain/repositories/invoice-info.repository';

describe('UpdateInvoiceInfoUsecase', () => {
  let usecase: UpdateInvoiceInfoUsecase;
  let repository: jasmine.SpyObj<MerchantInvoiceInfoRepository>;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;
  let successNotifierSpy: jasmine.Spy;

  const invoiceUI: InvoiceUI = {
    id: 67,
    merchantId: 13,
    rfc: 'XAXX010101000',
    email: '<EMAIL>',
    paymentMethod: 'Pago en Una Exhibición (PUE)',
    zipCode: '02000',
    invoiceRegime: '601 - General de Ley Personas Morales',
    name: 'name',
    currency: 'MXN',
    paymentForm: '03: Transferencia electrónica de fondos',
    paymentFormCode: 3,
    cfdi: 'G03: Gastos en General',
    cfdiCode: 15,
    facPubGral: false,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        UpdateInvoiceInfoUsecase,
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: MerchantInvoiceInfoRepository,
          useValue: jasmine.createSpyObj('MerchantInvoiceInfoRepository', [
            'updateOneBy',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(UpdateInvoiceInfoUsecase);
    repository = TestBed.inject(
      MerchantInvoiceInfoRepository
    ) as jasmine.SpyObj<MerchantInvoiceInfoRepository>;
    const loader = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();

    const notifier = TestBed.inject(NotifierService);
    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();

    const errorHandler = TestBed.inject(UseCaseErrorHandler);
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(UpdateInvoiceInfoUsecase);
  });

  it('should throw an error when the request not include the merchantId', fakeAsync(() => {
    const request = {
      ...invoiceUI,
      merchantId: undefined,
    } as any;

    let result: any;

    usecase.execute(request).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.message).toBe(
      'Detectamos un error: merchantId is undefined or null, property <merchantId> is empty'
    );
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when the request has a merchantId that is not a number', fakeAsync(() => {
    const request = {
      ...invoiceUI,
      merchantId: '13',
    } as any;

    let result: any;

    usecase.execute(request).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.message).toBe(
      'El id del comercio debe ser un número entero válido.'
    );
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when the request has a zipCode that is not a number', fakeAsync(() => {
    const request = {
      ...invoiceUI,
      zipCode: 'zipCode',
    } as any;

    let result: any;

    usecase.execute(request).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.message).toBe('El código postal debe tener 5 dígitos.');
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when the request has an invalid email', fakeAsync(() => {
    const request = {
      ...invoiceUI,
      email: 'email',
    } as any;

    let result: any;

    usecase.execute(request).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.message).toBe(
      'Ingrese correo electrónico válido, Ej: <EMAIL>'
    );
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when the request has an invalid RFC', fakeAsync(() => {
    const request = {
      ...invoiceUI,
      rfc: 'rfc',
    } as any;

    let result: any;

    usecase.execute(request).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.message).toBe(
      'El valor rfc no es un RFC válido. Para personas morales debe tener 12 caracteres y para personas físicas 13. Verifica que el RFC sea correcto.'
    );
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when the request has an invalid invoiceRegime', fakeAsync(() => {
    const request = {
      ...invoiceUI,
      invoiceRegime: 'invalid',
    } as any;

    let result: any;

    usecase.execute(request).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.message).toBe('El régimen fiscal no es válido.');
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when the request has an invalid paymentMethodId', fakeAsync(() => {
    const request = {
      ...invoiceUI,
      paymentMethod: 'invalid',
    } as any;

    let result: any;

    usecase.execute(request).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.message).toBe('El método de pago no es válido.');
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when the request has an invalid paymentFormId', fakeAsync(() => {
    const request = {
      ...invoiceUI,
      paymentForm: 'invalid',
    } as any;

    let result: any;

    usecase.execute(request).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.message).toBe('La forma de pago no es válida.');
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when the request has an invalid cfdiId', fakeAsync(() => {
    const request = {
      ...invoiceUI,
      cfdi: 'invalid',
    } as any;

    let result: any;

    usecase.execute(request).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.message).toBe('El CFDI no es válido.');
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when the repository throws an error', fakeAsync(() => {
    const request = invoiceUI;

    repository.updateOneBy.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: 'Internal Server Error',
            statusText: 'Internal Server Error',
          })
      )
    );

    let result: any;

    usecase.execute(request).subscribe({
      next: fail,
      error: error => {
        result = error;
      },
    });

    tick();

    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(result.status).toBe(500);
    expect(result.statusText).toBe('Internal Server Error');
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(1);
  }));

  it('should update the invoice info successfully', fakeAsync(() => {
    const request = invoiceUI;

    repository.updateOneBy.and.returnValue(of(undefined));

    let result: any;

    usecase.execute(request).subscribe({
      next: response => {
        result = response;
      },
      error: fail,
    });

    tick();

    expect(result).toEqual(invoiceUI);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);
    expect(repository.updateOneBy).toHaveBeenCalledTimes(1);
    expect(handleErrorSpy).toHaveBeenCalledTimes(0);
  }));
});
