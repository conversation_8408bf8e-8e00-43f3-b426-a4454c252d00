import { TestBed } from '@angular/core/testing';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalUsecaseErrorHandler,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, take, throwError } from 'rxjs';
import { SearchByIdOrNameUseCase } from '../../../../../app/features/admin/application/usecases/search-by-id-name.usecase';
import { SearchMerchantByRepository } from '../../../../../app/features/admin/domain/repositories/search-merchant-by.repository';
import { LocalSearchBy } from '../../local-search-by.repository';

describe('SearchByIdOrNameUseCase', () => {
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let handleSpy: jasmine.Spy;
  let searchBySpy: jasmine.Spy;
  let usecase: SearchByIdOrNameUseCase;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: UseCaseErrorHandler,
          useClass: LocalUsecaseErrorHandler,
        },
        {
          provide: SearchMerchantByRepository,
          useClass: LocalSearchBy,
        },
        SearchByIdOrNameUseCase,
      ],
    });

    const loaderService = TestBed.inject(LoaderService);
    showLoaderSpy = spyOn(loaderService, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loaderService, 'hide').and.callThrough();

    const errorHandler = TestBed.inject(UseCaseErrorHandler);
    handleSpy = spyOn(errorHandler, 'handle').and.callThrough();

    const repository = TestBed.inject(SearchMerchantByRepository);
    searchBySpy = spyOn(repository, 'searchBy');

    usecase = TestBed.inject(SearchByIdOrNameUseCase);
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(SearchByIdOrNameUseCase);
  });

  it('should return an empty array when no id or name is provided', async () => {
    const result = await lastValueFrom(
      usecase.execute({ id: 0, name: '' }).pipe(take(1))
    );

    expect(result).toEqual([]);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleSpy).toHaveBeenCalledTimes(1);
    expect(searchBySpy).toHaveBeenCalledTimes(0);
  });

  it('should return an empty array when an error occurs', async () => {
    searchBySpy.and.returnValue(throwError(() => 'Deliberate error'));

    const result = await lastValueFrom(
      usecase.execute({ id: 0, name: 'Zapat' }).pipe(take(1))
    );

    expect(result).toEqual([]);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleSpy).toHaveBeenCalledTimes(1);
    expect(searchBySpy).toHaveBeenCalledTimes(1);
  });

  it('should return an array with the search results', async () => {
    searchBySpy.and.callThrough();
    const result = await lastValueFrom(
      usecase.execute({ id: 0, name: 'Zapat' }).pipe(take(1))
    );

    expect(result).toEqual([
      {
        merchantId: 199,
        merchantName: 'Zapaterias Leon POS',
        intType: 'POSUI',
        status: 'APPROVED',
      },
    ]);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleSpy).toHaveBeenCalledTimes(0);
    expect(searchBySpy).toHaveBeenCalledTimes(1);
  });

  it('should handle error, return empty array and show an warn notification when the execute is called with a name with less than 3 characters', async () => {
    const result = await lastValueFrom(
      usecase.execute({ id: 0, name: 'Za' }).pipe(take(1))
    );

    expect(result).toEqual([]);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(handleSpy).toHaveBeenCalledTimes(1);
    expect(searchBySpy).toHaveBeenCalledTimes(0);
  });
});
