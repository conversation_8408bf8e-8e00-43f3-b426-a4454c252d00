import {
  AdminMerchant<PERSON><PERSON>per,
  invalidAvgTicketError,
  invalidIndustryError,
  invalidIntTypeError,
  invalidYearlySalesError,
  newMerchantUIToRequestDefaultError,
} from '../../../../../app/features/admin/application/services/admin-mapper';
import {
  BusinessMetrics,
  GeneralInfo,
  LegalRepresentative,
  Prospect,
} from '../../../../../app/features/admin/domain/entities/prospect';

describe('AdminMapper', () => {
  const prospect: Prospect = {
    'general-info': {
      email: '<EMAIL>',
      merchantName: 'mi nuevo comercio',
      webPage: 'www.comercio.com',
      address: 'mi direccion 1234 CP 12345 CDMX',
    },
    'legal-representative': {
      name: '<PERSON>',
      position: 'CEO',
      phone: '1234567890',
    },
    'business-metrics': {
      industry: 'Accesorios de Moda',
      averageTicket: '800$ - 3,000$',
      salesVolume: '0 - $25M',
      integrationType: 'PROSCAI',
    },
  };

  it('should throw an error when general-info is missing', () => {
    const errorProspect: Partial<Prospect> = { ...prospect };
    delete errorProspect['general-info'];

    expect(() =>
      AdminMerchantMapper.fromUIToRequest(errorProspect as Prospect)
    ).toThrowError(newMerchantUIToRequestDefaultError);
  });

  it('should throw an error when legal-representative is missing', () => {
    const errorProspect: Partial<Prospect> = { ...prospect };
    delete errorProspect['legal-representative'];

    expect(() =>
      AdminMerchantMapper.fromUIToRequest(errorProspect as Prospect)
    ).toThrowError(newMerchantUIToRequestDefaultError);
  });

  it('should throw an error when business-metrics is missing', () => {
    const errorProspect: Partial<Prospect> = { ...prospect };
    delete errorProspect['business-metrics'];

    expect(() =>
      AdminMerchantMapper.fromUIToRequest(errorProspect as Prospect)
    ).toThrowError(newMerchantUIToRequestDefaultError);
  });

  it('should throw an error when general-info.email is missing', () => {
    const errorGeneralInfo: Partial<GeneralInfo> = {
      ...prospect['general-info'],
    };
    delete errorGeneralInfo.email;

    const prospectTest: Prospect = { ...prospect };
    prospectTest['general-info'] = errorGeneralInfo as GeneralInfo;

    expect(() =>
      AdminMerchantMapper.fromUIToRequest(prospectTest)
    ).toThrowError(newMerchantUIToRequestDefaultError);
  });

  it('should throw an error when legal-representative.name is missing', () => {
    const errorLegalRep: Partial<LegalRepresentative> = {
      ...prospect['legal-representative'],
    };
    delete errorLegalRep.name;

    const prospectTest: Prospect = { ...prospect };
    prospectTest['legal-representative'] = errorLegalRep as LegalRepresentative;

    expect(() =>
      AdminMerchantMapper.fromUIToRequest(prospectTest)
    ).toThrowError(newMerchantUIToRequestDefaultError);
  });

  it('should throw an error when business-metrics.industry is missing', () => {
    const errorBusinessMetrics: Partial<BusinessMetrics> = {
      ...prospect['business-metrics'],
    };
    delete errorBusinessMetrics.industry;

    const prospectTest: Prospect = { ...prospect };
    prospectTest['business-metrics'] = errorBusinessMetrics as BusinessMetrics;

    expect(() =>
      AdminMerchantMapper.fromUIToRequest(prospectTest)
    ).toThrowError(newMerchantUIToRequestDefaultError);
  });

  it('should throw an error when LegalRepresentative.phone is not a number', () => {
    const errorLegalRep: Partial<LegalRepresentative> = {
      ...prospect['legal-representative'],
    };
    errorLegalRep.phone = '123456789a';

    const prospectTest: Prospect = { ...prospect };
    prospectTest['legal-representative'] = errorLegalRep as LegalRepresentative;

    expect(() =>
      AdminMerchantMapper.fromUIToRequest(prospectTest)
    ).toThrowError('El valor no es un número válido.');
  });

  it('should throw an error when an invalid industry is selected', () => {
    const errorBusinessMetrics: Partial<BusinessMetrics> = {
      ...prospect['business-metrics'],
    };
    errorBusinessMetrics.industry = 'Invalid Industry';

    const prospectTest: Prospect = { ...prospect };
    prospectTest['business-metrics'] = errorBusinessMetrics as BusinessMetrics;

    expect(() =>
      AdminMerchantMapper.fromUIToRequest(prospectTest)
    ).toThrowError(invalidIndustryError);
  });

  it('should throw an error when an invalid integration type is selected', () => {
    const errorBusinessMetrics: Partial<BusinessMetrics> = {
      ...prospect['business-metrics'],
    };
    errorBusinessMetrics.integrationType = 'Invalid Integration Type';

    const prospectTest: Prospect = { ...prospect };
    prospectTest['business-metrics'] = errorBusinessMetrics as BusinessMetrics;

    expect(() =>
      AdminMerchantMapper.fromUIToRequest(prospectTest)
    ).toThrowError(invalidIntTypeError);
  });

  it('should throw an error when an invalid average ticket is selected', () => {
    const errorBusinessMetrics: Partial<BusinessMetrics> = {
      ...prospect['business-metrics'],
    };
    errorBusinessMetrics.averageTicket = 'Invalid Average Ticket';

    const prospectTest: Prospect = { ...prospect };
    prospectTest['business-metrics'] = errorBusinessMetrics as BusinessMetrics;

    expect(() =>
      AdminMerchantMapper.fromUIToRequest(prospectTest)
    ).toThrowError(invalidAvgTicketError);
  });

  it('should throw an error when an invalid sales volume is selected', () => {
    const errorBusinessMetrics: Partial<BusinessMetrics> = {
      ...prospect['business-metrics'],
    };
    errorBusinessMetrics.salesVolume = 'Invalid Sales Volume';

    const prospectTest: Prospect = { ...prospect };
    prospectTest['business-metrics'] = errorBusinessMetrics as BusinessMetrics;

    expect(() =>
      AdminMerchantMapper.fromUIToRequest(prospectTest)
    ).toThrowError(invalidYearlySalesError);
  });

  it('should return a valid request when all fields are valid', () => {
    const result = AdminMerchantMapper.fromUIToRequest(prospect);

    expect(result).toEqual({
      basic: {
        email: '<EMAIL>',
        businessName: 'mi nuevo comercio',
        businessWebpage: 'www.comercio.com',
        businessAddress: 'mi direccion 1234 CP 12345 CDMX',
        contactName: 'Miguel Angel Taboada',
        position: 'CEO',
        phone: '521234567890',
        industry: 'Accesorios de Moda',
        avgTicket: '800$ - 3,000$',
        yearlySales: '0 - $25M',
        intType: 'PROSCAI',
      },
    });
  });
});
