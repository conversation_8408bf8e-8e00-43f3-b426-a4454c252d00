import {
  OperatorResponse,
  OperatorWithPasswordDto,
  parseIsoDate,
  serializeUserResponse,
  toRepositoryCreateRequest,
} from '../../../../../app/features/admin/domain/dtos/user.dto';

describe('UserDto', () => {
  describe('parseIsoDate', () => {
    it('should return null when date is invalid', () => {
      const result = parseIsoDate('invalid date');

      expect(result).toBeNull();
    });

    it('should return a date when date is valid', () => {
      const result = parseIsoDate('2021-01-01T00:00:00.000Z');

      expect(result).toEqual(new Date('2021-01-01'));
    });

    it('should return null when date is null', () => {
      const result = parseIsoDate(null);

      expect(result).toBeNull();
    });
  });

  describe('serializeUserResponse', () => {
    it('should return a valid user response', () => {
      const response: OperatorResponse = {
        idAccount: 1,
        branches: null,
        deletedAt: null,
        platform: 'POSUI',
        role: 'ROLE_PANEL_ADMIN',
        userEmail: '<EMAIL>',
        username: 'John Doe',
        createdAt: '2021-01-01T00:00:00.000Z',
        updatedAt: '2021-01-01T00:00:00.000Z',
        userStatus: 'Activo',
      };

      const result = serializeUserResponse(199, response);

      expect(result).toEqual({
        merchantId: 199,
        branches: null,
        idAccount: 1,
        role: 'PANEL_ADMIN',
        userEmail: '<EMAIL>',
        username: 'John Doe',
        platform: 'POSUI',
        userStatus: 'Activo',
        createdAt: new Date('2021-01-01'),
        updatedAt: new Date('2021-01-01'),
        deletedAt: null,
      });
    });
  });

  describe('toRepositoryRequest', () => {
    const dto: OperatorWithPasswordDto = {
      merchantId: 123,
      role: 'Gerente',
      userEmail: '<EMAIL>',
      username: 'John Doe',
      password: '12344',
      branchesId: [1234],
    };

    it('should return a valid request', () => {
      const result = toRepositoryCreateRequest(dto);

      expect(result).toEqual({
        id: undefined,
        merchantId: 123,
        role: 'ROLE_MANAGER',
        email: '<EMAIL>',
        username: 'John Doe',
        password: '12344',
        branchesId: [1234],
      });
    });

    it('should throw an error when merchantId is not provided', () => {
      const invalidDto = { ...dto, merchantId: undefined } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'Detectamos errores:: merchantId is undefined or null'
      );
    });

    it('should throw an error when password id empty string', () => {
      const invalidDto = { ...dto, password: '    ' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El password es requerido'
      );
    });

    it('should throw an error when username is empty string', () => {
      const invalidDto = { ...dto, username: '    ' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El username es requerido'
      );
    });

    it('should throw an error when merchantId is not a number', () => {
      const invalidDto = { ...dto, merchantId: 'invalid' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El id del comercio debe ser un número entero válido.'
      );
    });

    it('should throw an error when merchantId is less than 0', () => {
      const invalidDto = { ...dto, merchantId: -1 } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El id del comercio debe ser un número entero válido mayor de 0.'
      );
    });

    it('should throw an error when email is empty string', () => {
      const invalidDto = { ...dto, userEmail: '    ' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'Ingrese correo electrónico válido, Ej: <EMAIL>'
      );
    });

    it('should throw an error when role is empty string', () => {
      const invalidDto = { ...dto, role: '    ' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El rol proporcionado no es válido'
      );
    });

    it('should throw an error when role is invalid', () => {
      const invalidDto = { ...dto, role: 'invalid' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El rol proporcionado no es válido'
      );
    });

    it('should throw an error when branchesId is not provided and role is pos', () => {
      const invalidDto = {
        ...dto,
        branchesId: undefined,
        role: 'Vendedor(a)',
      } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'Una selección de tiendas es requerida para el rol que esta intentando crear'
      );
    });
  });

  describe('toRepositoryUpdateRequest', () => {
    const dto: OperatorWithPasswordDto = {
      merchantId: 123,
      role: 'Gerente',
      userEmail: '<EMAIL>',
      username: 'John Doe',
      password: '12344',
      branchesId: [1234],
    };

    it('should return a valid request', () => {
      const result = toRepositoryCreateRequest(dto);

      expect(result).toEqual({
        id: undefined,
        merchantId: 123,
        role: 'ROLE_MANAGER',
        email: '<EMAIL>',
        username: 'John Doe',
        password: '12344',
        branchesId: [1234],
      });
    });

    it('should throw an error when merchantId is not provided', () => {
      const invalidDto = { ...dto, merchantId: undefined } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'Detectamos errores:: merchantId is undefined or null'
      );
    });

    it('should throw an error when password id empty string', () => {
      const invalidDto = { ...dto, password: '    ' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El password es requerido'
      );
    });

    it('should throw an error when username is empty string', () => {
      const invalidDto = { ...dto, username: '    ' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El username es requerido'
      );
    });

    it('should throw an error when merchantId is not a number', () => {
      const invalidDto = { ...dto, merchantId: 'invalid' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El id del comercio debe ser un número entero válido.'
      );
    });

    it('should throw an error when merchantId is less than 0', () => {
      const invalidDto = { ...dto, merchantId: -1 } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El id del comercio debe ser un número entero válido mayor de 0.'
      );
    });

    it('should throw an error when email is empty string', () => {
      const invalidDto = { ...dto, userEmail: '    ' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'Ingrese correo electrónico válido, Ej: <EMAIL>'
      );
    });

    it('should throw an error when role is empty string', () => {
      const invalidDto = { ...dto, role: '    ' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El rol proporcionado no es válido'
      );
    });

    it('should throw an error when role is invalid', () => {
      const invalidDto = { ...dto, role: 'invalid' } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'El rol proporcionado no es válido'
      );
    });

    it('should throw an error when branchesId is not provided and role is pos', () => {
      const invalidDto = {
        ...dto,
        branchesId: undefined,
        role: 'Vendedor(a)',
      } as any;

      expect(() => toRepositoryCreateRequest(invalidDto)).toThrowError(
        'Una selección de tiendas es requerida para el rol que esta intentando crear'
      );
    });
  });
});
