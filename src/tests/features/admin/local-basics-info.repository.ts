import { HttpErrorResponse } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { MerchantBasicsResponse } from '../../../app/features/admin/domain/dtos/basics.dto';
import { MerchantBasicInfoRepository } from '../../../app/features/admin/domain/repositories/basic-info.repository';
import data from './merchant.db.json';

export class LocalBasicsInfoRepository extends MerchantBasicInfoRepository {
  readonly #data = data;

  getInfo(args: number): Observable<MerchantBasicsResponse> {
    const filtered = this.#data.find(item => item.id === args);

    if (!filtered) {
      throw new HttpErrorResponse({
        status: 500,
        error: { error: 'Unable to find' },
      });
    }

    const parsed: MerchantBasicsResponse = {
      id: filtered.id,
      idMComInfo: filtered.idMComInfo,
      name: filtered.name,
      status: filtered.status,
      intType: filtered.intType,
      created: filtered.created,
      updated: filtered.updated,
    };

    return of(parsed);
  }

  updateByMerchantId(request: {
    merchantId: number;
    merchantName?: string;
    merchantStatus?: string;
    catIntegrationType?: string;
  }): Observable<void> {
    if (!request.merchantId) {
      return throwError(
        () =>
          new HttpErrorResponse({
            status: 400,
            error: { error: 'Unable to update' },
            statusText: 'Bad Request',
          })
      );
    }
    if (
      request.merchantName &&
      request.merchantStatus &&
      request.catIntegrationType
    ) {
      return throwError(
        () =>
          new HttpErrorResponse({
            status: 400,
            error: { error: 'Nothing to update' },
            statusText: 'Bad Request',
          })
      );
    }

    console.log(
      'BasicInfoRepository::updateStatus::',
      request.merchantId,
      '::',
      request.merchantStatus,
      '::',
      request.merchantName,
      '::',
      request.catIntegrationType
    );

    return of(void 0);
  }
}
