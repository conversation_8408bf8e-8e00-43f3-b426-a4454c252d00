import { HttpErrorResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import {
  AccountEditionRequest,
  AccountInfoResponse,
} from '../../../app/features/admin/domain/dtos/account.dto';
import { MerchantAccountRepository } from '../../../app/features/admin/domain/repositories/account-info.repository';
import data from './merchant.db.json';

export class LocalAccountInfoRepository implements MerchantAccountRepository {
  readonly #data = data;

  getInfo(merchantId: number): Observable<AccountInfoResponse> {
    const filtered = this.#data.find(itm => itm.id === merchantId);

    if (!filtered) {
      throw new HttpErrorResponse({
        status: 500,
        error: {
          error: 'Unable to find merchant with the provided id.',
        },
      });
    }

    const parsed: AccountInfoResponse = {
      idAccount: filtered.idAccount,
      idApiToken: filtered.idApiToken,
      merchantId: filtered.id,
      token: filtered.token,
      emailAccount: filtered.merchantEmail,
      idBilling: filtered.idBilling,
      bank: filtered.bank,
      bankAccount: filtered.bankAccount,
    };

    return of(parsed);
  }

  updateOneBy(request: AccountEditionRequest): Observable<void> {
    console.log('AccountEditionRequest', request);

    return of(void 0);
  }
}
