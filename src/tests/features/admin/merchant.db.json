[{"id": 13, "idMComInfo": 10, "idMQuestionnaire": 10, "name": "<PERSON><PERSON><PERSON>", "status": "APPROVED", "intType": "API", "created": "2020-09-25T19:04:16.899518", "updated": "2022-03-24T02:15:58.888492", "merchantEmail": "<EMAIL>", "merchantAddress": "Guerrero 266 59000 Sahuayo, Michoacan", "merchantWebsite": "www.calzadoshys.com/", "merchantIndustry": "<PERSON><PERSON><PERSON>", "merchantAov": "800-3000", "merchantRevenue": "0-25M", "merchantCategory": "S/C", "bank": "bank", "bankAccount": "bankAccount", "idAccount": 17840, "idApiToken": 145, "idBilling": 17840, "token": "148f69e2-fade-4e97-9cb3-6481c67e1962"}, {"id": 199, "idMComInfo": 196, "idMQuestionnaire": 194, "name": "Zapaterias Leon POS", "status": "APPROVED", "intType": "POSUI", "created": "2021-04-07T15:12:57.19396", "updated": "2022-03-24T02:15:58.888492", "merchantEmail": "<EMAIL>", "merchantAddress": "Centro Histórico 1234 esq. Norte", "merchantWebsite": "zapateriasleon.com.mx", "merchantIndustry": "<PERSON><PERSON><PERSON>", "merchantAov": "800-3000", "merchantRevenue": "0-25M", "merchantCategory": "Moda y accesorios", "bank": "bank", "bankAccount": "bankAccount", "idAccount": 17841, "idApiToken": 146, "idBilling": 17841, "token": "148f69e2-fade-4e97-9cb3-6481c67e1962"}]