import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { matchByMenuGuard } from '../../../../../app/features/board/infra/guards/match-by-menu.guard';
import { HelicarrierRoute } from '../../../../../app/features/core/domain/route-names';
import { MenuStore } from '../../../../../app/features/shared/application/services/menu.store';

const setup = async (
  mockMenu: HelicarrierRoute[],
  toMatch: HelicarrierRoute
) => {
  TestBed.configureTestingModule({
    providers: [
      {
        provide: MenuStore,
        useValue: {
          menu$: of(mockMenu),
        },
      },
    ],
  });

  return await TestBed.runInInjectionContext(() =>
    matchByMenuGuard(toMatch)({} as any, {} as any)
  );
};

describe('SimpleMenuStore', () => {
  it('should return true when route is in menu', async () => {
    const result = await setup(
      ['merchant_onboarding', 'merchant_payment'],
      'merchant_onboarding'
    );

    expect(result)
      .withContext('Expected to return true when route is in menu')
      .toBeTrue();
  });

  it('should return false when route is not in menu', async () => {
    const result = await setup(
      ['merchant_onboarding', 'merchant_payment'],
      // @ts-expect-error: testing invalid route
      'merchant_management'
    );

    expect(result)
      .withContext('Expected to return false when route is not in menu')
      .toBeFalse();
  });
});
