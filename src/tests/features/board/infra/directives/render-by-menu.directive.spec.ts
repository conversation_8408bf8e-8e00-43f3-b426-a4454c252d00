import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { RenderByMenuDirective } from '../../../../../app/features/board/infra/directives/render-by-menu.directive';
import {
  HelicarrierRoute,
  ROUTES_CONFIG,
} from '../../../../../app/features/core/domain/route-names';
import { MenuStore } from '../../../../../app/features/shared/application/services/menu.store';

@Component({
  selector: 'app-host-test',
  imports: [RenderByMenuDirective],
  template: `
    <div *renderByMenu="[routes.payments]">
      <p>Test</p>
    </div>
  `,
})
export class HostTestComponent {
  routes: typeof ROUTES_CONFIG = ROUTES_CONFIG;
}

const setup = (menu?: HelicarrierRoute[]) => {
  if (menu) {
    TestBed.overrideProvider(MenuStore, {
      useValue: {
        getMenu: () => menu,
      },
    });
  }

  const fixture = TestBed.createComponent(HostTestComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  return { fixture, component };
};

describe('RenderByMenuDirective', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [RenderByMenuDirective, HostTestComponent],
      providers: [
        {
          provide: MenuStore,
          useValue: {
            getMenu: () => ['merchant_payment'],
          },
        },
      ],
    });
  });

  it('should create', () => {
    const { component } = setup();

    expect(component).toBeTruthy();
  });

  it('should render the content if the menu has the route', () => {
    const { fixture } = setup(['merchant_payment']);

    const p = fixture.debugElement.query(By.css('p'));

    expect(p).withContext('The content should be rendered').toBeTruthy();
  });

  it('should not render the content if the menu does not have the route', () => {
    const { fixture } = setup(['merchant_onboarding']);

    const p = fixture.debugElement.query(By.css('p'));

    expect(p).withContext('The content should not be rendered').toBeFalsy();
  });
});
