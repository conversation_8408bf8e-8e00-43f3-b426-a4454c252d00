import { AsyncPipe } from '@angular/common';
import { Component } from '@angular/core';
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import {
  ActivatedRoute,
  provideRouter,
  RouterLink,
  RouterOutlet,
} from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { GoogleSSOProvider } from '@aplazo/front-social-sso/google';
import { RedirectionService } from '@aplazo/merchant/shared';
import { LocalRedirecter } from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoDashboardComponents,
  AplazoDashboardHeaderComponent,
} from '@aplazo/shared-ui/dashboard';
import {
  AplazoDropdownComponent,
  AplazoDropdownComponents,
} from '@aplazo/shared-ui/dropdown';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoSidenavLinkComponent } from '@aplazo/shared-ui/sidenav';
import { lastValueFrom, of, take } from 'rxjs';
import { RenderByMenuDirective } from '../../../../app/features/board/infra/directives/render-by-menu.directive';
import { BoardComponent } from '../../../../app/features/board/infra/pages/board.component';
import {
  HelicarrierRoute,
  ROUTES_CONFIG,
} from '../../../../app/features/core/domain/route-names';
import { SHIELD_ENVIRONMENT } from '../../../../app/features/core/infra/config/environments';
import { UserStore } from '../../../../app/features/login/application/services/user.store';
import { MenuStore } from '../../../../app/features/shared/application/services/menu.store';

@Component({
  standalone: true,
  template: `<span>test</span>`,
})
export class TestComponent {}

const setup = async (
  menu?: HelicarrierRoute[],
  shieldEnvironment?: any
): Promise<{
  component: BoardComponent;
  fixture: ComponentFixture<BoardComponent>;
  harness: RouterTestingHarness;
  redirecter: RedirectionService;
}> => {
  if (menu) {
    TestBed.overrideProvider(MenuStore, {
      useValue: {
        getMenu: () => menu,
      },
    });
  }

  if (shieldEnvironment) {
    TestBed.overrideProvider(SHIELD_ENVIRONMENT, {
      useValue: shieldEnvironment,
    });
  }

  const harness = await RouterTestingHarness.create();
  const component = await harness.navigateByUrl(
    '/' + ROUTES_CONFIG.board,
    BoardComponent
  );
  harness.detectChanges();

  const fixture = harness.fixture as ComponentFixture<any>;
  const redirecter = TestBed.inject(RedirectionService);

  return {
    component,
    fixture,
    harness,
    redirecter,
  };
};

describe('Board Component', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        TestComponent,
        BoardComponent,
        AsyncPipe,
        RouterLink,
        RouterOutlet,
        AplazoSidenavLinkComponent,
        AplazoDashboardComponents,
        AplazoButtonComponent,
        AplazoDropdownComponents,
        AplazoIconComponent,
        RenderByMenuDirective,
      ],
      providers: [
        provideRouter([
          {
            path: ROUTES_CONFIG.board,
            component: BoardComponent,
            children: [
              {
                path: '',
                component: TestComponent,
                data: { title: 'test title comp' },
              },
            ],
          },
        ]),
        AplazoIconRegistryService,
        {
          provide: UserStore,
          useValue: {
            email$: of('<EMAIL>'),
            clearUser: () => {
              void 0;
            },
          },
        },
        {
          provide: RedirectionService,
          useClass: LocalRedirecter,
        },
        {
          provide: GoogleSSOProvider,
          useValue: {
            preventAutoStart: () => {
              void 0;
            },
          },
        },
        {
          provide: SHIELD_ENVIRONMENT,
          useValue: {
            googleClientId: 'google-test',
            apiBaseUrl: 'api-test',
            bifrostUrl: 'bifrost-test',
            i18nUrl: 'i18n-test',
            landingUrl: 'landing-test',
          },
        },
        {
          provide: MenuStore,
          useValue: {
            getMenu: () => ['merchant_onboarding', 'merchant_payment'],
          },
        },
      ],
    });
  });

  it('should create', async () => {
    const { component } = await setup();
    expect(component).toBeTruthy();
  });

  it('should contain the title', async () => {
    const { fixture, component } = await setup();
    const route = TestBed.inject(ActivatedRoute);

    const header = fixture.debugElement.query(
      By.directive(AplazoDashboardHeaderComponent)
    );

    const title = await lastValueFrom(component.title$.pipe(take(1)));

    expect(title).toBe('test title comp');
    expect(route.snapshot.firstChild?.firstChild?.data?.title).toBe(
      'test title comp'
    );

    expect(
      header.query(By.css('nav h2')).nativeElement.textContent.trim()
    ).toBe('test title comp');

    const routeStream = await lastValueFrom(
      route.firstChild!.firstChild!.data.pipe(take(1))
    );

    expect(routeStream).toEqual({ title: 'test title comp' });
  });

  it('should redirect on logout', fakeAsync(async () => {
    const { fixture, component, redirecter } = await setup();
    const logoutSpy = spyOn(component, 'logout').and.callThrough();
    const internalRedirectionSpy = spyOn(
      redirecter,
      'internalNavigation'
    ).and.callThrough();
    const header = fixture.debugElement.query(
      By.directive(AplazoDashboardHeaderComponent)
    );

    const openMenu = header.query(By.directive(AplazoButtonComponent));

    (openMenu.nativeElement as HTMLButtonElement).dispatchEvent(
      new Event('click')
    );
    fixture.detectChanges();
    tick();

    const menu = header.query(By.directive(AplazoDropdownComponent));

    const logoutBtn = menu.query(By.css('dialog nav ul li button'));

    logoutBtn.nativeElement.click();

    fixture.detectChanges();

    expect(logoutSpy).toHaveBeenCalledTimes(1);
    expect(internalRedirectionSpy).toHaveBeenCalledTimes(1);
    expect(internalRedirectionSpy).toHaveBeenCalledWith(['/']);
  }));

  it('should handle click on logo', async () => {
    const { fixture, component } = await setup();
    const landingRedirectSpy = spyOn(component, 'landing').and.callThrough();
    const logo = fixture.debugElement.query(
      By.css('.aplazo-dash__nav .aplazo-dash__logo button')
    );

    (logo.nativeElement as HTMLButtonElement)?.dispatchEvent(
      new Event('click')
    );

    fixture.detectChanges();

    expect(landingRedirectSpy).toHaveBeenCalledTimes(1);
  });

  it('should call externalNavigation on logo click', async () => {
    const { fixture, redirecter } = await setup();
    const externalRedirectionSpy = spyOn(
      redirecter,
      'externalNavigation'
    ).and.callThrough();
    const logo = fixture.debugElement.query(
      By.css('.aplazo-dash__nav .aplazo-dash__logo button')
    );

    (logo.nativeElement as HTMLButtonElement)?.dispatchEvent(
      new Event('click')
    );

    fixture.detectChanges();

    expect(externalRedirectionSpy).toHaveBeenCalledTimes(1);
    expect(externalRedirectionSpy).toHaveBeenCalledWith(
      'landing-test',
      '_blank'
    );
  });

  it('should not call externalNavigation on logo click if no landing url', async () => {
    const { fixture, redirecter } = await setup(undefined, {
      landingUrl: null,
    });
    const externalRedirectionSpy = spyOn(
      redirecter,
      'externalNavigation'
    ).and.callThrough();

    const logo = fixture.debugElement.query(
      By.css('.aplazo-dash__nav .aplazo-dash__logo button')
    );

    (logo.nativeElement as HTMLButtonElement)?.dispatchEvent(
      new Event('click')
    );

    fixture.detectChanges();

    expect(externalRedirectionSpy).toHaveBeenCalledTimes(0);
  });

  it('should have a payment link', async () => {
    const { fixture } = await setup();

    const links = fixture.debugElement.queryAll(
      By.directive(AplazoSidenavLinkComponent)
    );

    console.log();

    const paymentLink = links
      .map(link => link.query(By.css('span.aplazo-sidenav-link__label')))
      .find(link => {
        return (
          link.nativeElement.textContent.trim().toLowerCase() ===
          'historial de pagos'
        );
      });

    expect(paymentLink)
      .withContext('Has a sidenav link with the text "Pagos"')
      .toBeTruthy();
  });

  it('should not have a payment link', async () => {
    const { fixture } = await setup(['merchant_onboarding']);

    const links = fixture.debugElement.queryAll(
      By.directive(AplazoSidenavLinkComponent)
    );

    const paymentLink = links.find(link => {
      return (
        link.nativeElement.textContent.trim().toLowerCase() ===
        'Historial de pagos'
      );
    });

    expect(paymentLink)
      .withContext('Has a sidenav link with the text "Pagos"')
      .toBeFalsy();
  });
});
