import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { provideRouter, Route, UrlTree } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { of } from 'rxjs';
import { ROUTES_CONFIG } from '../../../../../app/features/core/domain/route-names';
import {
  loggedInUsersOnly,
  loggedOffUsersOnly,
} from '../../../../../app/features/core/infra/guards/auth.guard';
import { UserStore } from '../../../../../app/features/login/application/services/user.store';

@Component({
  standalone: true,
  template: `<span>test</span>`,
})
export class TestComponent {}

const setupLoggedIn = (mockUserStore: unknown, routes: Route[]) => {
  TestBed.configureTestingModule({
    imports: [TestComponent],
    providers: [
      {
        provide: UserStore,
        useValue: mockUserStore,
      },
      provideRouter(routes),
    ],
  });

  return TestBed.runInInjectionContext(() =>
    loggedInUsersOnly({} as any, {} as any)
  );
};

describe('Logged In Users Only', () => {
  let routes: Route[];

  beforeEach(() => {
    routes = [
      {
        path: 'app',
        canActivate: [loggedInUsersOnly],
        component: TestComponent,
      },
      {
        path: '',
        component: TestComponent,
      },
    ];
  });

  it('should allow navigation to logged in users', async () => {
    const mockUserStore = {
      isLoggedIn$: of(true),
    };

    const guard = await setupLoggedIn(mockUserStore, routes);

    await RouterTestingHarness.create('app');

    expect(guard).toBeTrue();
  });

  it('should not allow navigation to logged out users', async () => {
    const mockUserStore = {
      isLoggedIn$: of(false),
      clearUser: () => {},
    };

    const guard = await setupLoggedIn(mockUserStore, routes);

    await RouterTestingHarness.create('app');

    expect(guard).toBeInstanceOf(UrlTree);
    expect((guard as UrlTree).toString()).toBe('/');
  });
});

const setupLoggedOff = (mockUserStore: unknown, routes: Route[]) => {
  TestBed.configureTestingModule({
    imports: [TestComponent],
    providers: [
      {
        provide: UserStore,
        useValue: mockUserStore,
      },
      provideRouter(routes),
    ],
  });

  return TestBed.runInInjectionContext(() =>
    loggedOffUsersOnly({} as any, {} as any)
  );
};

describe('Logged Off Users Only', () => {
  let routes: Route[];

  beforeEach(() => {
    routes = [
      {
        path: ROUTES_CONFIG.board,
        canActivate: [loggedOffUsersOnly],
        component: TestComponent,
      },
      {
        path: '',
        component: TestComponent,
      },
    ];
  });

  it('should allow navigation to logged out users', async () => {
    const mockUserStore = {
      isLoggedIn$: of(false),
    };

    const guard = await setupLoggedOff(mockUserStore, routes);

    await RouterTestingHarness.create('app');

    expect(guard).toBeTrue();
  });

  it('should not allow navigation to logged in users', async () => {
    const mockUserStore = {
      isLoggedIn$: of(true),
    };

    const guard = await setupLoggedOff(mockUserStore, routes);

    await RouterTestingHarness.create('/');

    expect(guard).toBeInstanceOf(UrlTree);
    expect((guard as UrlTree).toString()).toBe('/' + ROUTES_CONFIG.board);
  });
});
