import { TestBed } from '@angular/core/testing';
import {
  provideEnvironmentVariables,
  SHIELD_ENVIRONMENT,
  ShieldEnvironments,
} from '../../../../../app/features/core/infra/config/environments';

describe('Environment Variables', () => {
  let environment: ShieldEnvironments;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideEnvironmentVariables()],
    });

    environment = TestBed.inject(SHIELD_ENVIRONMENT);
  });

  it('should provide environment variables', () => {
    expect(environment).toBeTruthy();
    expect(environment.apiBaseUrl).toBeTruthy();
    expect(environment.bifrostUrl).toBeTruthy();
    expect(environment.googleClientId).toBeTruthy();
    expect(environment.i18nUrl).toBeTruthy();
    expect(environment.landingUrl).toBeTruthy();
  });
});
