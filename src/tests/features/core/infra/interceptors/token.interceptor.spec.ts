import {
  HttpClient,
  provideHttpClient,
  withInterceptors,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { tokenInterceptor } from '../../../../../app/features/core/infra/interceptors/token.interceptor';
import { UserStore } from '../../../../../app/features/login/application/services/user.store';

const setupLoggedIn = (testBed: TestBed) => {
  const httpTestingController = testBed.inject(HttpTestingController);
  const httpClient = testBed.inject(HttpClient);

  return {
    httpClient,
    httpTestingController,
  };
};

describe('Token Interceptor', () => {
  const mockStore = {
    isLoggedIn$: of(true),
    tokenSession$: of('token'),
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(withInterceptors([tokenInterceptor])),
        provideHttpClientTesting(),
        {
          provide: UserStore,
          useValue: mockStore,
        },
      ],
    });
  });

  it('should add token to headers', () => {
    const { httpClient, httpTestingController } = setupLoggedIn(TestBed);

    httpClient.get('/test').subscribe();

    const req = httpTestingController.expectOne('/test');
    expect(req.request.headers.get('Authorization')).toBe('Bearer token');
  });

  it('should not add token to headers if user is not logged in', () => {
    TestBed.overrideProvider(UserStore, {
      useValue: {
        isLoggedIn$: of(false),
        tokenSession$: of('token'),
      },
    });
    const { httpClient, httpTestingController } = setupLoggedIn(TestBed);

    httpClient.get('/test').subscribe();

    const req = httpTestingController.expectOne('/test');
    expect(req.request.headers.get('Authorization')).toBeNull();
  });

  it('should not add token to headers if request url is marked as skiped', () => {
    const { httpClient, httpTestingController } = setupLoggedIn(TestBed);

    httpClient.get('/test.svg').subscribe();

    const req = httpTestingController.expectOne('/test.svg');
    expect(req.request.headers.get('Authorization')).toBeNull();
  });
});
