import { importProvidersFrom } from '@angular/core';
import {
  ComponentFixture,
  fakeAsync,
  flush,
  TestBed,
  tick,
} from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  GlobalConfig,
  ToastNoAnimationModule,
  ToastPackage,
  ToastrService,
} from 'ngx-toastr';
import { of, Subject } from 'rxjs';
import { CustomToastrComponent } from '../../../../../app/features/core/infra/components/custom-toastr.component';

export const DefaultNoComponentGlobalConfig: GlobalConfig = {
  maxOpened: 0,
  autoDismiss: false,
  newestOnTop: true,
  preventDuplicates: false,
  countDuplicates: false,
  resetTimeoutOnDuplicate: false,
  includeTitleDuplicates: false,

  iconClasses: {
    error: 'toast-error',
    info: 'toast-info',
    success: 'toast-success',
    warning: 'toast-warning',
  },

  // Individual
  closeButton: false,
  disableTimeOut: false,
  timeOut: 5000,
  extendedTimeOut: 1000,
  enableHtml: false,
  progressBar: false,
  toastClass: 'ngx-toastr',
  positionClass: 'toast-top-right',
  titleClass: 'toast-title',
  messageClass: 'toast-message',
  easing: 'ease-in',
  easeTime: 300,
  tapToDismiss: true,
  onActivateTick: false,
  progressAnimation: 'decreasing',
};

describe('CustomToastrComponent', () => {
  let fixture: ComponentFixture<CustomToastrComponent>;
  let component: CustomToastrComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [AplazoButtonComponent, NoopAnimationsModule],
      providers: [
        importProvidersFrom(
          ToastNoAnimationModule.forRoot({
            closeButton: true,
            timeOut: 8000,
          })
        ),
        ToastrService,
        {
          provide: ToastPackage,
          useValue: {
            toastId: 123,
            config: DefaultNoComponentGlobalConfig,
            message: 'test',
            title: 'test',
            toastRef: (() => {
              const activationSubject = new Subject<void>();
              const manualClosedSubject = new Subject<void>();
              const afterClosedSubject = new Subject<void>();
              const timeoutResetSubject = new Subject<void>();
              const onTapSubject = new Subject<void>();
              const onActionSubject = new Subject<any>();

              return {
                toastId: 123,
                afterActivate: () => activationSubject.asObservable(),
                activate: () => {
                  activationSubject.next();
                  activationSubject.complete();
                },
                manualClosed: () => manualClosedSubject.asObservable(),
                manualClose: () => {
                  manualClosedSubject.next();
                  manualClosedSubject.complete();
                },
                afterClosed: () => afterClosedSubject.asObservable(),
                close: () => {
                  afterClosedSubject.next();
                  afterClosedSubject.complete();
                },
                timeoutReset: () => timeoutResetSubject.asObservable(),
                onTap: () => onTapSubject.asObservable(),
                triggerTap: () => onTapSubject.next(),
                onAction: () => onActionSubject.asObservable(),
                triggerAction: (action?: any) => onActionSubject.next(action),
                countDuplicate: () => of(void 0),
              };
            })(),
            triggerTap: () => {
              (component.toastPackage.toastRef as any).triggerTap();
            },
            onTap: () => (component.toastPackage.toastRef as any).onTap(),

            triggerAction: (action?: any) => {
              (component.toastPackage.toastRef as any).triggerAction(action);
            },
            onAction: (action?: any) =>
              (component.toastPackage.toastRef as any).onAction(),
          },
        },
      ],
    });

    fixture = TestBed.createComponent(CustomToastrComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', fakeAsync(() => {
    const activateSpy = spyOn(component, 'activateToast').and.callThrough();

    fixture.detectChanges();

    component.toastPackage.toastRef.activate();

    tick();

    expect(activateSpy).toHaveBeenCalled();

    fixture.detectChanges();

    expect(component).toBeDefined();

    flush();
  }));

  describe('Toastr methods', () => {
    beforeEach(fakeAsync(() => {
      fixture.detectChanges();
      component.toastPackage.config.disableTimeOut = true;
      component.toastPackage.config.toastComponent = true as any;
      component.toastPackage.toastRef.activate();
      tick();
      fixture.detectChanges();
    }));

    it('should call remove when close is called', fakeAsync(() => {
      const spy = spyOn(
        component.toastPackage,
        'triggerAction'
      ).and.callThrough();

      component.close();
      flush();

      expect(spy).toHaveBeenCalledWith({ confirmation: false });
    }));

    it('should call triggerAction and close when action is called', fakeAsync(() => {
      const triggerActionSpy = spyOn(
        component.toastPackage,
        'triggerAction'
      ).and.callThrough();

      component.action({} as any);
      fixture.detectChanges();
      flush();

      expect(triggerActionSpy).toHaveBeenCalledWith({ confirmation: true });
    }));
  });

  describe('Template rendering', () => {
    it('should display title and message when provided', fakeAsync(() => {
      component.title = 'Test Title';
      component.message = 'Test Message';
      component.toastPackage.toastRef.activate();
      tick();
      fixture.detectChanges();

      const titleEl = fixture.debugElement.query(By.css('.text-dark-primary'));
      const messageEl = fixture.debugElement.query(
        By.css('.text-dark-secondary')
      );
      expect(titleEl.nativeElement.textContent.trim()).toBe('Test Title');
      expect(messageEl.nativeElement.textContent.trim()).toBe('Test Message');
      flush();
    }));

    it('should display only message when title is not provided', fakeAsync(() => {
      component.title = undefined;
      component.message = 'Only Message';
      component.toastPackage.toastRef.activate();
      tick();
      fixture.detectChanges();

      const titleEl = fixture.debugElement.query(By.css('.text-dark-primary'));
      const messageEl = fixture.debugElement.query(
        By.css('.text-dark-secondary')
      );
      expect(titleEl).toBeNull();
      expect(messageEl.nativeElement.textContent.trim()).toBe('Only Message');
      flush();
    }));

    it('should display only title when message is not provided', fakeAsync(() => {
      component.title = 'Only Title';
      component.message = undefined;
      component.toastPackage.toastRef.activate();
      tick();
      fixture.detectChanges();

      const titleEl = fixture.debugElement.query(By.css('.text-dark-primary'));
      const messageEl = fixture.debugElement.query(
        By.css('.text-dark-secondary')
      );
      expect(titleEl.nativeElement.textContent.trim()).toBe('Only Title');
      expect(messageEl).toBeNull();
      flush();
    }));

    it('should initially be hidden if state is inactive', fakeAsync(() => {
      fixture.detectChanges();
      const articleEl = fixture.debugElement.query(By.css('article'));
      expect(articleEl.nativeElement.style.display).toBe('none');
      flush();
    }));
  });

  describe('Button interactions', () => {
    beforeEach(fakeAsync(() => {
      component.toastPackage.config.disableTimeOut = true;
      component.toastPackage.toastRef.activate();
      tick();
      fixture.detectChanges();
    }));

    it('should call close() when "Más tarde" button is clicked', fakeAsync(() => {
      const closeSpy = spyOn(component, 'close').and.callThrough();
      const buttons = fixture.debugElement.queryAll(
        By.css('button[aplzButton]')
      );
      const masTardeButton = buttons.find(btn =>
        btn.nativeElement.textContent.includes('Más tarde')
      );

      expect(masTardeButton)
        .withContext('"Más tarde" button not found')
        .toBeTruthy();
      if (masTardeButton) {
        masTardeButton.nativeElement.click();
        fixture.detectChanges();
      }
      flush();

      expect(closeSpy).toHaveBeenCalled();
    }));

    it('should call action() when "Actualizar" button is clicked', fakeAsync(() => {
      const actionSpy = spyOn(component, 'action').and.callThrough();
      const buttons = fixture.debugElement.queryAll(
        By.css('button[aplzButton]')
      );
      const actualizarButton = buttons.find(btn =>
        btn.nativeElement.textContent.includes('Actualizar')
      );

      expect(actualizarButton)
        .withContext('"Actualizar" button not found')
        .toBeTruthy();
      if (actualizarButton) {
        actualizarButton.nativeElement.click();
        fixture.detectChanges();
      }
      flush();

      expect(actionSpy).toHaveBeenCalled();
    }));
  });
});
