{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"helicarrier": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "i18n": {"sourceLocale": "es-MX"}, "architect": {"build": {"builder": "@ngx-env/builder:application", "options": {"outputPath": "dist/helicarrier", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/styles.css", "node_modules/@aplazo/shared-ui/config/air-datepicker.css", "node_modules/@aplazo/shared-ui/themes/aplazo-light.css"], "scripts": [], "allowedCommonJsDependencies": ["*"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all", "serviceWorker": "ngsw-config.json"}, "stage": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all", "serviceWorker": "ngsw-config.json"}, "development": {"preserveSymlinks": true, "optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@ngx-env/builder:dev-server", "configurations": {"production": {"buildTarget": "helicarrier:build:production"}, "stage": {"buildTarget": "helicarrier:build:stage"}, "development": {"buildTarget": "helicarrier:build:development"}}, "defaultConfiguration": "development", "options": {}}, "extract-i18n": {"builder": "@ngx-env/builder:extract-i18n", "options": {"buildTarget": "helicarrier:build"}}, "test": {"builder": "@ngx-env/builder:karma", "options": {"main": "src/test.ts", "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/styles.css", "node_modules/@aplazo/shared-ui/config/air-datepicker.css", "node_modules/@aplazo/shared-ui/themes/aplazo-light.css"], "scripts": [], "karmaConfig": "karma.conf.js", "browsers": "Chrome"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"cache": {"enabled": false}, "analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}