/**
 * @type {import('semantic-release').GlobalConfig}
 */
export default {
  branches: [{ name: 'master', channel: 'latest' }],
  tagFormat: 'v${version}',
  repositoryUrl:
    'https://github.com/aplazo/angular.control-tower-dashboard.git',
  preset: 'conventionalcommits',
  presetConfig: {
    types: [
      { type: 'feat', section: 'Features' },
      { type: 'fix', section: 'Bug Fixes' },
      { type: 'chore', section: 'Chores' },
      { type: 'docs', hidden: true },
      { type: 'style', hidden: true },
      { type: 'refactor', section: 'Refactoring' },
      { type: 'perf', hidden: true },
      { type: 'test', hidden: true },
    ],
  },
  plugins: [
    '@semantic-release/commit-analyzer',
    '@semantic-release/release-notes-generator',
    [
      '@semantic-release/changelog',
      {
        changelogFile: `./CHANGELOG.md`,
      },
    ],
    [
      '@semantic-release/npm',
      {
        npmPublish: false,
      },
    ],
    [
      '@semantic-release/git',
      {
        assets: [`package.json`, `CHANGELOG.md`],
        message:
          'chore(release): -v${nextRelease.version}  [skip ci]\n\n${nextRelease.notes}',
      },
    ],
  ],
};
