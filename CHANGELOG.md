## <small>1.29.1 (2025-07-04)</small>

* fix: mexp-635 ServiceWorkerUpdaterService to clear toastr notifications ([7c02122](https://github.com/aplazo/angular.control-tower-dashboard/commit/7c02122))

## 1.29.0 (2025-07-02)

* Merge pull request #227 from aplazo/mexp-603 ([2f6f513](https://github.com/aplazo/angular.control-tower-dashboard/commit/2f6f513)), closes [#227](https://github.com/aplazo/angular.control-tower-dashboard/issues/227)
* feat: mexp-603 enhance payments component with reactive state management ([ae58d73](https://github.com/aplazo/angular.control-tower-dashboard/commit/ae58d73))

## 1.28.0 (2025-07-02)

* Merge pull request #226 from aplazo/cards-improvements ([21b5abc](https://github.com/aplazo/angular.control-tower-dashboard/commit/21b5abc)), closes [#226](https://github.com/aplazo/angular.control-tower-dashboard/issues/226)
* docs: update version and changelog ([f00de75](https://github.com/aplazo/angular.control-tower-dashboard/commit/f00de75))
* refactor: load locations from endpoint instead of an id field ([70404c0](https://github.com/aplazo/angular.control-tower-dashboard/commit/70404c0))
* refactor: remove embossWith field ([8907ad1](https://github.com/aplazo/angular.control-tower-dashboard/commit/8907ad1))
* refactor: remove unusted field and add states catalog ([7889df3](https://github.com/aplazo/angular.control-tower-dashboard/commit/7889df3))
* refactor: renamed field, remove unused fields and add states catalog ([731c57f](https://github.com/aplazo/angular.control-tower-dashboard/commit/731c57f))
* feat: add states catalog ([213cf37](https://github.com/aplazo/angular.control-tower-dashboard/commit/213cf37))

## 1.28.0 (2025-07-01)

* feat: enhance cards management with improved user experience and Mexican states support
* feat: remove embossWith field from bulk order form to simplify card ordering process
* feat: add Mexican states dropdown to bulk order and location forms for better data consistency
* feat: add locations dropdown to bulk order form with dynamic loading from API
* feat: simplify location form by removing parent location fields (now set to default values)
* feat: update locations table to remove Parent Location ID column and improve display

## 1.27.0 (2025-07-01)

* feat!: add unit tests for provideServiceWorkerUpdater function ([ca17d2e](https://github.com/aplazo/angular.control-tower-dashboard/commit/ca17d2e))
* feat!: update testing imports and clean up service worker updater tests ([153814d](https://github.com/aplazo/angular.control-tower-dashboard/commit/153814d))
* feat!: upgrade Node.js version to 22.13.1 in Dockerfiles and package.json ([1a156ad](https://github.com/aplazo/angular.control-tower-dashboard/commit/1a156ad))
* feat!: upgrade to angular v20 and update dependencies ([b2e0c50](https://github.com/aplazo/angular.control-tower-dashboard/commit/b2e0c50))
* Merge pull request #225 from aplazo/mexp-603 ([82bc1a7](https://github.com/aplazo/angular.control-tower-dashboard/commit/82bc1a7)), closes [#225](https://github.com/aplazo/angular.control-tower-dashboard/issues/225)
* test: update component tests to use RouterOutlet and improve type safety ([aa61306](https://github.com/aplazo/angular.control-tower-dashboard/commit/aa61306))
* feat: update to angular v18 ([0abe03a](https://github.com/aplazo/angular.control-tower-dashboard/commit/0abe03a))
* feat: update to angular v19 ([c3526c0](https://github.com/aplazo/angular.control-tower-dashboard/commit/c3526c0))
* feat: update to angular v20 ([37aa788](https://github.com/aplazo/angular.control-tower-dashboard/commit/37aa788))
* chore: update dependencies ([ecc3d1c](https://github.com/aplazo/angular.control-tower-dashboard/commit/ecc3d1c))

## <small>1.26.1 (2025-06-25)</small>

* Merge pull request #224 from aplazo/v-1-26-0 ([469f109](https://github.com/aplazo/angular.control-tower-dashboard/commit/469f109)), closes [#224](https://github.com/aplazo/angular.control-tower-dashboard/issues/224)
* docs: update changelog ([cee657f](https://github.com/aplazo/angular.control-tower-dashboard/commit/cee657f))
* fix: update version ([1d56c27](https://github.com/aplazo/angular.control-tower-dashboard/commit/1d56c27))

## 1.26.0 (2025-01-15)

* feat: cards admin functionality with locations and bulk orders management ([#223](https://github.com/aplazo/angular.control-tower-dashboard/pull/223))

## 1.25.0 (2025-06-16)

* Merge pull request #222 from aplazo/MEXP-621 ([958d785](https://github.com/aplazo/angular.control-tower-dashboard/commit/958d785)), closes [#222](https://github.com/aplazo/angular.control-tower-dashboard/issues/222)
* feat: mexp-621 add nue role: ROLE_PANEL_MANAGER ([fdd96b0](https://github.com/aplazo/angular.control-tower-dashboard/commit/fdd96b0))

## 1.24.0 (2025-06-13)

* Merge pull request #221 from aplazo/MEXP-619 ([29a1371](https://github.com/aplazo/angular.control-tower-dashboard/commit/29a1371)), closes [#221](https://github.com/aplazo/angular.control-tower-dashboard/issues/221)
* feat: mexp-619 hide filters section until backend issues are being fixed ([f48de65](https://github.com/aplazo/angular.control-tower-dashboard/commit/f48de65))
* feat: mexp-619 remove type for isBackendReady property ([455ea9a](https://github.com/aplazo/angular.control-tower-dashboard/commit/455ea9a))

## <small>1.23.1 (2025-06-12)</small>

* fix: mexp-602 update dependencies ([87fb3d8](https://github.com/aplazo/angular.control-tower-dashboard/commit/87fb3d8))

## 1.23.0 (2025-06-11)

* Merge pull request #219 from aplazo/mexp-602 ([427d580](https://github.com/aplazo/angular.control-tower-dashboard/commit/427d580)), closes [#219](https://github.com/aplazo/angular.control-tower-dashboard/issues/219)
* feat: mexp-602 update dependencies and replace loader component in app ([232fd93](https://github.com/aplazo/angular.control-tower-dashboard/commit/232fd93))

## <small>1.22.2 (2025-06-05)</small>

* Merge pull request #218 from aplazo/release-1.23 ([7146b25](https://github.com/aplazo/angular.control-tower-dashboard/commit/7146b25)), closes [#218](https://github.com/aplazo/angular.control-tower-dashboard/issues/218)
* fix: update login component to use BaseSSOProvider and adjust tests accordingly ([e1bc56b](https://github.com/aplazo/angular.control-tower-dashboard/commit/e1bc56b))
* chore: release with updated dependencies for service worker ([4fcfcd1](https://github.com/aplazo/angular.control-tower-dashboard/commit/4fcfcd1))

## <small>1.22.1 (2025-05-26)</small>

* Merge pull request #215 from aplazo/mexp-538 ([e170bc3](https://github.com/aplazo/angular.control-tower-dashboard/commit/e170bc3)), closes [#215](https://github.com/aplazo/angular.control-tower-dashboard/issues/215)
* fix: mexp-528 update listing campaigns for optimize data stream handling ([72c476b](https://github.com/aplazo/angular.control-tower-dashboard/commit/72c476b))

## 1.22.0 (2025-05-22)

* feat: changing statsig api key ([35e7e8d](https://github.com/aplazo/angular.control-tower-dashboard/commit/35e7e8d))

## <small>1.21.1 (2025-05-22)</small>

* Merge pull request #214 from aplazo/mexp-537 ([04d79dc](https://github.com/aplazo/angular.control-tower-dashboard/commit/04d79dc)), closes [#214](https://github.com/aplazo/angular.control-tower-dashboard/issues/214)
* fix: mexp-537 update prize distribution component ([5fe6657](https://github.com/aplazo/angular.control-tower-dashboard/commit/5fe6657))

## 1.21.0 (2025-05-22)

* feat: mexp-537 prize distribution refactor ([127a2f7](https://github.com/aplazo/angular.control-tower-dashboard/commit/127a2f7))
* Merge pull request #213 from aplazo/mexp-536 ([af92110](https://github.com/aplazo/angular.control-tower-dashboard/commit/af92110)), closes [#213](https://github.com/aplazo/angular.control-tower-dashboard/issues/213)
* refactor: mexp-536 enhance prize distribution component ([7ce6c57](https://github.com/aplazo/angular.control-tower-dashboard/commit/7ce6c57))
* refactor: mexp-536 streamline campaign use cases and update component ([89d29bb](https://github.com/aplazo/angular.control-tower-dashboard/commit/89d29bb))
* refactor: mexp-537 enhance campaign form and prize distribution components ([825b54e](https://github.com/aplazo/angular.control-tower-dashboard/commit/825b54e))
* refactor: mexp-537 improve campaign form and prize distribution components ([00f9e97](https://github.com/aplazo/angular.control-tower-dashboard/commit/00f9e97))
* refactor: mexp-537 update login component to handle undefined values for email and userID ([11f85f8](https://github.com/aplazo/angular.control-tower-dashboard/commit/11f85f8))
* refactor: mexp-537 update prize distribution component to improve validation and form handling ([00bc19a](https://github.com/aplazo/angular.control-tower-dashboard/commit/00bc19a))
* refactor: mexp-537 update prize distribution component to use readonly methods for internal logic ([a9eed00](https://github.com/aplazo/angular.control-tower-dashboard/commit/a9eed00))
* refactor: mexp-537 update prize distribution component to use rxjs instead signals ([57144b1](https://github.com/aplazo/angular.control-tower-dashboard/commit/57144b1))
* refactor: mexp-537 update prize distribution to utilize reactive forms and enhance validation ([96a8c8b](https://github.com/aplazo/angular.control-tower-dashboard/commit/96a8c8b))
* chore: update angular.json budget limits to 1mb warning and 2mb error ([ed5f05f](https://github.com/aplazo/angular.control-tower-dashboard/commit/ed5f05f))
* chore: update dependencies to version 4.41.0 for various rollup packages and @types ([f9931ae](https://github.com/aplazo/angular.control-tower-dashboard/commit/f9931ae))
* chore: update FRONT_GH_TOKEN in production history ([bbc050f](https://github.com/aplazo/angular.control-tower-dashboard/commit/bbc050f))
* chore: update FRONT_GH_TOKEN in staging history ([5ae46e5](https://github.com/aplazo/angular.control-tower-dashboard/commit/5ae46e5))

## 1.20.0 (2025-05-20)

* Merge pull request #212 from aplazo/mexp-539 ([5682685](https://github.com/aplazo/angular.control-tower-dashboard/commit/5682685)), closes [#212](https://github.com/aplazo/angular.control-tower-dashboard/issues/212)
* feat: mexp-539 implement GetLastCampaigns and GetWinnersByCampaign use cases with error handling ([c98db65](https://github.com/aplazo/angular.control-tower-dashboard/commit/c98db65))
* feat: mexp-539 integrate Statsig SDK for feature flag management and session replay ([0170ef6](https://github.com/aplazo/angular.control-tower-dashboard/commit/0170ef6))
* feat: update dependencies add statsig angular sdk ([4d11c42](https://github.com/aplazo/angular.control-tower-dashboard/commit/4d11c42))
* refactor: mexp-539 clean up payments component template and remove unused imports ([6e9183f](https://github.com/aplazo/angular.control-tower-dashboard/commit/6e9183f))
* refactor: mexp-539 replace FeatureFlagsService with StatsigService in LoginComponent ([cd20b0b](https://github.com/aplazo/angular.control-tower-dashboard/commit/cd20b0b))
* refactor: mexp-539 replace FeatureFlagsService with StatsigService in maintenance guard ([addabe8](https://github.com/aplazo/angular.control-tower-dashboard/commit/addabe8))
* refactor: mexp-539 simplify tests in ServiceWorkerUpdaterService and LoginComponent ([0e27999](https://github.com/aplazo/angular.control-tower-dashboard/commit/0e27999))
* refactor: mexp-539 update CampaignFormComponent to use StatsigService for feature flag management ([700089e](https://github.com/aplazo/angular.control-tower-dashboard/commit/700089e))
* fix: mexp-539 update ngsw-config.json to refine asset caching and navigation URL patterns ([33231c5](https://github.com/aplazo/angular.control-tower-dashboard/commit/33231c5))
* fix: mexp-539 update service worker updater to reload document directly on update activation ([394e746](https://github.com/aplazo/angular.control-tower-dashboard/commit/394e746))

## 1.19.0 (2025-04-21)

* feat: release ([d1ba088](https://github.com/aplazo/angular.control-tower-dashboard/commit/d1ba088))

## 1.18.0 (2025-04-21)

* feat: release ([01ffa08](https://github.com/aplazo/angular.control-tower-dashboard/commit/01ffa08))
* chore(release): release to prod ([06ae812](https://github.com/aplazo/angular.control-tower-dashboard/commit/06ae812))

## 1.17.0 (2025-04-09)

* Merge pull request #210 from aplazo/mexp-482 ([6481cfa](https://github.com/aplazo/angular.control-tower-dashboard/commit/6481cfa)), closes [#210](https://github.com/aplazo/angular.control-tower-dashboard/issues/210)
* feat: mexp-482 add providers to custom error handler and connection status ([7c8a194](https://github.com/aplazo/angular.control-tower-dashboard/commit/7c8a194))
* feat: mexp-482 add service worker and update asset references ([4985339](https://github.com/aplazo/angular.control-tower-dashboard/commit/4985339))
* feat: mexp-482 implement service worker updater with custom toastr notifications ([9c55fe3](https://github.com/aplazo/angular.control-tower-dashboard/commit/9c55fe3))
* feat: mexp-482 refactor service worker updater to use injected DOCUMENT and add unit tests ([365113d](https://github.com/aplazo/angular.control-tower-dashboard/commit/365113d))
* chore: mexp-482 update dependencies ([acf446b](https://github.com/aplazo/angular.control-tower-dashboard/commit/acf446b))

## 1.16.0 (2025-04-01)

* feat(campaigns): mexp-418 feature flag added to show/hide filters ([c4b4e36](https://github.com/aplazo/angular.control-tower-dashboard/commit/c4b4e36))
* feat(campaigns): mexp-418 restore components related to another feature ([bcd1a7b](https://github.com/aplazo/angular.control-tower-dashboard/commit/bcd1a7b))
* feat(campaigns): mexp-428 adjust code after pr comments ([4d96810](https://github.com/aplazo/angular.control-tower-dashboard/commit/4d96810))
* feat(campaigns): mexp-428 frequency filter completed, add an interceptor ([35d83dc](https://github.com/aplazo/angular.control-tower-dashboard/commit/35d83dc))
* feat(campaigns): mexp-428 frequency filter completed, add an interceptor ([52c9dc5](https://github.com/aplazo/angular.control-tower-dashboard/commit/52c9dc5))
* feat(campaigns): mexp-428 invoiceStatus filter completed ([85b66ef](https://github.com/aplazo/angular.control-tower-dashboard/commit/85b66ef))
* feat(campaigns): mexp-428 marchantId, marchantName filters added, adjust current tests ([fddac03](https://github.com/aplazo/angular.control-tower-dashboard/commit/fddac03))
* feat(campaigns): mexp-428 paymentId filter completed ([bb93621](https://github.com/aplazo/angular.control-tower-dashboard/commit/bb93621))
* feat(campaigns): mexp-428 paymentStatus filter completed ([533be2c](https://github.com/aplazo/angular.control-tower-dashboard/commit/533be2c))
* chore: mexp-485 fix payment criteria ([26b2cfa](https://github.com/aplazo/angular.control-tower-dashboard/commit/26b2cfa))
* Merge branch 'master' into MEXP-428 ([dc4e15f](https://github.com/aplazo/angular.control-tower-dashboard/commit/dc4e15f))
* Merge branch 'MEXP-428' of github.com:aplazo/angular.control-tower-dashboard into MEXP-428 ([6d80024](https://github.com/aplazo/angular.control-tower-dashboard/commit/6d80024))

## 1.15.0 (2025-03-25)

* Merge pull request #206 from aplazo/MAC-277 ([1dab6ec](https://github.com/aplazo/angular.control-tower-dashboard/commit/1dab6ec)), closes [#206](https://github.com/aplazo/angular.control-tower-dashboard/issues/206)
* feat: mac-277 adjust value in Forma de Pago dropdown ([4838da0](https://github.com/aplazo/angular.control-tower-dashboard/commit/4838da0))

## 1.14.0 (2025-03-24)

* Merge branch 'master' into MAC-277 ([57b8e57](https://github.com/aplazo/angular.control-tower-dashboard/commit/57b8e57))
* Merge pull request #205 from aplazo/MAC-277 ([7f26ee5](https://github.com/aplazo/angular.control-tower-dashboard/commit/7f26ee5)), closes [#205](https://github.com/aplazo/angular.control-tower-dashboard/issues/205)
* feat: mac-277 adjust value in Forma de Pago dropdown ([3a29282](https://github.com/aplazo/angular.control-tower-dashboard/commit/3a29282))
* feat: mac-277 current tests adjusted ([450b482](https://github.com/aplazo/angular.control-tower-dashboard/commit/450b482))

## <small>1.13.2 (2025-03-24)</small>

* Merge pull request #204 from aplazo/mexp-487 ([a8c7f3e](https://github.com/aplazo/angular.control-tower-dashboard/commit/a8c7f3e)), closes [#204](https://github.com/aplazo/angular.control-tower-dashboard/issues/204)
* fix(campaigns): mexp-487 update range logic and debounce for prize distribution ([b5c7f6b](https://github.com/aplazo/angular.control-tower-dashboard/commit/b5c7f6b))

## <small>1.13.1 (2025-03-20)</small>

* Merge pull request #202 from aplazo/release ([c5d7af2](https://github.com/aplazo/angular.control-tower-dashboard/commit/c5d7af2)), closes [#202](https://github.com/aplazo/angular.control-tower-dashboard/issues/202)
* Merge pull request #203 from aplazo/release ([3c3aeae](https://github.com/aplazo/angular.control-tower-dashboard/commit/3c3aeae)), closes [#203](https://github.com/aplazo/angular.control-tower-dashboard/issues/203)
* fix: mexp-485 unused comments removed ([9776b16](https://github.com/aplazo/angular.control-tower-dashboard/commit/9776b16))
* chore: mexp-485 adjust currento test without feature flag ([dacc2aa](https://github.com/aplazo/angular.control-tower-dashboard/commit/dacc2aa))
* chore: mexp-485 remove feature flag from winners table ([7c243f6](https://github.com/aplazo/angular.control-tower-dashboard/commit/7c243f6))

## 1.13.0 (2025-03-19)

* Merge pull request #201 from aplazo/mexp-384 ([93d21f4](https://github.com/aplazo/angular.control-tower-dashboard/commit/93d21f4)), closes [#201](https://github.com/aplazo/angular.control-tower-dashboard/issues/201)
* feat(campaigns): mexp-384 enhance rewards table with tooltip for missing terms and conditions ([84d2596](https://github.com/aplazo/angular.control-tower-dashboard/commit/84d2596))

## <small>1.12.1 (2025-03-19)</small>

* fix(campaigns): mexp-239 add startWith to valueChanges subscription for initial value ([b3137a6](https://github.com/aplazo/angular.control-tower-dashboard/commit/b3137a6))
* fix(campaigns): mexp-239 distinctUntilChanged to prevent unnecessary emissions in listing campaigns ([3ebbfdc](https://github.com/aplazo/angular.control-tower-dashboard/commit/3ebbfdc))
* fix(campaigns): mexp-239 remove debug log from prize distribution valueChanges subscription ([1e896b2](https://github.com/aplazo/angular.control-tower-dashboard/commit/1e896b2))
* fix(campaigns): mexp-239 remove debug logs from prize distribution component ([281e0e0](https://github.com/aplazo/angular.control-tower-dashboard/commit/281e0e0))
* fix(campaigns): mexp-239 update termsConditions fallback for falsy values ([0d60a42](https://github.com/aplazo/angular.control-tower-dashboard/commit/0d60a42))
* chore(deps): update package-lock.json to version 1.12.0 with dependency upgrades ([164adc6](https://github.com/aplazo/angular.control-tower-dashboard/commit/164adc6))

## 1.12.0 (2025-03-19)

* Merge pull request #200 from aplazo/poc-linked-list ([395f587](https://github.com/aplazo/angular.control-tower-dashboard/commit/395f587)), closes [#200](https://github.com/aplazo/angular.control-tower-dashboard/issues/200)
* fix(campaigns): mexp-239 correct label in prize distribution form ([4da2b4f](https://github.com/aplazo/angular.control-tower-dashboard/commit/4da2b4f))
* fix(campaigns): mexp-239 update prize distribution logic and improve null handling ([7eb95ff](https://github.com/aplazo/angular.control-tower-dashboard/commit/7eb95ff))
* feat(campaigns): mexp-239 implement prize distribution component and range management ([2ed5c5e](https://github.com/aplazo/angular.control-tower-dashboard/commit/2ed5c5e))
* feat(campaigns): mexp-239 update prize distribution logic and remove obsolete components ([4d97fb7](https://github.com/aplazo/angular.control-tower-dashboard/commit/4d97fb7))

## 1.11.0 (2025-03-14)

* Merge pull request #199 from aplazo/mexp-446 ([65a3c6b](https://github.com/aplazo/angular.control-tower-dashboard/commit/65a3c6b)), closes [#199](https://github.com/aplazo/angular.control-tower-dashboard/issues/199)
* feat(maintenance): mexp-446 implement maintenance guard and message component ([cc49aea](https://github.com/aplazo/angular.control-tower-dashboard/commit/cc49aea))

## 1.10.0 (2025-03-14)

* Merge pull request #198 from aplazo/release ([d38ab1e](https://github.com/aplazo/angular.control-tower-dashboard/commit/d38ab1e)), closes [#198](https://github.com/aplazo/angular.control-tower-dashboard/issues/198)
* feat(campaigns): mexp-402, mexp-410 and mexp-411 ([9816b29](https://github.com/aplazo/angular.control-tower-dashboard/commit/9816b29))

## 1.9.0 (2025-03-13)

* Merge pull request #194 from aplazo/MEXP-412 ([832f000](https://github.com/aplazo/angular.control-tower-dashboard/commit/832f000)), closes [#194](https://github.com/aplazo/angular.control-tower-dashboard/issues/194)
* feat: mexp-412 remove mockdata and implement usecase ([ce7ce99](https://github.com/aplazo/angular.control-tower-dashboard/commit/ce7ce99))
* feat: mexp-412 tests: new test added for new usecase, update current tests ([68a340d](https://github.com/aplazo/angular.control-tower-dashboard/commit/68a340d))
* feat: mexp-412 tests: optimiza new tests code ([6920d0a](https://github.com/aplazo/angular.control-tower-dashboard/commit/6920d0a))

## 1.8.0 (2025-03-08)

* Merge pull request #192 from aplazo/MEXP-410 ([93d0c49](https://github.com/aplazo/angular.control-tower-dashboard/commit/93d0c49)), closes [#192](https://github.com/aplazo/angular.control-tower-dashboard/issues/192)
* feat(campaigns): mexp-410 fix validation to show the winners table ([2aab76f](https://github.com/aplazo/angular.control-tower-dashboard/commit/2aab76f))

## 1.7.0 (2025-03-07)

* Merge branch 'master' into MEXP-410 ([98ea7b7](https://github.com/aplazo/angular.control-tower-dashboard/commit/98ea7b7))
* Merge branch 'master' into MEXP-410 ([ed948ba](https://github.com/aplazo/angular.control-tower-dashboard/commit/ed948ba))
* Merge branch 'master' into MEXP-410 ([b340878](https://github.com/aplazo/angular.control-tower-dashboard/commit/b340878))
* Merge pull request #189 from aplazo/MEXP-410 ([0884230](https://github.com/aplazo/angular.control-tower-dashboard/commit/0884230)), closes [#189](https://github.com/aplazo/angular.control-tower-dashboard/issues/189)
* feat: mexp-410 adjust use case ([e7813f8](https://github.com/aplazo/angular.control-tower-dashboard/commit/e7813f8))
* feat: mexp-410 remove unused column in winners table ([b8f0eba](https://github.com/aplazo/angular.control-tower-dashboard/commit/b8f0eba))
* feat: mexp-410 winners page ([9c0a5a7](https://github.com/aplazo/angular.control-tower-dashboard/commit/9c0a5a7))
* feat(campaings): mexp-410 feature flag added ([cea79c4](https://github.com/aplazo/angular.control-tower-dashboard/commit/cea79c4))
* feat(campaings): mexp-410 tests: adjusts test for using feature flags ([6910cf4](https://github.com/aplazo/angular.control-tower-dashboard/commit/6910cf4))
* feat(campaings): mexp-410 tests: tests files added ([120af8c](https://github.com/aplazo/angular.control-tower-dashboard/commit/120af8c))

## 1.6.0 (2025-03-07)

* Merge pull request #190 from aplazo/MEXP-402 ([62cc2dc](https://github.com/aplazo/angular.control-tower-dashboard/commit/62cc2dc)), closes [#190](https://github.com/aplazo/angular.control-tower-dashboard/issues/190)
* feat(campaigns): mexp-402 fix validation to fetch numberOfWinners value ([3ba8272](https://github.com/aplazo/angular.control-tower-dashboard/commit/3ba8272))
* feat(campaigns): mexp-402 remove console.logs ([2332df3](https://github.com/aplazo/angular.control-tower-dashboard/commit/2332df3))

## 1.5.0 (2025-03-06)

* Merge pull request #188 from aplazo/mexp-402 ([a5ed504](https://github.com/aplazo/angular.control-tower-dashboard/commit/a5ed504)), closes [#188](https://github.com/aplazo/angular.control-tower-dashboard/issues/188)
* feat(campaigns): mexp-402 add feature flag for prize distribution and refactor campaign form ([c4adefd](https://github.com/aplazo/angular.control-tower-dashboard/commit/c4adefd))

## 1.4.0 (2025-03-06)

* Merge pull request #187 from aplazo/mexp-336 ([a7bb4ca](https://github.com/aplazo/angular.control-tower-dashboard/commit/a7bb4ca)), closes [#187](https://github.com/aplazo/angular.control-tower-dashboard/issues/187)
* test(login): mexp-336 add feature flags service mock for login component spec ([aee6deb](https://github.com/aplazo/angular.control-tower-dashboard/commit/aee6deb))
* feat(feature-flags): mexp-336 integrate Statsig feature flags service ([8887cb1](https://github.com/aplazo/angular.control-tower-dashboard/commit/8887cb1))

## <small>1.3.1 (2025-03-06)</small>

* fix(jenkins): mexp-336 improve coverage volume detection robustness ([e86611d](https://github.com/aplazo/angular.control-tower-dashboard/commit/e86611d))

## 1.3.0 (2025-03-05)

* Merge branch 'master' into MEXP-402 ([1f22cee](https://github.com/aplazo/angular.control-tower-dashboard/commit/1f22cee))
* Merge branch 'MEXP-402' of github.com:aplazo/angular.control-tower-dashboard into MEXP-402 ([044568d](https://github.com/aplazo/angular.control-tower-dashboard/commit/044568d))
* Merge pull request #186 from aplazo/MEXP-402 ([ce02a99](https://github.com/aplazo/angular.control-tower-dashboard/commit/ce02a99)), closes [#186](https://github.com/aplazo/angular.control-tower-dashboard/issues/186)
* feat: mexp-402 adjust tests ([a2016e4](https://github.com/aplazo/angular.control-tower-dashboard/commit/a2016e4))
* feat: mexp-402 adjust tests for rest campaign fetcher ([d40657e](https://github.com/aplazo/angular.control-tower-dashboard/commit/d40657e))
* feat: mexp-402 ramove date filter ([00c2dfa](https://github.com/aplazo/angular.control-tower-dashboard/commit/00c2dfa))

## 1.2.0 (2025-03-05)

* Merge branch 'master' into integration/stage ([c39fe82](https://github.com/aplazo/angular.control-tower-dashboard/commit/c39fe82))
* Merge branch 'mexp-307' into integration/stage ([2ed63ff](https://github.com/aplazo/angular.control-tower-dashboard/commit/2ed63ff))
* Merge pull request #183 from aplazo/integration/stage ([a1240ec](https://github.com/aplazo/angular.control-tower-dashboard/commit/a1240ec)), closes [#183](https://github.com/aplazo/angular.control-tower-dashboard/issues/183)
* Merge pull request #185 from aplazo/mexp-307-deploy ([d46a447](https://github.com/aplazo/angular.control-tower-dashboard/commit/d46a447)), closes [#185](https://github.com/aplazo/angular.control-tower-dashboard/issues/185)
* feat: mexp 307 premios aplazo ([1e02b1a](https://github.com/aplazo/angular.control-tower-dashboard/commit/1e02b1a))
* docs: mexp-433 enhance documentation ([8f4b5d1](https://github.com/aplazo/angular.control-tower-dashboard/commit/8f4b5d1))
* chore: mexp-307 add missing validations and interactions ([c9b8b5f](https://github.com/aplazo/angular.control-tower-dashboard/commit/c9b8b5f))
* chore: mexp-307 adjust current test cases ([391b196](https://github.com/aplazo/angular.control-tower-dashboard/commit/391b196))
* chore: mexp-307 adjust prize property ([5c3cd27](https://github.com/aplazo/angular.control-tower-dashboard/commit/5c3cd27))
* chore: mexp-307 adjust validations ([8b3eece](https://github.com/aplazo/angular.control-tower-dashboard/commit/8b3eece))
* chore: mexp-307 adjust validations ([cb04d52](https://github.com/aplazo/angular.control-tower-dashboard/commit/cb04d52))
* chore: mexp-307 adjust validations and integrate usecase as a function ([64146a1](https://github.com/aplazo/angular.control-tower-dashboard/commit/64146a1))
* chore: mexp-307 adjust validators and template ([2e613e6](https://github.com/aplazo/angular.control-tower-dashboard/commit/2e613e6))
* chore: mexp-307 adjust validators and template ([ed89061](https://github.com/aplazo/angular.control-tower-dashboard/commit/ed89061))
* chore: mexp-307 custom validators removed ([f1ebee7](https://github.com/aplazo/angular.control-tower-dashboard/commit/f1ebee7))
* chore: mexp-307 fix for disable the prize distribution section and ranges ([1d0e9b9](https://github.com/aplazo/angular.control-tower-dashboard/commit/1d0e9b9))
* chore: mexp-307 fix properties names ([5c12a9e](https://github.com/aplazo/angular.control-tower-dashboard/commit/5c12a9e))
* chore: mexp-307 isolate distribution prices logic ([6f24b31](https://github.com/aplazo/angular.control-tower-dashboard/commit/6f24b31))
* chore: mexp-307 optimize code by remove code duplication ([a3877d7](https://github.com/aplazo/angular.control-tower-dashboard/commit/a3877d7))
* chore: mexp-307 remove unused method ([4660592](https://github.com/aplazo/angular.control-tower-dashboard/commit/4660592))
* chore: mexp-307 update files to allow add and update prices ([7334b9a](https://github.com/aplazo/angular.control-tower-dashboard/commit/7334b9a))

## <small>1.1.6 (2025-03-05)</small>

* fix(ci): mexp-336 improve Jenkins pipeline coverage volume handling ([da30083](https://github.com/aplazo/angular.control-tower-dashboard/commit/da30083))
* chore(deps): mexp-336 update @ngx-env/builder and package dependencies ([98af983](https://github.com/aplazo/angular.control-tower-dashboard/commit/98af983))

## <small>1.1.5 (2025-03-04)</small>

* fix(ci): mexp-336 enhance Jenkins pipeline secret management ([cea20d1](https://github.com/aplazo/angular.control-tower-dashboard/commit/cea20d1))

## <small>1.1.4 (2025-03-03)</small>

* fix(ci): mexp-336 refactor Jenkins pipeline version management ([f29ada9](https://github.com/aplazo/angular.control-tower-dashboard/commit/f29ada9))

## <small>1.1.3 (2025-03-03)</small>

* fix(ci): mexp-336 enhance version management with stash and unstash strategy ([2f75d48](https://github.com/aplazo/angular.control-tower-dashboard/commit/2f75d48))

## <small>1.1.2 (2025-03-03)</small>

* fix(ci): mexp-336 simplify version management in Jenkins pipeline ([0a388e1](https://github.com/aplazo/angular.control-tower-dashboard/commit/0a388e1))

## <small>1.1.1 (2025-03-03)</small>

* fix(ci): mexp-336 improve version management in Jenkins pipeline ([108b857](https://github.com/aplazo/angular.control-tower-dashboard/commit/108b857))

## 1.1.0 (2025-03-03)

* feat(ci): mexp-336 remove tag dependency from deployment pipeline ([5563e83](https://github.com/aplazo/angular.control-tower-dashboard/commit/5563e83))

## <small>1.0.3 (2025-03-03)</small>

* fix: mexp-336 bump package version ([2556eeb](https://github.com/aplazo/angular.control-tower-dashboard/commit/2556eeb))

## <small>1.0.2 (2025-03-03)</small>

* fix: mexp-336 dont rerun after git commands in pipeline ([2563693](https://github.com/aplazo/angular.control-tower-dashboard/commit/2563693))
* fix: mexp-336 escaped characters ([1bf400e](https://github.com/aplazo/angular.control-tower-dashboard/commit/1bf400e))
* fix: mexp-336 fix coverage file creation ([f1e2297](https://github.com/aplazo/angular.control-tower-dashboard/commit/f1e2297))

## <small>1.0.1 (2025-03-03)</small>

* Merge pull request #181 from aplazo/mexp-336 ([4531b03](https://github.com/aplazo/angular.control-tower-dashboard/commit/4531b03)), closes [#181](https://github.com/aplazo/angular.control-tower-dashboard/issues/181)
* fix: mexp-336 dont run in pr ([b891884](https://github.com/aplazo/angular.control-tower-dashboard/commit/b891884))

## 1.0.0 (2025-03-03)

* feat: add app loader ui component ([439813e](https://github.com/aplazo/angular.control-tower-dashboard/commit/439813e))
* feat: add app loader ui component ([d6daee5](https://github.com/aplazo/angular.control-tower-dashboard/commit/d6daee5))
* feat: add auth guards ([0de06bf](https://github.com/aplazo/angular.control-tower-dashboard/commit/0de06bf))
* feat: add auth guards ([0b2c5bb](https://github.com/aplazo/angular.control-tower-dashboard/commit/0b2c5bb))
* feat: add basics info component ([ba5259e](https://github.com/aplazo/angular.control-tower-dashboard/commit/ba5259e))
* feat: add dockerfile and fix tailwind types ([8224394](https://github.com/aplazo/angular.control-tower-dashboard/commit/8224394))
* feat: add generals component ([bcbec7c](https://github.com/aplazo/angular.control-tower-dashboard/commit/bcbec7c))
* feat: add jenkinsfile ([146cfed](https://github.com/aplazo/angular.control-tower-dashboard/commit/146cfed))
* feat: add login dtos ([877d891](https://github.com/aplazo/angular.control-tower-dashboard/commit/877d891))
* feat: add login dtos ([b082c53](https://github.com/aplazo/angular.control-tower-dashboard/commit/b082c53))
* feat: add login repository implementation ([f9ea8e2](https://github.com/aplazo/angular.control-tower-dashboard/commit/f9ea8e2))
* feat: add login repository implementation ([493b520](https://github.com/aplazo/angular.control-tower-dashboard/commit/493b520))
* feat: Add Mac ARM64 docker configuration and update dev server settings ([9b113f2](https://github.com/aplazo/angular.control-tower-dashboard/commit/9b113f2))
* feat: add makefile to build/run docker img ([c002d44](https://github.com/aplazo/angular.control-tower-dashboard/commit/c002d44))
* feat: add new serializer method ([d2951aa](https://github.com/aplazo/angular.control-tower-dashboard/commit/d2951aa))
* feat: add new serializer method ([bb6915a](https://github.com/aplazo/angular.control-tower-dashboard/commit/bb6915a))
* feat: add persistence service ([a15f9d4](https://github.com/aplazo/angular.control-tower-dashboard/commit/a15f9d4))
* feat: add persistence service ([a4aaa11](https://github.com/aplazo/angular.control-tower-dashboard/commit/a4aaa11))
* feat: add refresh login usecase ([ab56a81](https://github.com/aplazo/angular.control-tower-dashboard/commit/ab56a81))
* feat: add refresh login usecase ([7d47598](https://github.com/aplazo/angular.control-tower-dashboard/commit/7d47598))
* feat: Add shield_local_test service for containerized unit testing ([12eff23](https://github.com/aplazo/angular.control-tower-dashboard/commit/12eff23))
* feat: add user entity ([38be528](https://github.com/aplazo/angular.control-tower-dashboard/commit/38be528))
* feat: add user entity ([72c1467](https://github.com/aplazo/angular.control-tower-dashboard/commit/72c1467))
* feat: add user store service ([cae6fdc](https://github.com/aplazo/angular.control-tower-dashboard/commit/cae6fdc))
* feat: add user store service ([02d894c](https://github.com/aplazo/angular.control-tower-dashboard/commit/02d894c))
* feat: add valid roles ([2e277ca](https://github.com/aplazo/angular.control-tower-dashboard/commit/2e277ca))
* feat: add valid roles ([b7eafb1](https://github.com/aplazo/angular.control-tower-dashboard/commit/b7eafb1))
* feat: APM-2129 add refresh login to app initializer factory ([ce6c961](https://github.com/aplazo/angular.control-tower-dashboard/commit/ce6c961))
* feat: APM-2129 add refresh login to app initializer factory ([2511629](https://github.com/aplazo/angular.control-tower-dashboard/commit/2511629))
* feat: APM-2129 change routing strategy and render components ([74347a9](https://github.com/aplazo/angular.control-tower-dashboard/commit/74347a9))
* feat: APM-2129 redirect in success login usecase ([16766e4](https://github.com/aplazo/angular.control-tower-dashboard/commit/16766e4))
* feat: APM-2129 redirect in success login usecase ([792c01b](https://github.com/aplazo/angular.control-tower-dashboard/commit/792c01b))
* feat: APM-2129 refactor routes ([4d6b3db](https://github.com/aplazo/angular.control-tower-dashboard/commit/4d6b3db))
* feat: APM-2130 add colors to adjustments in payments module ([8b794ec](https://github.com/aplazo/angular.control-tower-dashboard/commit/8b794ec))
* feat: apm-2130 add send report usecase ([34969c1](https://github.com/aplazo/angular.control-tower-dashboard/commit/34969c1))
* feat: apm-2130 handle null values for summary ([75ab649](https://github.com/aplazo/angular.control-tower-dashboard/commit/75ab649))
* feat: apm-2130 payments list ([1b26093](https://github.com/aplazo/angular.control-tower-dashboard/commit/1b26093))
* feat: apm-2130 payments module ([f2a41b5](https://github.com/aplazo/angular.control-tower-dashboard/commit/f2a41b5))
* feat: APM-2130 set icon to payments sidenavlink ([85e95e9](https://github.com/aplazo/angular.control-tower-dashboard/commit/85e95e9))
* feat: apm-2296 add admin mapper ([66c385a](https://github.com/aplazo/angular.control-tower-dashboard/commit/66c385a))
* feat: apm-2296 add business metrics ([b529645](https://github.com/aplazo/angular.control-tower-dashboard/commit/b529645))
* feat: apm-2296 add components to creation page ([2aba0be](https://github.com/aplazo/angular.control-tower-dashboard/commit/2aba0be))
* feat: apm-2296 add deactivated guard to merchant creation ([500fcb3](https://github.com/aplazo/angular.control-tower-dashboard/commit/500fcb3))
* feat: apm-2296 add info merchant component ([9dfd857](https://github.com/aplazo/angular.control-tower-dashboard/commit/9dfd857))
* feat: apm-2296 add legal representative component ([aff831d](https://github.com/aplazo/angular.control-tower-dashboard/commit/aff831d))
* feat: apm-2296 add merchant creation component ([1ec63a9](https://github.com/aplazo/angular.control-tower-dashboard/commit/1ec63a9))
* feat: apm-2296 add new merchant repository ([9b2179a](https://github.com/aplazo/angular.control-tower-dashboard/commit/9b2179a))
* feat: apm-2296 add new merchant request dto ([f82de3c](https://github.com/aplazo/angular.control-tower-dashboard/commit/f82de3c))
* feat: apm-2296 add new merchant response dto ([3390e35](https://github.com/aplazo/angular.control-tower-dashboard/commit/3390e35))
* feat: apm-2296 add new merchant usecase ([f002241](https://github.com/aplazo/angular.control-tower-dashboard/commit/f002241))
* feat: apm-2296 add webpage validation to mapper ([d79efa7](https://github.com/aplazo/angular.control-tower-dashboard/commit/d79efa7))
* feat: apm-2296 improve confirm prospect component ([43e7805](https://github.com/aplazo/angular.control-tower-dashboard/commit/43e7805))
* feat: apm-2297 add admin feature module ([638aeb7](https://github.com/aplazo/angular.control-tower-dashboard/commit/638aeb7))
* feat: apm-2297 add extra getter to menu store ([cce4719](https://github.com/aplazo/angular.control-tower-dashboard/commit/cce4719))
* feat: apm-2297 add match by menu ([d8cb09e](https://github.com/aplazo/angular.control-tower-dashboard/commit/d8cb09e))
* feat: apm-2297 add menu store ([76b0945](https://github.com/aplazo/angular.control-tower-dashboard/commit/76b0945))
* feat: apm-2297 add new valid role ([e91d1e8](https://github.com/aplazo/angular.control-tower-dashboard/commit/e91d1e8))
* feat: apm-2297 add sidenav links for admin module ([179a35e](https://github.com/aplazo/angular.control-tower-dashboard/commit/179a35e))
* feat: apm-2297 add user email entity ([40e3804](https://github.com/aplazo/angular.control-tower-dashboard/commit/40e3804))
* feat: apm-2297 hydrate menu store on login usecase ([bad4e8a](https://github.com/aplazo/angular.control-tower-dashboard/commit/bad4e8a))
* feat: apm-2297 implement canmatch by menu ([73109aa](https://github.com/aplazo/angular.control-tower-dashboard/commit/73109aa))
* feat: apm-2349 add new admin providers ([58c7541](https://github.com/aplazo/angular.control-tower-dashboard/commit/58c7541))
* feat: apm-2349 add new entities/dtos ([6cf4da8](https://github.com/aplazo/angular.control-tower-dashboard/commit/6cf4da8))
* feat: apm-2349 add seach by usecase ([a169027](https://github.com/aplazo/angular.control-tower-dashboard/commit/a169027))
* feat: apm-2349 add search by repository ([7e64b93](https://github.com/aplazo/angular.control-tower-dashboard/commit/7e64b93))
* feat: apm-2349 admin layout improvements ([c9bee28](https://github.com/aplazo/angular.control-tower-dashboard/commit/c9bee28))
* feat: apm-2349 change admin layout ([c0a4545](https://github.com/aplazo/angular.control-tower-dashboard/commit/c0a4545))
* feat: apm-2349 get basics info repository ([d4bb2e8](https://github.com/aplazo/angular.control-tower-dashboard/commit/d4bb2e8))
* feat: apm-2349 get generals info repository ([dfd368f](https://github.com/aplazo/angular.control-tower-dashboard/commit/dfd368f))
* feat: apm-2349 improve merchant store ([0d5dedd](https://github.com/aplazo/angular.control-tower-dashboard/commit/0d5dedd))
* feat: apm-2361 add account component ([5feb7a8](https://github.com/aplazo/angular.control-tower-dashboard/commit/5feb7a8))
* feat: apm-2362 add invoice page ([c25a107](https://github.com/aplazo/angular.control-tower-dashboard/commit/c25a107))
* feat: apm-2363 add contacts ([46b1ff2](https://github.com/aplazo/angular.control-tower-dashboard/commit/46b1ff2))
* feat: apm-2364 add mdr ([fd8dcfe](https://github.com/aplazo/angular.control-tower-dashboard/commit/fd8dcfe))
* feat: apm-2365 add storefronts page ([bc7825f](https://github.com/aplazo/angular.control-tower-dashboard/commit/bc7825f))
* feat: apm-2366 add users page ([adfc4fb](https://github.com/aplazo/angular.control-tower-dashboard/commit/adfc4fb))
* feat: apm-2465 update merchant status ([f58d760](https://github.com/aplazo/angular.control-tower-dashboard/commit/f58d760))
* feat: apm-2518 add receipts ([1dac7e9](https://github.com/aplazo/angular.control-tower-dashboard/commit/1dac7e9))
* feat: apm-2518 menu improvements ([59a8fd1](https://github.com/aplazo/angular.control-tower-dashboard/commit/59a8fd1))
* feat: apm-2518 uploading files ([c035190](https://github.com/aplazo/angular.control-tower-dashboard/commit/c035190))
* feat: apm-2528 sidebar click outside improvements ([11db5ad](https://github.com/aplazo/angular.control-tower-dashboard/commit/11db5ad))
* feat: apm-2569 basics editing in component ([234842d](https://github.com/aplazo/angular.control-tower-dashboard/commit/234842d))
* feat: apm-2569 new contract to basic info ([2bfc7f3](https://github.com/aplazo/angular.control-tower-dashboard/commit/2bfc7f3))
* feat: apm-2569 rename filename ([f59b94b](https://github.com/aplazo/angular.control-tower-dashboard/commit/f59b94b))
* feat: apm-2572 update account ([76ed997](https://github.com/aplazo/angular.control-tower-dashboard/commit/76ed997))
* feat: apm-2573 improve invoice entities ([2e606ca](https://github.com/aplazo/angular.control-tower-dashboard/commit/2e606ca))
* feat: apm-2574 add storefront edit/create ([ba62cc1](https://github.com/aplazo/angular.control-tower-dashboard/commit/ba62cc1))
* feat: apm-2575 update mdr usecase ([cc78457](https://github.com/aplazo/angular.control-tower-dashboard/commit/cc78457))
* feat: apm-2656 remove page url validation ([35acbfd](https://github.com/aplazo/angular.control-tower-dashboard/commit/35acbfd))
* feat: apm-2656 remove page url validation ([b043b31](https://github.com/aplazo/angular.control-tower-dashboard/commit/b043b31))
* feat: apm-2656 remove unused status from onboarding ([bd2eafc](https://github.com/aplazo/angular.control-tower-dashboard/commit/bd2eafc))
* feat: apm-2701 create user feature ([7dae682](https://github.com/aplazo/angular.control-tower-dashboard/commit/7dae682))
* feat: apm-2701 create user form component ([6de2368](https://github.com/aplazo/angular.control-tower-dashboard/commit/6de2368))
* feat: apm-2701 edit user ([80f69db](https://github.com/aplazo/angular.control-tower-dashboard/commit/80f69db))
* feat: apm-2701 edit user issues ([7481224](https://github.com/aplazo/angular.control-tower-dashboard/commit/7481224))
* feat: apm-2701 edit users ([ed45ef2](https://github.com/aplazo/angular.control-tower-dashboard/commit/ed45ef2))
* feat: apm-2718 create contact ([2f9a509](https://github.com/aplazo/angular.control-tower-dashboard/commit/2f9a509))
* feat: apm-2718 edit contact ([b381751](https://github.com/aplazo/angular.control-tower-dashboard/commit/b381751))
* feat: apm-2811 invoice generator ([5f05ebc](https://github.com/aplazo/angular.control-tower-dashboard/commit/5f05ebc))
* feat: apm-2896 industries list changes ([2e06322](https://github.com/aplazo/angular.control-tower-dashboard/commit/2e06322))
* feat: apm-2897 remove category field from generals ([729adf3](https://github.com/aplazo/angular.control-tower-dashboard/commit/729adf3))
* feat: camapign fetcher pagination and basic form ([35a759b](https://github.com/aplazo/angular.control-tower-dashboard/commit/35a759b))
* feat: centralize mapping chores ([b4f8b1f](https://github.com/aplazo/angular.control-tower-dashboard/commit/b4f8b1f))
* feat: centralize mapping chores ([046c11a](https://github.com/aplazo/angular.control-tower-dashboard/commit/046c11a))
* feat: define login repository ([906e1b7](https://github.com/aplazo/angular.control-tower-dashboard/commit/906e1b7))
* feat: define login repository ([6c41177](https://github.com/aplazo/angular.control-tower-dashboard/commit/6c41177))
* feat: dx improvements ([93118ae](https://github.com/aplazo/angular.control-tower-dashboard/commit/93118ae))
* feat: edit generals ([3347532](https://github.com/aplazo/angular.control-tower-dashboard/commit/3347532))
* feat: google GSI with button in login page ([df5f4c8](https://github.com/aplazo/angular.control-tower-dashboard/commit/df5f4c8))
* feat: inject and resolve root providers ([8075e75](https://github.com/aplazo/angular.control-tower-dashboard/commit/8075e75))
* feat: inject and resolve root providers ([9d874ac](https://github.com/aplazo/angular.control-tower-dashboard/commit/9d874ac))
* feat: mac-125 add phone to contact form ([0a5287e](https://github.com/aplazo/angular.control-tower-dashboard/commit/0a5287e))
* feat: mac-135 invoices by id ([c26bb32](https://github.com/aplazo/angular.control-tower-dashboard/commit/c26bb32))
* feat: mac-140 merchant status changes ([b9fcee0](https://github.com/aplazo/angular.control-tower-dashboard/commit/b9fcee0))
* feat: mac-164 add excluded invoice status ([6f0eab7](https://github.com/aplazo/angular.control-tower-dashboard/commit/6f0eab7))
* feat: mac-204 show error msg from update basics ([21d4968](https://github.com/aplazo/angular.control-tower-dashboard/commit/21d4968))
* feat: mac-63 changing logic to accept empty email and shows branches ([5228bb7](https://github.com/aplazo/angular.control-tower-dashboard/commit/5228bb7))
* feat: mac-63 hydrate branches on select merchant ([37a1941](https://github.com/aplazo/angular.control-tower-dashboard/commit/37a1941))
* feat: mac-77 retrieve invoice summary for payments ([e0b3644](https://github.com/aplazo/angular.control-tower-dashboard/commit/e0b3644))
* feat: mac-78 add invoice status to payments list ([9ed20e6](https://github.com/aplazo/angular.control-tower-dashboard/commit/9ed20e6))
* feat: mexp-200 create one campaign ([c6db383](https://github.com/aplazo/angular.control-tower-dashboard/commit/c6db383))
* feat: mexp-208 improve usecase ([df1f6ab](https://github.com/aplazo/angular.control-tower-dashboard/commit/df1f6ab))
* feat: mexp-209 edit campaign ([eaaf117](https://github.com/aplazo/angular.control-tower-dashboard/commit/eaaf117))
* feat: mexp-315 replace old role with a new one ([84ab71c](https://github.com/aplazo/angular.control-tower-dashboard/commit/84ab71c))
* feat: mexp-317 add tyc link to campaign form ([3af0306](https://github.com/aplazo/angular.control-tower-dashboard/commit/3af0306))
* feat: mexp-334 Enhance Jenkins pipeline with improved Slack notifications and secret management ([75530e4](https://github.com/aplazo/angular.control-tower-dashboard/commit/75530e4))
* feat: mexp-334 migrate to @ngx-env/builder for environment configuration ([52360c3](https://github.com/aplazo/angular.control-tower-dashboard/commit/52360c3))
* feat: mexp-77 form datepicker ([2f3f37f](https://github.com/aplazo/angular.control-tower-dashboard/commit/2f3f37f))
* feat: Update Docker and Jenkins configuration for multi-image build and deployment ([9d7241c](https://github.com/aplazo/angular.control-tower-dashboard/commit/9d7241c))
* feat(jenkins): mexp-336 add Git branch name retrieval for SonarQube version ([81bc26b](https://github.com/aplazo/angular.control-tower-dashboard/commit/81bc26b))
* feat(jenkins): mexp-336 enhance secret management with JSON to .env conversion utility ([fcecad7](https://github.com/aplazo/angular.control-tower-dashboard/commit/fcecad7))
* feat(jenkins): mexp-336 optimize npm release process with ci install ([fa7e1ec](https://github.com/aplazo/angular.control-tower-dashboard/commit/fa7e1ec))
* feat(jenkins): mexp-336 refactor deployment workflow with enhanced CI/CD pipeline ([3887843](https://github.com/aplazo/angular.control-tower-dashboard/commit/3887843))
* feat(release): mexp-336 configure semantic-release script in package.json ([d748a61](https://github.com/aplazo/angular.control-tower-dashboard/commit/d748a61))
* feat(shared-ui): apm-2518 all logic completed ([a4ee980](https://github.com/aplazo/angular.control-tower-dashboard/commit/a4ee980))
* add call to register history for receipts ([be1e42c](https://github.com/aplazo/angular.control-tower-dashboard/commit/be1e42c))
* add receipts get list history ([7a4113e](https://github.com/aplazo/angular.control-tower-dashboard/commit/7a4113e))
* add table for history receipts upload ([54407f4](https://github.com/aplazo/angular.control-tower-dashboard/commit/54407f4))
* add test ([ad94eb4](https://github.com/aplazo/angular.control-tower-dashboard/commit/ad94eb4))
* add test for repository receipt-history ([4594bc3](https://github.com/aplazo/angular.control-tower-dashboard/commit/4594bc3))
* added a env variable for testing ([5012e86](https://github.com/aplazo/angular.control-tower-dashboard/commit/5012e86))
* change fit to it in receipt-list-with-http.repository.spec.ts ([15a584b](https://github.com/aplazo/angular.control-tower-dashboard/commit/15a584b))
* feat:campaign module ([a8c446f](https://github.com/aplazo/angular.control-tower-dashboard/commit/a8c446f))
* feat:fetcher config ([598f996](https://github.com/aplazo/angular.control-tower-dashboard/commit/598f996))
* feat:routes ([a742cb4](https://github.com/aplazo/angular.control-tower-dashboard/commit/a742cb4))
* feat:routes ([60d8141](https://github.com/aplazo/angular.control-tower-dashboard/commit/60d8141))
* initial commit ([a473896](https://github.com/aplazo/angular.control-tower-dashboard/commit/a473896))
* Merge branch 'integration/develop' into feat/apm-2129 ([1bcdc9b](https://github.com/aplazo/angular.control-tower-dashboard/commit/1bcdc9b))
* Merge branch 'integration/develop' into feature/apm-2872 ([1ac15d0](https://github.com/aplazo/angular.control-tower-dashboard/commit/1ac15d0))
* Merge branch 'integration/stage' into integration/develop ([9bb73cb](https://github.com/aplazo/angular.control-tower-dashboard/commit/9bb73cb))
* Merge branch 'integration/stage' into mexp-200 ([3985784](https://github.com/aplazo/angular.control-tower-dashboard/commit/3985784))
* Merge branch 'integration/stage' into mexp-208 ([7878ffb](https://github.com/aplazo/angular.control-tower-dashboard/commit/7878ffb))
* Merge branch 'integration/stage' into mexp-303 ([a492b98](https://github.com/aplazo/angular.control-tower-dashboard/commit/a492b98))
* Merge branch 'integration/stage' into mexp-303 ([00e1435](https://github.com/aplazo/angular.control-tower-dashboard/commit/00e1435))
* Merge branch 'integration/stage' into mexp-313 ([9a082fd](https://github.com/aplazo/angular.control-tower-dashboard/commit/9a082fd))
* Merge branch 'integration/stage' into mexp-334 ([016c67b](https://github.com/aplazo/angular.control-tower-dashboard/commit/016c67b))
* Merge branch 'integration/stage' into mexp-336 ([752b820](https://github.com/aplazo/angular.control-tower-dashboard/commit/752b820))
* Merge branch 'master' into apm-2811 ([575c94f](https://github.com/aplazo/angular.control-tower-dashboard/commit/575c94f))
* Merge branch 'master' into feature/apm-2874 ([2d25d55](https://github.com/aplazo/angular.control-tower-dashboard/commit/2d25d55))
* Merge branch 'master' into integration/stage ([290d207](https://github.com/aplazo/angular.control-tower-dashboard/commit/290d207))
* Merge branch 'mexp-303' of github.com:aplazo/angular.control-tower-dashboard into mexp-303 ([72cc266](https://github.com/aplazo/angular.control-tower-dashboard/commit/72cc266))
* Merge pull request #1 from aplazo/integration/develop ([f50b320](https://github.com/aplazo/angular.control-tower-dashboard/commit/f50b320)), closes [#1](https://github.com/aplazo/angular.control-tower-dashboard/issues/1)
* Merge pull request #100 from aplazo/apm-2811 ([8483353](https://github.com/aplazo/angular.control-tower-dashboard/commit/8483353)), closes [#100](https://github.com/aplazo/angular.control-tower-dashboard/issues/100)
* Merge pull request #101 from aplazo/apm-2892 ([1e8da7b](https://github.com/aplazo/angular.control-tower-dashboard/commit/1e8da7b)), closes [#101](https://github.com/aplazo/angular.control-tower-dashboard/issues/101)
* Merge pull request #102 from aplazo/integration/develop ([5503152](https://github.com/aplazo/angular.control-tower-dashboard/commit/5503152)), closes [#102](https://github.com/aplazo/angular.control-tower-dashboard/issues/102)
* Merge pull request #103 from aplazo/integration/stage ([2e15dde](https://github.com/aplazo/angular.control-tower-dashboard/commit/2e15dde)), closes [#103](https://github.com/aplazo/angular.control-tower-dashboard/issues/103)
* Merge pull request #104 from aplazo/apm-2718 ([1d5078d](https://github.com/aplazo/angular.control-tower-dashboard/commit/1d5078d)), closes [#104](https://github.com/aplazo/angular.control-tower-dashboard/issues/104)
* Merge pull request #105 from aplazo/integration/develop ([f62f594](https://github.com/aplazo/angular.control-tower-dashboard/commit/f62f594)), closes [#105](https://github.com/aplazo/angular.control-tower-dashboard/issues/105)
* Merge pull request #106 from aplazo/apm-2896 ([73d2d90](https://github.com/aplazo/angular.control-tower-dashboard/commit/73d2d90)), closes [#106](https://github.com/aplazo/angular.control-tower-dashboard/issues/106)
* Merge pull request #107 from aplazo/apm-2897 ([b87e5ad](https://github.com/aplazo/angular.control-tower-dashboard/commit/b87e5ad)), closes [#107](https://github.com/aplazo/angular.control-tower-dashboard/issues/107)
* Merge pull request #108 from aplazo/integration/develop ([42a5d93](https://github.com/aplazo/angular.control-tower-dashboard/commit/42a5d93)), closes [#108](https://github.com/aplazo/angular.control-tower-dashboard/issues/108)
* Merge pull request #109 from aplazo/integration/develop ([2669b88](https://github.com/aplazo/angular.control-tower-dashboard/commit/2669b88)), closes [#109](https://github.com/aplazo/angular.control-tower-dashboard/issues/109)
* Merge pull request #11 from aplazo/integration/develop ([3f47b9c](https://github.com/aplazo/angular.control-tower-dashboard/commit/3f47b9c)), closes [#11](https://github.com/aplazo/angular.control-tower-dashboard/issues/11)
* Merge pull request #110 from aplazo/integration/stage ([32a42c1](https://github.com/aplazo/angular.control-tower-dashboard/commit/32a42c1)), closes [#110](https://github.com/aplazo/angular.control-tower-dashboard/issues/110)
* Merge pull request #111 from aplazo/feature/apm-2874 ([7f3f86a](https://github.com/aplazo/angular.control-tower-dashboard/commit/7f3f86a)), closes [#111](https://github.com/aplazo/angular.control-tower-dashboard/issues/111)
* Merge pull request #112 from aplazo/integration/develop ([bcb7c19](https://github.com/aplazo/angular.control-tower-dashboard/commit/bcb7c19)), closes [#112](https://github.com/aplazo/angular.control-tower-dashboard/issues/112)
* Merge pull request #113 from aplazo/integration/stage ([375afc6](https://github.com/aplazo/angular.control-tower-dashboard/commit/375afc6)), closes [#113](https://github.com/aplazo/angular.control-tower-dashboard/issues/113)
* Merge pull request #114 from aplazo/integration/develop ([bdcba17](https://github.com/aplazo/angular.control-tower-dashboard/commit/bdcba17)), closes [#114](https://github.com/aplazo/angular.control-tower-dashboard/issues/114)
* Merge pull request #115 from aplazo/integration/stage ([ed25968](https://github.com/aplazo/angular.control-tower-dashboard/commit/ed25968)), closes [#115](https://github.com/aplazo/angular.control-tower-dashboard/issues/115)
* Merge pull request #116 from aplazo/mac-63 ([c94143a](https://github.com/aplazo/angular.control-tower-dashboard/commit/c94143a)), closes [#116](https://github.com/aplazo/angular.control-tower-dashboard/issues/116)
* Merge pull request #117 from aplazo/mac-77 ([3ece786](https://github.com/aplazo/angular.control-tower-dashboard/commit/3ece786)), closes [#117](https://github.com/aplazo/angular.control-tower-dashboard/issues/117)
* Merge pull request #118 from aplazo/mac-78 ([2e7152a](https://github.com/aplazo/angular.control-tower-dashboard/commit/2e7152a)), closes [#118](https://github.com/aplazo/angular.control-tower-dashboard/issues/118)
* Merge pull request #119 from aplazo/integration/stage ([aba9c66](https://github.com/aplazo/angular.control-tower-dashboard/commit/aba9c66)), closes [#119](https://github.com/aplazo/angular.control-tower-dashboard/issues/119)
* Merge pull request #12 from aplazo/integration/develop ([095b134](https://github.com/aplazo/angular.control-tower-dashboard/commit/095b134)), closes [#12](https://github.com/aplazo/angular.control-tower-dashboard/issues/12)
* Merge pull request #120 from aplazo/mac-87 ([d6cff42](https://github.com/aplazo/angular.control-tower-dashboard/commit/d6cff42)), closes [#120](https://github.com/aplazo/angular.control-tower-dashboard/issues/120)
* Merge pull request #121 from aplazo/integration/stage ([580dbbf](https://github.com/aplazo/angular.control-tower-dashboard/commit/580dbbf)), closes [#121](https://github.com/aplazo/angular.control-tower-dashboard/issues/121)
* Merge pull request #122 from aplazo/mac-125 ([329db26](https://github.com/aplazo/angular.control-tower-dashboard/commit/329db26)), closes [#122](https://github.com/aplazo/angular.control-tower-dashboard/issues/122)
* Merge pull request #123 from aplazo/integration/stage ([e390237](https://github.com/aplazo/angular.control-tower-dashboard/commit/e390237)), closes [#123](https://github.com/aplazo/angular.control-tower-dashboard/issues/123)
* Merge pull request #124 from aplazo/mac-135 ([69a16c5](https://github.com/aplazo/angular.control-tower-dashboard/commit/69a16c5)), closes [#124](https://github.com/aplazo/angular.control-tower-dashboard/issues/124)
* Merge pull request #125 from aplazo/integration/stage ([8c3ff80](https://github.com/aplazo/angular.control-tower-dashboard/commit/8c3ff80)), closes [#125](https://github.com/aplazo/angular.control-tower-dashboard/issues/125)
* Merge pull request #126 from aplazo/mac-140 ([0bcbf0c](https://github.com/aplazo/angular.control-tower-dashboard/commit/0bcbf0c)), closes [#126](https://github.com/aplazo/angular.control-tower-dashboard/issues/126)
* Merge pull request #127 from aplazo/mac-164 ([724612a](https://github.com/aplazo/angular.control-tower-dashboard/commit/724612a)), closes [#127](https://github.com/aplazo/angular.control-tower-dashboard/issues/127)
* Merge pull request #128 from aplazo/integration/stage ([2d4e667](https://github.com/aplazo/angular.control-tower-dashboard/commit/2d4e667)), closes [#128](https://github.com/aplazo/angular.control-tower-dashboard/issues/128)
* Merge pull request #129 from aplazo/integration/stage ([52994f2](https://github.com/aplazo/angular.control-tower-dashboard/commit/52994f2)), closes [#129](https://github.com/aplazo/angular.control-tower-dashboard/issues/129)
* Merge pull request #13 from aplazo/integration/develop ([fcabccc](https://github.com/aplazo/angular.control-tower-dashboard/commit/fcabccc)), closes [#13](https://github.com/aplazo/angular.control-tower-dashboard/issues/13)
* Merge pull request #130 from aplazo/integration/stage ([a82a06a](https://github.com/aplazo/angular.control-tower-dashboard/commit/a82a06a)), closes [#130](https://github.com/aplazo/angular.control-tower-dashboard/issues/130)
* Merge pull request #131 from aplazo/mac-204 ([1bdfa20](https://github.com/aplazo/angular.control-tower-dashboard/commit/1bdfa20)), closes [#131](https://github.com/aplazo/angular.control-tower-dashboard/issues/131)
* Merge pull request #132 from aplazo/integration/stage ([9106fb4](https://github.com/aplazo/angular.control-tower-dashboard/commit/9106fb4)), closes [#132](https://github.com/aplazo/angular.control-tower-dashboard/issues/132)
* Merge pull request #133 from aplazo/MEXP-205 ([eb8d5c7](https://github.com/aplazo/angular.control-tower-dashboard/commit/eb8d5c7)), closes [#133](https://github.com/aplazo/angular.control-tower-dashboard/issues/133)
* Merge pull request #134 from aplazo/MEXP-208 ([9ad00a0](https://github.com/aplazo/angular.control-tower-dashboard/commit/9ad00a0)), closes [#134](https://github.com/aplazo/angular.control-tower-dashboard/issues/134)
* Merge pull request #137 from aplazo/MEXP-208-Create-Campa-as-tab-details-in-Premios-Aplazo-sub-modul ([1166275](https://github.com/aplazo/angular.control-tower-dashboard/commit/1166275)), closes [#137](https://github.com/aplazo/angular.control-tower-dashboard/issues/137)
* Merge pull request #138 from aplazo/mexp-200 ([2681ca2](https://github.com/aplazo/angular.control-tower-dashboard/commit/2681ca2)), closes [#138](https://github.com/aplazo/angular.control-tower-dashboard/issues/138)
* Merge pull request #139 from aplazo/mexp-208 ([4e7ac94](https://github.com/aplazo/angular.control-tower-dashboard/commit/4e7ac94)), closes [#139](https://github.com/aplazo/angular.control-tower-dashboard/issues/139)
* Merge pull request #140 from aplazo/mexp-209 ([a596ca3](https://github.com/aplazo/angular.control-tower-dashboard/commit/a596ca3)), closes [#140](https://github.com/aplazo/angular.control-tower-dashboard/issues/140)
* Merge pull request #141 from aplazo/feature/PSPs ([383aee0](https://github.com/aplazo/angular.control-tower-dashboard/commit/383aee0)), closes [#141](https://github.com/aplazo/angular.control-tower-dashboard/issues/141)
* Merge pull request #142 from aplazo/mexp-209 ([42e846c](https://github.com/aplazo/angular.control-tower-dashboard/commit/42e846c)), closes [#142](https://github.com/aplazo/angular.control-tower-dashboard/issues/142)
* Merge pull request #143 from aplazo/mac-256 ([a677e5b](https://github.com/aplazo/angular.control-tower-dashboard/commit/a677e5b)), closes [#143](https://github.com/aplazo/angular.control-tower-dashboard/issues/143)
* Merge pull request #144 from aplazo/integration/stage ([2766876](https://github.com/aplazo/angular.control-tower-dashboard/commit/2766876)), closes [#144](https://github.com/aplazo/angular.control-tower-dashboard/issues/144)
* Merge pull request #148 from aplazo/mexp-315 ([fb5cb5a](https://github.com/aplazo/angular.control-tower-dashboard/commit/fb5cb5a)), closes [#148](https://github.com/aplazo/angular.control-tower-dashboard/issues/148)
* Merge pull request #149 from aplazo/integration/stage ([a773fcd](https://github.com/aplazo/angular.control-tower-dashboard/commit/a773fcd)), closes [#149](https://github.com/aplazo/angular.control-tower-dashboard/issues/149)
* Merge pull request #15 from aplazo/chore/APM-2200 ([8c82546](https://github.com/aplazo/angular.control-tower-dashboard/commit/8c82546)), closes [#15](https://github.com/aplazo/angular.control-tower-dashboard/issues/15)
* Merge pull request #152 from aplazo/mexp-317 ([10659bb](https://github.com/aplazo/angular.control-tower-dashboard/commit/10659bb)), closes [#152](https://github.com/aplazo/angular.control-tower-dashboard/issues/152)
* Merge pull request #153 from aplazo/mexp-313 ([57f6c57](https://github.com/aplazo/angular.control-tower-dashboard/commit/57f6c57)), closes [#153](https://github.com/aplazo/angular.control-tower-dashboard/issues/153)
* Merge pull request #155 from aplazo/mexp-303 ([28446dc](https://github.com/aplazo/angular.control-tower-dashboard/commit/28446dc)), closes [#155](https://github.com/aplazo/angular.control-tower-dashboard/issues/155)
* Merge pull request #156 from aplazo/mexp-317 ([8362cfa](https://github.com/aplazo/angular.control-tower-dashboard/commit/8362cfa)), closes [#156](https://github.com/aplazo/angular.control-tower-dashboard/issues/156)
* Merge pull request #157 from aplazo/mexp-313 ([cd68fc5](https://github.com/aplazo/angular.control-tower-dashboard/commit/cd68fc5)), closes [#157](https://github.com/aplazo/angular.control-tower-dashboard/issues/157)
* Merge pull request #158 from aplazo/feature/PSPs ([1b993c3](https://github.com/aplazo/angular.control-tower-dashboard/commit/1b993c3)), closes [#158](https://github.com/aplazo/angular.control-tower-dashboard/issues/158)
* Merge pull request #159 from aplazo/feature/PSPs ([7265f21](https://github.com/aplazo/angular.control-tower-dashboard/commit/7265f21)), closes [#159](https://github.com/aplazo/angular.control-tower-dashboard/issues/159)
* Merge pull request #16 from aplazo/chore/APM-2200 ([e821e4b](https://github.com/aplazo/angular.control-tower-dashboard/commit/e821e4b)), closes [#16](https://github.com/aplazo/angular.control-tower-dashboard/issues/16)
* Merge pull request #160 from aplazo/integration/stage ([0d1688a](https://github.com/aplazo/angular.control-tower-dashboard/commit/0d1688a)), closes [#160](https://github.com/aplazo/angular.control-tower-dashboard/issues/160)
* Merge pull request #163 from aplazo/mexp-303 ([141656d](https://github.com/aplazo/angular.control-tower-dashboard/commit/141656d)), closes [#163](https://github.com/aplazo/angular.control-tower-dashboard/issues/163)
* Merge pull request #164 from aplazo/MAC-277 ([82f356d](https://github.com/aplazo/angular.control-tower-dashboard/commit/82f356d)), closes [#164](https://github.com/aplazo/angular.control-tower-dashboard/issues/164)
* Merge pull request #165 from aplazo/MAC-277 ([58e50ce](https://github.com/aplazo/angular.control-tower-dashboard/commit/58e50ce)), closes [#165](https://github.com/aplazo/angular.control-tower-dashboard/issues/165)
* Merge pull request #166 from aplazo/hotfix/removalofSTP ([92b9338](https://github.com/aplazo/angular.control-tower-dashboard/commit/92b9338)), closes [#166](https://github.com/aplazo/angular.control-tower-dashboard/issues/166)
* Merge pull request #167 from aplazo/mexp-303 ([1f31046](https://github.com/aplazo/angular.control-tower-dashboard/commit/1f31046)), closes [#167](https://github.com/aplazo/angular.control-tower-dashboard/issues/167)
* Merge pull request #168 from aplazo/mexp-334 ([08ba2e7](https://github.com/aplazo/angular.control-tower-dashboard/commit/08ba2e7)), closes [#168](https://github.com/aplazo/angular.control-tower-dashboard/issues/168)
* Merge pull request #169 from aplazo/integration/stage ([b3c4071](https://github.com/aplazo/angular.control-tower-dashboard/commit/b3c4071)), closes [#169](https://github.com/aplazo/angular.control-tower-dashboard/issues/169)
* Merge pull request #17 from aplazo/integration/develop ([f6557b9](https://github.com/aplazo/angular.control-tower-dashboard/commit/f6557b9)), closes [#17](https://github.com/aplazo/angular.control-tower-dashboard/issues/17)
* Merge pull request #171 from aplazo/mexp-389 ([f760017](https://github.com/aplazo/angular.control-tower-dashboard/commit/f760017)), closes [#171](https://github.com/aplazo/angular.control-tower-dashboard/issues/171)
* Merge pull request #173 from aplazo/mexp-336 ([0601e0d](https://github.com/aplazo/angular.control-tower-dashboard/commit/0601e0d)), closes [#173](https://github.com/aplazo/angular.control-tower-dashboard/issues/173)
* Merge pull request #179 from aplazo/devops-feature ([1dd7327](https://github.com/aplazo/angular.control-tower-dashboard/commit/1dd7327)), closes [#179](https://github.com/aplazo/angular.control-tower-dashboard/issues/179)
* Merge pull request #18 from aplazo/tests/apm-2200 ([a18a531](https://github.com/aplazo/angular.control-tower-dashboard/commit/a18a531)), closes [#18](https://github.com/aplazo/angular.control-tower-dashboard/issues/18)
* Merge pull request #180 from aplazo/integration/stage ([79c2fa5](https://github.com/aplazo/angular.control-tower-dashboard/commit/79c2fa5)), closes [#180](https://github.com/aplazo/angular.control-tower-dashboard/issues/180)
* Merge pull request #19 from aplazo/chore/headless-tests ([981fe5c](https://github.com/aplazo/angular.control-tower-dashboard/commit/981fe5c)), closes [#19](https://github.com/aplazo/angular.control-tower-dashboard/issues/19)
* Merge pull request #20 from aplazo/apm-2200 ([48b21f0](https://github.com/aplazo/angular.control-tower-dashboard/commit/48b21f0)), closes [#20](https://github.com/aplazo/angular.control-tower-dashboard/issues/20)
* Merge pull request #21 from aplazo/apm-2200 ([59d94da](https://github.com/aplazo/angular.control-tower-dashboard/commit/59d94da)), closes [#21](https://github.com/aplazo/angular.control-tower-dashboard/issues/21)
* Merge pull request #23 from aplazo/integration/develop ([cc99faf](https://github.com/aplazo/angular.control-tower-dashboard/commit/cc99faf)), closes [#23](https://github.com/aplazo/angular.control-tower-dashboard/issues/23)
* Merge pull request #24 from aplazo/integration/develop ([34ea3d6](https://github.com/aplazo/angular.control-tower-dashboard/commit/34ea3d6)), closes [#24](https://github.com/aplazo/angular.control-tower-dashboard/issues/24)
* Merge pull request #25 from aplazo/integration/stage ([b160bb9](https://github.com/aplazo/angular.control-tower-dashboard/commit/b160bb9)), closes [#25](https://github.com/aplazo/angular.control-tower-dashboard/issues/25)
* Merge pull request #27 from aplazo/apm-2297 ([05ba87a](https://github.com/aplazo/angular.control-tower-dashboard/commit/05ba87a)), closes [#27](https://github.com/aplazo/angular.control-tower-dashboard/issues/27)
* Merge pull request #28 from aplazo/integration/develop ([00b5393](https://github.com/aplazo/angular.control-tower-dashboard/commit/00b5393)), closes [#28](https://github.com/aplazo/angular.control-tower-dashboard/issues/28)
* Merge pull request #29 from aplazo/apm-2297 ([54f0a24](https://github.com/aplazo/angular.control-tower-dashboard/commit/54f0a24)), closes [#29](https://github.com/aplazo/angular.control-tower-dashboard/issues/29)
* Merge pull request #3 from aplazo/feat/apm-2129 ([7817c35](https://github.com/aplazo/angular.control-tower-dashboard/commit/7817c35)), closes [#3](https://github.com/aplazo/angular.control-tower-dashboard/issues/3)
* Merge pull request #30 from aplazo/integration/develop ([14d7736](https://github.com/aplazo/angular.control-tower-dashboard/commit/14d7736)), closes [#30](https://github.com/aplazo/angular.control-tower-dashboard/issues/30)
* Merge pull request #31 from aplazo/integration/develop ([c4a297e](https://github.com/aplazo/angular.control-tower-dashboard/commit/c4a297e)), closes [#31](https://github.com/aplazo/angular.control-tower-dashboard/issues/31)
* Merge pull request #32 from aplazo/integration/develop ([559f381](https://github.com/aplazo/angular.control-tower-dashboard/commit/559f381)), closes [#32](https://github.com/aplazo/angular.control-tower-dashboard/issues/32)
* Merge pull request #33 from aplazo/apm-2296 ([2ace1bd](https://github.com/aplazo/angular.control-tower-dashboard/commit/2ace1bd)), closes [#33](https://github.com/aplazo/angular.control-tower-dashboard/issues/33)
* Merge pull request #34 from aplazo/integration/develop ([43d1b06](https://github.com/aplazo/angular.control-tower-dashboard/commit/43d1b06)), closes [#34](https://github.com/aplazo/angular.control-tower-dashboard/issues/34)
* Merge pull request #35 from aplazo/integration/develop ([d230210](https://github.com/aplazo/angular.control-tower-dashboard/commit/d230210)), closes [#35](https://github.com/aplazo/angular.control-tower-dashboard/issues/35)
* Merge pull request #36 from aplazo/apm-2349 ([f79ea82](https://github.com/aplazo/angular.control-tower-dashboard/commit/f79ea82)), closes [#36](https://github.com/aplazo/angular.control-tower-dashboard/issues/36)
* Merge pull request #37 from aplazo/apm-2359 ([6d165bc](https://github.com/aplazo/angular.control-tower-dashboard/commit/6d165bc)), closes [#37](https://github.com/aplazo/angular.control-tower-dashboard/issues/37)
* Merge pull request #38 from aplazo/apm-2360 ([93a64f3](https://github.com/aplazo/angular.control-tower-dashboard/commit/93a64f3)), closes [#38](https://github.com/aplazo/angular.control-tower-dashboard/issues/38)
* Merge pull request #39 from aplazo/integration/develop ([abfb806](https://github.com/aplazo/angular.control-tower-dashboard/commit/abfb806)), closes [#39](https://github.com/aplazo/angular.control-tower-dashboard/issues/39)
* Merge pull request #4 from aplazo/integration/develop ([3fbc1e6](https://github.com/aplazo/angular.control-tower-dashboard/commit/3fbc1e6)), closes [#4](https://github.com/aplazo/angular.control-tower-dashboard/issues/4)
* Merge pull request #40 from aplazo/integration/develop ([ba4e40c](https://github.com/aplazo/angular.control-tower-dashboard/commit/ba4e40c)), closes [#40](https://github.com/aplazo/angular.control-tower-dashboard/issues/40)
* Merge pull request #41 from aplazo/apm-2361 ([27b21df](https://github.com/aplazo/angular.control-tower-dashboard/commit/27b21df)), closes [#41](https://github.com/aplazo/angular.control-tower-dashboard/issues/41)
* Merge pull request #42 from aplazo/integration/develop ([1107b3a](https://github.com/aplazo/angular.control-tower-dashboard/commit/1107b3a)), closes [#42](https://github.com/aplazo/angular.control-tower-dashboard/issues/42)
* Merge pull request #43 from aplazo/integration/develop ([29bba20](https://github.com/aplazo/angular.control-tower-dashboard/commit/29bba20)), closes [#43](https://github.com/aplazo/angular.control-tower-dashboard/issues/43)
* Merge pull request #45 from aplazo/apm-2364 ([353d11b](https://github.com/aplazo/angular.control-tower-dashboard/commit/353d11b)), closes [#45](https://github.com/aplazo/angular.control-tower-dashboard/issues/45)
* Merge pull request #46 from aplazo/integration/develop ([3a85f1d](https://github.com/aplazo/angular.control-tower-dashboard/commit/3a85f1d)), closes [#46](https://github.com/aplazo/angular.control-tower-dashboard/issues/46)
* Merge pull request #47 from aplazo/apm-2363 ([1cd9667](https://github.com/aplazo/angular.control-tower-dashboard/commit/1cd9667)), closes [#47](https://github.com/aplazo/angular.control-tower-dashboard/issues/47)
* Merge pull request #48 from aplazo/integration/develop ([24424a9](https://github.com/aplazo/angular.control-tower-dashboard/commit/24424a9)), closes [#48](https://github.com/aplazo/angular.control-tower-dashboard/issues/48)
* Merge pull request #49 from aplazo/integration/develop ([7899ac8](https://github.com/aplazo/angular.control-tower-dashboard/commit/7899ac8)), closes [#49](https://github.com/aplazo/angular.control-tower-dashboard/issues/49)
* Merge pull request #5 from aplazo/integration/develop ([8fb450b](https://github.com/aplazo/angular.control-tower-dashboard/commit/8fb450b)), closes [#5](https://github.com/aplazo/angular.control-tower-dashboard/issues/5)
* Merge pull request #50 from aplazo/apm-2362 ([5f600dd](https://github.com/aplazo/angular.control-tower-dashboard/commit/5f600dd)), closes [#50](https://github.com/aplazo/angular.control-tower-dashboard/issues/50)
* Merge pull request #51 from aplazo/integration/develop ([51f04ae](https://github.com/aplazo/angular.control-tower-dashboard/commit/51f04ae)), closes [#51](https://github.com/aplazo/angular.control-tower-dashboard/issues/51)
* Merge pull request #52 from aplazo/apm-2365 ([d5dfda6](https://github.com/aplazo/angular.control-tower-dashboard/commit/d5dfda6)), closes [#52](https://github.com/aplazo/angular.control-tower-dashboard/issues/52)
* Merge pull request #53 from aplazo/integration/develop ([89e0c27](https://github.com/aplazo/angular.control-tower-dashboard/commit/89e0c27)), closes [#53](https://github.com/aplazo/angular.control-tower-dashboard/issues/53)
* Merge pull request #54 from aplazo/apm-2366 ([58613c8](https://github.com/aplazo/angular.control-tower-dashboard/commit/58613c8)), closes [#54](https://github.com/aplazo/angular.control-tower-dashboard/issues/54)
* Merge pull request #55 from aplazo/integration/develop ([4f46f30](https://github.com/aplazo/angular.control-tower-dashboard/commit/4f46f30)), closes [#55](https://github.com/aplazo/angular.control-tower-dashboard/issues/55)
* Merge pull request #56 from aplazo/integration/stage ([8b3c7fa](https://github.com/aplazo/angular.control-tower-dashboard/commit/8b3c7fa)), closes [#56](https://github.com/aplazo/angular.control-tower-dashboard/issues/56)
* Merge pull request #57 from aplazo/integration/develop ([5c7d4b9](https://github.com/aplazo/angular.control-tower-dashboard/commit/5c7d4b9)), closes [#57](https://github.com/aplazo/angular.control-tower-dashboard/issues/57)
* Merge pull request #58 from aplazo/apm-2465 ([6c6ac83](https://github.com/aplazo/angular.control-tower-dashboard/commit/6c6ac83)), closes [#58](https://github.com/aplazo/angular.control-tower-dashboard/issues/58)
* Merge pull request #59 from aplazo/integration/develop ([fa8efef](https://github.com/aplazo/angular.control-tower-dashboard/commit/fa8efef)), closes [#59](https://github.com/aplazo/angular.control-tower-dashboard/issues/59)
* Merge pull request #6 from aplazo/integration/stage ([a18a935](https://github.com/aplazo/angular.control-tower-dashboard/commit/a18a935)), closes [#6](https://github.com/aplazo/angular.control-tower-dashboard/issues/6)
* Merge pull request #60 from aplazo/integration/develop ([e59afd6](https://github.com/aplazo/angular.control-tower-dashboard/commit/e59afd6)), closes [#60](https://github.com/aplazo/angular.control-tower-dashboard/issues/60)
* Merge pull request #61 from aplazo/integration/develop ([387c6da](https://github.com/aplazo/angular.control-tower-dashboard/commit/387c6da)), closes [#61](https://github.com/aplazo/angular.control-tower-dashboard/issues/61)
* Merge pull request #62 from aplazo/apm-2518 ([a5c8fc8](https://github.com/aplazo/angular.control-tower-dashboard/commit/a5c8fc8)), closes [#62](https://github.com/aplazo/angular.control-tower-dashboard/issues/62)
* Merge pull request #63 from aplazo/integration/develop ([832fe74](https://github.com/aplazo/angular.control-tower-dashboard/commit/832fe74)), closes [#63](https://github.com/aplazo/angular.control-tower-dashboard/issues/63)
* Merge pull request #64 from aplazo/integration/develop ([1bd94ed](https://github.com/aplazo/angular.control-tower-dashboard/commit/1bd94ed)), closes [#64](https://github.com/aplazo/angular.control-tower-dashboard/issues/64)
* Merge pull request #65 from aplazo/integration/develop ([5f9bccd](https://github.com/aplazo/angular.control-tower-dashboard/commit/5f9bccd)), closes [#65](https://github.com/aplazo/angular.control-tower-dashboard/issues/65)
* Merge pull request #66 from aplazo/integration/stage ([e0fd599](https://github.com/aplazo/angular.control-tower-dashboard/commit/e0fd599)), closes [#66](https://github.com/aplazo/angular.control-tower-dashboard/issues/66)
* Merge pull request #67 from aplazo/apm-2569 ([bfe7e50](https://github.com/aplazo/angular.control-tower-dashboard/commit/bfe7e50)), closes [#67](https://github.com/aplazo/angular.control-tower-dashboard/issues/67)
* Merge pull request #68 from aplazo/integration/develop ([0d9c630](https://github.com/aplazo/angular.control-tower-dashboard/commit/0d9c630)), closes [#68](https://github.com/aplazo/angular.control-tower-dashboard/issues/68)
* Merge pull request #69 from aplazo/integration/develop ([c5c0c97](https://github.com/aplazo/angular.control-tower-dashboard/commit/c5c0c97)), closes [#69](https://github.com/aplazo/angular.control-tower-dashboard/issues/69)
* Merge pull request #7 from aplazo/integration/develop ([8afd5d0](https://github.com/aplazo/angular.control-tower-dashboard/commit/8afd5d0)), closes [#7](https://github.com/aplazo/angular.control-tower-dashboard/issues/7)
* Merge pull request #70 from aplazo/integration/develop ([a2dc9b8](https://github.com/aplazo/angular.control-tower-dashboard/commit/a2dc9b8)), closes [#70](https://github.com/aplazo/angular.control-tower-dashboard/issues/70)
* Merge pull request #72 from aplazo/apm-2574 ([5941f3e](https://github.com/aplazo/angular.control-tower-dashboard/commit/5941f3e)), closes [#72](https://github.com/aplazo/angular.control-tower-dashboard/issues/72)
* Merge pull request #73 from aplazo/apm-2574 ([47a9019](https://github.com/aplazo/angular.control-tower-dashboard/commit/47a9019)), closes [#73](https://github.com/aplazo/angular.control-tower-dashboard/issues/73)
* Merge pull request #74 from aplazo/integration/develop ([2406757](https://github.com/aplazo/angular.control-tower-dashboard/commit/2406757)), closes [#74](https://github.com/aplazo/angular.control-tower-dashboard/issues/74)
* Merge pull request #75 from aplazo/integration/develop ([923fa6d](https://github.com/aplazo/angular.control-tower-dashboard/commit/923fa6d)), closes [#75](https://github.com/aplazo/angular.control-tower-dashboard/issues/75)
* Merge pull request #76 from aplazo/apm-2571 ([6561fa9](https://github.com/aplazo/angular.control-tower-dashboard/commit/6561fa9)), closes [#76](https://github.com/aplazo/angular.control-tower-dashboard/issues/76)
* Merge pull request #77 from aplazo/integration/develop ([2260ce5](https://github.com/aplazo/angular.control-tower-dashboard/commit/2260ce5)), closes [#77](https://github.com/aplazo/angular.control-tower-dashboard/issues/77)
* Merge pull request #78 from aplazo/apm-2572 ([433c19b](https://github.com/aplazo/angular.control-tower-dashboard/commit/433c19b)), closes [#78](https://github.com/aplazo/angular.control-tower-dashboard/issues/78)
* Merge pull request #79 from aplazo/chore/release-apm-2656 ([6f618dc](https://github.com/aplazo/angular.control-tower-dashboard/commit/6f618dc)), closes [#79](https://github.com/aplazo/angular.control-tower-dashboard/issues/79)
* Merge pull request #80 from aplazo/integration/develop ([df54c46](https://github.com/aplazo/angular.control-tower-dashboard/commit/df54c46)), closes [#80](https://github.com/aplazo/angular.control-tower-dashboard/issues/80)
* Merge pull request #81 from aplazo/apm-2571 ([88f8d00](https://github.com/aplazo/angular.control-tower-dashboard/commit/88f8d00)), closes [#81](https://github.com/aplazo/angular.control-tower-dashboard/issues/81)
* Merge pull request #82 from aplazo/integration/develop ([90ef0bc](https://github.com/aplazo/angular.control-tower-dashboard/commit/90ef0bc)), closes [#82](https://github.com/aplazo/angular.control-tower-dashboard/issues/82)
* Merge pull request #83 from aplazo/apm-2573 ([42c5854](https://github.com/aplazo/angular.control-tower-dashboard/commit/42c5854)), closes [#83](https://github.com/aplazo/angular.control-tower-dashboard/issues/83)
* Merge pull request #84 from aplazo/integration/develop ([0c1a2ed](https://github.com/aplazo/angular.control-tower-dashboard/commit/0c1a2ed)), closes [#84](https://github.com/aplazo/angular.control-tower-dashboard/issues/84)
* Merge pull request #85 from aplazo/integration/develop ([aa951d1](https://github.com/aplazo/angular.control-tower-dashboard/commit/aa951d1)), closes [#85](https://github.com/aplazo/angular.control-tower-dashboard/issues/85)
* Merge pull request #86 from aplazo/integration/develop ([e0780b5](https://github.com/aplazo/angular.control-tower-dashboard/commit/e0780b5)), closes [#86](https://github.com/aplazo/angular.control-tower-dashboard/issues/86)
* Merge pull request #87 from aplazo/apm-2575 ([171405d](https://github.com/aplazo/angular.control-tower-dashboard/commit/171405d)), closes [#87](https://github.com/aplazo/angular.control-tower-dashboard/issues/87)
* Merge pull request #88 from aplazo/integration/develop ([8dd6052](https://github.com/aplazo/angular.control-tower-dashboard/commit/8dd6052)), closes [#88](https://github.com/aplazo/angular.control-tower-dashboard/issues/88)
* Merge pull request #89 from aplazo/apm-2701 ([2b009f4](https://github.com/aplazo/angular.control-tower-dashboard/commit/2b009f4)), closes [#89](https://github.com/aplazo/angular.control-tower-dashboard/issues/89)
* Merge pull request #90 from aplazo/feature/apm-2872 ([6cb12b7](https://github.com/aplazo/angular.control-tower-dashboard/commit/6cb12b7)), closes [#90](https://github.com/aplazo/angular.control-tower-dashboard/issues/90)
* Merge pull request #91 from aplazo/integration/develop ([cf3472e](https://github.com/aplazo/angular.control-tower-dashboard/commit/cf3472e)), closes [#91](https://github.com/aplazo/angular.control-tower-dashboard/issues/91)
* Merge pull request #92 from aplazo/feature/apm-2872 ([25a0187](https://github.com/aplazo/angular.control-tower-dashboard/commit/25a0187)), closes [#92](https://github.com/aplazo/angular.control-tower-dashboard/issues/92)
* Merge pull request #93 from aplazo/feature/apm-2872 ([54c79c5](https://github.com/aplazo/angular.control-tower-dashboard/commit/54c79c5)), closes [#93](https://github.com/aplazo/angular.control-tower-dashboard/issues/93)
* Merge pull request #95 from aplazo/integration/develop ([9533e50](https://github.com/aplazo/angular.control-tower-dashboard/commit/9533e50)), closes [#95](https://github.com/aplazo/angular.control-tower-dashboard/issues/95)
* Merge pull request #96 from aplazo/integration/stage ([9be3b1c](https://github.com/aplazo/angular.control-tower-dashboard/commit/9be3b1c)), closes [#96](https://github.com/aplazo/angular.control-tower-dashboard/issues/96)
* Merge pull request #97 from aplazo/apm-2701 ([9d7ff45](https://github.com/aplazo/angular.control-tower-dashboard/commit/9d7ff45)), closes [#97](https://github.com/aplazo/angular.control-tower-dashboard/issues/97)
* Merge pull request #98 from aplazo/integration/develop ([3afc449](https://github.com/aplazo/angular.control-tower-dashboard/commit/3afc449)), closes [#98](https://github.com/aplazo/angular.control-tower-dashboard/issues/98)
* Merge pull request #99 from aplazo/integration/develop ([e382a08](https://github.com/aplazo/angular.control-tower-dashboard/commit/e382a08)), closes [#99](https://github.com/aplazo/angular.control-tower-dashboard/issues/99)
* Modified auth declaration to use only the interceptor token ([daf91ed](https://github.com/aplazo/angular.control-tower-dashboard/commit/daf91ed))
* modified styling ([501e72e](https://github.com/aplazo/angular.control-tower-dashboard/commit/501e72e))
* remove class unused in receipts.component.ts ([27069c4](https://github.com/aplazo/angular.control-tower-dashboard/commit/27069c4))
* removed  stg token ([c7c9d39](https://github.com/aplazo/angular.control-tower-dashboard/commit/c7c9d39))
* Removed alert message and used a toaster notification ([38723a4](https://github.com/aplazo/angular.control-tower-dashboard/commit/38723a4))
* removed comment ([a38fa6f](https://github.com/aplazo/angular.control-tower-dashboard/commit/a38fa6f))
* removed comments ([f866bc4](https://github.com/aplazo/angular.control-tower-dashboard/commit/f866bc4))
* Removed STP as part of the providers ([a175371](https://github.com/aplazo/angular.control-tower-dashboard/commit/a175371))
* Servicio y funcionalidad para visualizar nueva sección de ordenamiento de proveedores de pago ([87392fe](https://github.com/aplazo/angular.control-tower-dashboard/commit/87392fe))
* Update environment.stage.ts ([e3f9121](https://github.com/aplazo/angular.control-tower-dashboard/commit/e3f9121))
* Update Jenkinsfile ([3263eb4](https://github.com/aplazo/angular.control-tower-dashboard/commit/3263eb4))
* update test ([d2e17e9](https://github.com/aplazo/angular.control-tower-dashboard/commit/d2e17e9))
* update test for receiptsUsecase ([b819a0b](https://github.com/aplazo/angular.control-tower-dashboard/commit/b819a0b))
* update usecase receipts ([d8ae339](https://github.com/aplazo/angular.control-tower-dashboard/commit/d8ae339))
* Updated button press logic and change detection, added new service to call a new endpoint ([3db298a](https://github.com/aplazo/angular.control-tower-dashboard/commit/3db298a))
* Updated code to display the current order ([3ed2d64](https://github.com/aplazo/angular.control-tower-dashboard/commit/3ed2d64))
* updated notifier ([ebe66cb](https://github.com/aplazo/angular.control-tower-dashboard/commit/ebe66cb))
* Updated provider order and enabled selected ([7c11ed2](https://github.com/aplazo/angular.control-tower-dashboard/commit/7c11ed2))
* fix: apm-2130 remove unused dynamic pipe ([811d542](https://github.com/aplazo/angular.control-tower-dashboard/commit/811d542))
* fix: apm-2296 handle already registered email error ([7bd6be1](https://github.com/aplazo/angular.control-tower-dashboard/commit/7bd6be1))
* fix: apm-2296 handle reset at the end of merchant creation ([bd8aaa7](https://github.com/aplazo/angular.control-tower-dashboard/commit/bd8aaa7))
* fix: apm-2296 merchant request contract ([51a1e75](https://github.com/aplazo/angular.control-tower-dashboard/commit/51a1e75))
* fix: apm-2297 add providers to correct layer ([424cd54](https://github.com/aplazo/angular.control-tower-dashboard/commit/424cd54))
* fix: apm-2297 add selectors to prevent collitions ([8b77485](https://github.com/aplazo/angular.control-tower-dashboard/commit/8b77485))
* fix: apm-2465 select instead radio for refund new status ([17946d3](https://github.com/aplazo/angular.control-tower-dashboard/commit/17946d3))
* fix: apm-2518 dynamic multifiles ([910a459](https://github.com/aplazo/angular.control-tower-dashboard/commit/910a459))
* fix: apm-2518 fix routing ([e8d63a1](https://github.com/aplazo/angular.control-tower-dashboard/commit/e8d63a1))
* fix: apm-2569 empty edited fields ([6d359d8](https://github.com/aplazo/angular.control-tower-dashboard/commit/6d359d8))
* fix: apm-2571 generals edition labels ([184dc05](https://github.com/aplazo/angular.control-tower-dashboard/commit/184dc05))
* fix: apm-2572 validate clabe 18 digits ([b31485b](https://github.com/aplazo/angular.control-tower-dashboard/commit/b31485b))
* fix: apm-2573 cfdi parsing values ([00193b6](https://github.com/aplazo/angular.control-tower-dashboard/commit/00193b6))
* fix: apm-2574 hide component on changes ([3639a94](https://github.com/aplazo/angular.control-tower-dashboard/commit/3639a94))
* fix: apm-2574 update branches from store ([9688b75](https://github.com/aplazo/angular.control-tower-dashboard/commit/9688b75))
* fix: apm-2574 update label ([0ff0c37](https://github.com/aplazo/angular.control-tower-dashboard/commit/0ff0c37))
* fix: apm-2575 mdr incorrect comparation ([3117b94](https://github.com/aplazo/angular.control-tower-dashboard/commit/3117b94))
* fix: apm-2701 only allowed roles ([7b9193c](https://github.com/aplazo/angular.control-tower-dashboard/commit/7b9193c))
* fix: apm-2811 fix roles to invoice generation ([0cec469](https://github.com/aplazo/angular.control-tower-dashboard/commit/0cec469))
* fix: apm-2811 generate invoice icon ([a3bd931](https://github.com/aplazo/angular.control-tower-dashboard/commit/a3bd931))
* fix: apm-2892 billing save store bug ([b57dce9](https://github.com/aplazo/angular.control-tower-dashboard/commit/b57dce9))
* fix: apm-2892 payment form fields ([ab773e3](https://github.com/aplazo/angular.control-tower-dashboard/commit/ab773e3))
* fix: apm-2892 payment form fields ([055c2b2](https://github.com/aplazo/angular.control-tower-dashboard/commit/055c2b2))
* fix: apm-2971 create user ([343228f](https://github.com/aplazo/angular.control-tower-dashboard/commit/343228f))
* fix: client id environment ([1cce72a](https://github.com/aplazo/angular.control-tower-dashboard/commit/1cce72a))
* fix: environment from inject ([a402e5f](https://github.com/aplazo/angular.control-tower-dashboard/commit/a402e5f))
* fix: Initialize tycControl with existing termsConditions data ([5eefe2c](https://github.com/aplazo/angular.control-tower-dashboard/commit/5eefe2c))
* fix: inject env vars ([f32e003](https://github.com/aplazo/angular.control-tower-dashboard/commit/f32e003))
* fix: jenkinks node version ([65f5949](https://github.com/aplazo/angular.control-tower-dashboard/commit/65f5949))
* fix: loader aftercontentcheck error ([5940484](https://github.com/aplazo/angular.control-tower-dashboard/commit/5940484))
* fix: mac-140 label for basic merchant staus label ([0430ad6](https://github.com/aplazo/angular.control-tower-dashboard/commit/0430ad6))
* fix: mac-256 remove cache for account and billing ([ad239ab](https://github.com/aplazo/angular.control-tower-dashboard/commit/ad239ab))
* fix: mac-63 dependencies ([57e4c40](https://github.com/aplazo/angular.control-tower-dashboard/commit/57e4c40))
* fix: mac-63 dependencies ([e0f9f83](https://github.com/aplazo/angular.control-tower-dashboard/commit/e0f9f83))
* fix: mac-63 option select for empty branches ([9f466e0](https://github.com/aplazo/angular.control-tower-dashboard/commit/9f466e0))
* fix: mac-63 optional email ([7d3ff2c](https://github.com/aplazo/angular.control-tower-dashboard/commit/7d3ff2c))
* fix: mac-88 active/deactive operator ([bc149bb](https://github.com/aplazo/angular.control-tower-dashboard/commit/bc149bb))
* fix: mdr disable edition on success ([df9c8ea](https://github.com/aplazo/angular.control-tower-dashboard/commit/df9c8ea))
* fix: mexp-188 datepicker border overlap ([3ebbb3b](https://github.com/aplazo/angular.control-tower-dashboard/commit/3ebbb3b))
* fix: mexp-201 Update campaign form TyC URL example to use .mx domain ([da982dd](https://github.com/aplazo/angular.control-tower-dashboard/commit/da982dd))
* fix: mexp-208 first page ([c6c1cd2](https://github.com/aplazo/angular.control-tower-dashboard/commit/c6c1cd2))
* fix: mexp-208 fixes for pr approval ([03dc76e](https://github.com/aplazo/angular.control-tower-dashboard/commit/03dc76e))
* fix: mexp-209 listing campaigns default date ([5a4fb83](https://github.com/aplazo/angular.control-tower-dashboard/commit/5a4fb83))
* fix: mexp-317 update campaign args ([be43947](https://github.com/aplazo/angular.control-tower-dashboard/commit/be43947))
* fix: mexp-334 Enhance Jenkins pipeline environment variable handling ([0e39c0e](https://github.com/aplazo/angular.control-tower-dashboard/commit/0e39c0e))
* fix: mexp-334 Modify Docker Compose test execution in Jenkins pipeline ([d1d7b79](https://github.com/aplazo/angular.control-tower-dashboard/commit/d1d7b79))
* fix: mexp-334 optimize Jenkins pipeline secret retrieval ([d56cbad](https://github.com/aplazo/angular.control-tower-dashboard/commit/d56cbad))
* fix: mexp-334 Refactor E2E test execution in Jenkins pipeline ([1acdf95](https://github.com/aplazo/angular.control-tower-dashboard/commit/1acdf95))
* fix: mexp-334 Refactor test configuration and Docker setup ([5fa4ac3](https://github.com/aplazo/angular.control-tower-dashboard/commit/5fa4ac3))
* fix: mexp-334 Simplify Playwright browser installation ([cc1b271](https://github.com/aplazo/angular.control-tower-dashboard/commit/cc1b271))
* fix: mexp-334 Simplify Playwright installation in Jenkins pipeline ([aec331d](https://github.com/aplazo/angular.control-tower-dashboard/commit/aec331d))
* fix: mexp-334 Update Docker Compose command syntax in Jenkins pipeline ([1bdd305](https://github.com/aplazo/angular.control-tower-dashboard/commit/1bdd305))
* fix: mexp-334 Update Jenkins pipeline test command ([af4a662](https://github.com/aplazo/angular.control-tower-dashboard/commit/af4a662))
* fix: mexp-334 Update Karma and Jenkins configurations for improved test environment ([51fd48a](https://github.com/aplazo/angular.control-tower-dashboard/commit/51fd48a))
* fix: mexp-334 Update Playwright installation command in Jenkins pipeline ([d33876f](https://github.com/aplazo/angular.control-tower-dashboard/commit/d33876f))
* fix: mexp-77 datepicker input key ([4225969](https://github.com/aplazo/angular.control-tower-dashboard/commit/4225969))
* fix: mexp-77 form datepicker styles and click outside ([2622b6f](https://github.com/aplazo/angular.control-tower-dashboard/commit/2622b6f))
* fix: mexp-77 prevent default ([1aa7b95](https://github.com/aplazo/angular.control-tower-dashboard/commit/1aa7b95))
* fix: remove comments ([59c7187](https://github.com/aplazo/angular.control-tower-dashboard/commit/59c7187))
* fix: response headers ([0611c27](https://github.com/aplazo/angular.control-tower-dashboard/commit/0611c27))
* fix: set public endpoints to dev env ([641dd15](https://github.com/aplazo/angular.control-tower-dashboard/commit/641dd15))
* fix: styles and dependencies ([92dcdd1](https://github.com/aplazo/angular.control-tower-dashboard/commit/92dcdd1))
* fix: unhandled null pointer ([8ced14d](https://github.com/aplazo/angular.control-tower-dashboard/commit/8ced14d))
* fix: unhandled null pointer ([9434ebe](https://github.com/aplazo/angular.control-tower-dashboard/commit/9434ebe))
* fix(docker): mexp-336 Enhance coverage directory permissions in test environment ([0a35cd0](https://github.com/aplazo/angular.control-tower-dashboard/commit/0a35cd0))
* fix(jenkins): mexp-336  Optimize unit test Docker workflow ([d8f8efb](https://github.com/aplazo/angular.control-tower-dashboard/commit/d8f8efb))
* fix(jenkins): mexp-336  Securely retrieve GitHub token from AWS Secrets Manager ([da49c93](https://github.com/aplazo/angular.control-tower-dashboard/commit/da49c93))
* fix(jenkins): mexp-336 Escape GitHub token variable in git remote URL ([5e8640f](https://github.com/aplazo/angular.control-tower-dashboard/commit/5e8640f))
* fix(jenkins): mexp-336 optimize package version extraction script ([dd2ff08](https://github.com/aplazo/angular.control-tower-dashboard/commit/dd2ff08))
* fix(jenkins): mexp-336 optimize package version extraction using Node.js ([954c874](https://github.com/aplazo/angular.control-tower-dashboard/commit/954c874))
* fix(jenkins): mexp-336 Update AWS Secrets Manager secret ID for GitHub token retrieval ([b3166e9](https://github.com/aplazo/angular.control-tower-dashboard/commit/b3166e9))
* fix(jenkins): mexp-336 update deployment pipeline trigger and npm installation ([335ebdb](https://github.com/aplazo/angular.control-tower-dashboard/commit/335ebdb))
* fix(login): mexp-336 Add null check for email validation ([dbc1046](https://github.com/aplazo/angular.control-tower-dashboard/commit/dbc1046))
* fix(shared-ui): apm-2575 mdr handling ui errrors ([7a02c6e](https://github.com/aplazo/angular.control-tower-dashboard/commit/7a02c6e))
* fix(shared-ui): mac-63 multiple branches ([85706c8](https://github.com/aplazo/angular.control-tower-dashboard/commit/85706c8))
* fix(tests): mexp-336 update Dockerfile and TypeScript configuration ([ff789d3](https://github.com/aplazo/angular.control-tower-dashboard/commit/ff789d3))
* chore:  mexp-336 add semantic-release configuration dependencies ([0744e8c](https://github.com/aplazo/angular.control-tower-dashboard/commit/0744e8c))
* chore: add api base url within env file ([cf8bd41](https://github.com/aplazo/angular.control-tower-dashboard/commit/cf8bd41))
* chore: add api base url within env file ([f49c74e](https://github.com/aplazo/angular.control-tower-dashboard/commit/f49c74e))
* chore: add dependencies ([6c7f009](https://github.com/aplazo/angular.control-tower-dashboard/commit/6c7f009))
* chore: add eslint prettier gitignore ([67e8e3c](https://github.com/aplazo/angular.control-tower-dashboard/commit/67e8e3c))
* chore: add landing url to environments ([e36c2a2](https://github.com/aplazo/angular.control-tower-dashboard/commit/e36c2a2))
* chore: add nx to gitignore ([2588636](https://github.com/aplazo/angular.control-tower-dashboard/commit/2588636))
* chore: add stage environment ([d0a678a](https://github.com/aplazo/angular.control-tower-dashboard/commit/d0a678a))
* chore: add stage environment ([f1b2106](https://github.com/aplazo/angular.control-tower-dashboard/commit/f1b2106))
* chore: add tailwind ([db9a2c5](https://github.com/aplazo/angular.control-tower-dashboard/commit/db9a2c5))
* chore: add tools within jenkinsfile ([e8f5a6a](https://github.com/aplazo/angular.control-tower-dashboard/commit/e8f5a6a))
* chore: apm-2130 update dependencies ([64629ca](https://github.com/aplazo/angular.control-tower-dashboard/commit/64629ca))
* chore: apm-2200 activate wait for quality check ([9d0737f](https://github.com/aplazo/angular.control-tower-dashboard/commit/9d0737f))
* chore: apm-2200 add app component tests ([5b7665e](https://github.com/aplazo/angular.control-tower-dashboard/commit/5b7665e))
* chore: apm-2200 add coverage to sonar ([422da06](https://github.com/aplazo/angular.control-tower-dashboard/commit/422da06))
* chore: apm-2200 add user to docker run cmd ([e8542ec](https://github.com/aplazo/angular.control-tower-dashboard/commit/e8542ec))
* chore: apm-2200 change from puppeteer to playwright ([3271e89](https://github.com/aplazo/angular.control-tower-dashboard/commit/3271e89))
* chore: apm-2200 escape dollar signs ([f01e05e](https://github.com/aplazo/angular.control-tower-dashboard/commit/f01e05e))
* chore: apm-2200 fix add dev dependencies ([6037e3a](https://github.com/aplazo/angular.control-tower-dashboard/commit/6037e3a))
* chore: apm-2200 fix docker folder clone ([dbad329](https://github.com/aplazo/angular.control-tower-dashboard/commit/dbad329))
* chore: apm-2200 fix dockerfile.tests ([27653e7](https://github.com/aplazo/angular.control-tower-dashboard/commit/27653e7))
* chore: apm-2200 fix jenkins docker run cmd ([6a42ac3](https://github.com/aplazo/angular.control-tower-dashboard/commit/6a42ac3))
* chore: apm-2200 fix jenkins docker run cmd ([de976b6](https://github.com/aplazo/angular.control-tower-dashboard/commit/de976b6))
* chore: apm-2200 fix test cmd ([92a0fd9](https://github.com/aplazo/angular.control-tower-dashboard/commit/92a0fd9))
* chore: apm-2200 improve app component test ([3008d29](https://github.com/aplazo/angular.control-tower-dashboard/commit/3008d29))
* chore: apm-2200 install chrome to headless ([cc95ca3](https://github.com/aplazo/angular.control-tower-dashboard/commit/cc95ca3))
* chore: apm-2200 reorder board files ([8dcb10b](https://github.com/aplazo/angular.control-tower-dashboard/commit/8dcb10b))
* chore: apm-2200 split quality gate check ([e4fb5b9](https://github.com/aplazo/angular.control-tower-dashboard/commit/e4fb5b9))
* chore: apm-2200 trying sleep to retrieve qualitycheck ([898d87a](https://github.com/aplazo/angular.control-tower-dashboard/commit/898d87a))
* chore: apm-2296 change sidebar menu copies ([2e631ff](https://github.com/aplazo/angular.control-tower-dashboard/commit/2e631ff))
* chore: apm-2296 disabled teardown components in tests ([2452470](https://github.com/aplazo/angular.control-tower-dashboard/commit/2452470))
* chore: apm-2296 update dependencies ([45248c5](https://github.com/aplazo/angular.control-tower-dashboard/commit/45248c5))
* chore: apm-2297 remove unnecessary provider ([f91eb8f](https://github.com/aplazo/angular.control-tower-dashboard/commit/f91eb8f))
* chore: apm-2297 update dependencies ([420a739](https://github.com/aplazo/angular.control-tower-dashboard/commit/420a739))
* chore: apm-2349 add ngx mask as dep ([540efe1](https://github.com/aplazo/angular.control-tower-dashboard/commit/540efe1))
* chore: apm-2572 cache control ([a4a6c28](https://github.com/aplazo/angular.control-tower-dashboard/commit/a4a6c28))
* chore: apm-2701 merge master into dev ([b107fd0](https://github.com/aplazo/angular.control-tower-dashboard/commit/b107fd0))
* chore: apm-2701 revert upsert user ([97ed936](https://github.com/aplazo/angular.control-tower-dashboard/commit/97ed936))
* chore: bump Aplazo package dependencies to version 2.11.3 ([54d3d08](https://github.com/aplazo/angular.control-tower-dashboard/commit/54d3d08))
* chore: change approach to running tests ([e3f4687](https://github.com/aplazo/angular.control-tower-dashboard/commit/e3f4687))
* chore: change home folder reference ([f80c0ce](https://github.com/aplazo/angular.control-tower-dashboard/commit/f80c0ce))
* chore: change npmrc config reference ([0c96864](https://github.com/aplazo/angular.control-tower-dashboard/commit/0c96864))
* chore: change npmrc config reference ([1645f69](https://github.com/aplazo/angular.control-tower-dashboard/commit/1645f69))
* chore: change npmrc config reference ([e17696d](https://github.com/aplazo/angular.control-tower-dashboard/commit/e17696d))
* chore: configure ESLint ignore and update Karma coverage reporter ([a865252](https://github.com/aplazo/angular.control-tower-dashboard/commit/a865252))
* chore: debug ([55ba8fb](https://github.com/aplazo/angular.control-tower-dashboard/commit/55ba8fb))
* chore: decrease font size ([f3c71e4](https://github.com/aplazo/angular.control-tower-dashboard/commit/f3c71e4))
* chore: fix docker run command ([1e7ad1a](https://github.com/aplazo/angular.control-tower-dashboard/commit/1e7ad1a))
* chore: fix npmrc copy destiny ([8e232cf](https://github.com/aplazo/angular.control-tower-dashboard/commit/8e232cf))
* chore: headless browser ([22da815](https://github.com/aplazo/angular.control-tower-dashboard/commit/22da815))
* chore: improve jenkins ([778513c](https://github.com/aplazo/angular.control-tower-dashboard/commit/778513c))
* chore: improve jenkins ([6690ae7](https://github.com/aplazo/angular.control-tower-dashboard/commit/6690ae7))
* chore: log ([c3f51f9](https://github.com/aplazo/angular.control-tower-dashboard/commit/c3f51f9))
* chore: log ([9cc3fb3](https://github.com/aplazo/angular.control-tower-dashboard/commit/9cc3fb3))
* chore: log ([fff3a5d](https://github.com/aplazo/angular.control-tower-dashboard/commit/fff3a5d))
* chore: log ([c2af5c7](https://github.com/aplazo/angular.control-tower-dashboard/commit/c2af5c7))
* chore: mac-277 add more options in dropdowns ([d9a85c4](https://github.com/aplazo/angular.control-tower-dashboard/commit/d9a85c4))
* chore: mac-277 adjust missing property in  get-invoice-with-http.repository.spec.ts ([0e62df6](https://github.com/aplazo/angular.control-tower-dashboard/commit/0e62df6))
* chore: mac-63 lint improved ([e14a549](https://github.com/aplazo/angular.control-tower-dashboard/commit/e14a549))
* chore: mac-63 merge stage into feat ([ae2b0b2](https://github.com/aplazo/angular.control-tower-dashboard/commit/ae2b0b2))
* chore: mac-87 change regime data ([419112a](https://github.com/aplazo/angular.control-tower-dashboard/commit/419112a))
* chore: merge master ([330001e](https://github.com/aplazo/angular.control-tower-dashboard/commit/330001e))
* chore: mexp-209 change edit label ([e2cb4d5](https://github.com/aplazo/angular.control-tower-dashboard/commit/e2cb4d5))
* chore: mexp-303 number of winners field added ([bd8c6c9](https://github.com/aplazo/angular.control-tower-dashboard/commit/bd8c6c9))
* chore: mexp-303 number of winners field added ([e6c006b](https://github.com/aplazo/angular.control-tower-dashboard/commit/e6c006b))
* chore: mexp-303 number of winners field added ([0e4f932](https://github.com/aplazo/angular.control-tower-dashboard/commit/0e4f932))
* chore: mexp-303 numberOfWinners property added in create campaign ([f387ff6](https://github.com/aplazo/angular.control-tower-dashboard/commit/f387ff6))
* chore: mexp-303 numberOfWinners property added in create campaign ([99c6b60](https://github.com/aplazo/angular.control-tower-dashboard/commit/99c6b60))
* chore: mexp-303 numberOfWinners property added in update campaign ([0b4a969](https://github.com/aplazo/angular.control-tower-dashboard/commit/0b4a969))
* chore: mexp-303 restore .cursorrules file ([673cfb6](https://github.com/aplazo/angular.control-tower-dashboard/commit/673cfb6))
* chore: mexp-334 add commit-and-tag-version for automated versioning ([6da167f](https://github.com/aplazo/angular.control-tower-dashboard/commit/6da167f))
* chore: mexp-336 add commitlint and husky for commit message validation ([ea27310](https://github.com/aplazo/angular.control-tower-dashboard/commit/ea27310))
* chore: mexp-336 add semantic-release configuration file ([6d0a124](https://github.com/aplazo/angular.control-tower-dashboard/commit/6d0a124))
* chore: mexp-336 simplify build configuration and remove environment-specific scripts ([18b5460](https://github.com/aplazo/angular.control-tower-dashboard/commit/18b5460))
* chore: mexp-389 add pull reques template ([2746888](https://github.com/aplazo/angular.control-tower-dashboard/commit/2746888))
* chore: prepare initial scafolding ([ae4ea55](https://github.com/aplazo/angular.control-tower-dashboard/commit/ae4ea55))
* chore: release ([e4892b1](https://github.com/aplazo/angular.control-tower-dashboard/commit/e4892b1))
* chore: release 1.0.0 ([597165b](https://github.com/aplazo/angular.control-tower-dashboard/commit/597165b))
* chore: release redo the history to .com.mx ([5b3a0af](https://github.com/aplazo/angular.control-tower-dashboard/commit/5b3a0af))
* chore: remove unused configuration and environment type files ([616709d](https://github.com/aplazo/angular.control-tower-dashboard/commit/616709d))
* chore: remove unused injection ([0be08cd](https://github.com/aplazo/angular.control-tower-dashboard/commit/0be08cd))
* chore: reorder files ([bc4b328](https://github.com/aplazo/angular.control-tower-dashboard/commit/bc4b328))
* chore: reorder files and directories ([0d51ebe](https://github.com/aplazo/angular.control-tower-dashboard/commit/0d51ebe))
* chore: reorder files and directories ([fb7be7b](https://github.com/aplazo/angular.control-tower-dashboard/commit/fb7be7b))
* chore: set docker image, build and run ([f0b826a](https://github.com/aplazo/angular.control-tower-dashboard/commit/f0b826a))
* chore: settings for vscode ([c8cf5c2](https://github.com/aplazo/angular.control-tower-dashboard/commit/c8cf5c2))
* chore: settings for vscode ([881da31](https://github.com/aplazo/angular.control-tower-dashboard/commit/881da31))
* chore: sonar node version ([dc83c42](https://github.com/aplazo/angular.control-tower-dashboard/commit/dc83c42))
* chore: update dependencies ([43165dd](https://github.com/aplazo/angular.control-tower-dashboard/commit/43165dd))
* chore: update dependencies ([c39f099](https://github.com/aplazo/angular.control-tower-dashboard/commit/c39f099))
* chore: update dependencies ([8f0d34d](https://github.com/aplazo/angular.control-tower-dashboard/commit/8f0d34d))
* chore: update dependencies ([06c2a5c](https://github.com/aplazo/angular.control-tower-dashboard/commit/06c2a5c))
* chore: update dependencies ([ddf87cd](https://github.com/aplazo/angular.control-tower-dashboard/commit/ddf87cd))
* chore: update dependencies ([f4d11ec](https://github.com/aplazo/angular.control-tower-dashboard/commit/f4d11ec))
* chore: update deps ([e0dc599](https://github.com/aplazo/angular.control-tower-dashboard/commit/e0dc599))
* chore: update deps scope ([c6acc29](https://github.com/aplazo/angular.control-tower-dashboard/commit/c6acc29))
* chore: update jenkins ([2ec9f3e](https://github.com/aplazo/angular.control-tower-dashboard/commit/2ec9f3e))
* chore: update Jenkins ([750d58e](https://github.com/aplazo/angular.control-tower-dashboard/commit/750d58e))
* chore: Update package dependencies and add tyc field to campaign form ([d813992](https://github.com/aplazo/angular.control-tower-dashboard/commit/d813992))
* chore(dependencies): merge master ([6147e03](https://github.com/aplazo/angular.control-tower-dashboard/commit/6147e03))
* chore(docker): mexp-336 Add SELinux volume labeling for coverage directory ([4814258](https://github.com/aplazo/angular.control-tower-dashboard/commit/4814258))
* chore(docker): optimize coverage volume management in test workflow ([99eb45a](https://github.com/aplazo/angular.control-tower-dashboard/commit/99eb45a))
* chore(jenkins): mexp-336 Simplify git push command in deployment pipeline ([f421011](https://github.com/aplazo/angular.control-tower-dashboard/commit/f421011))
* chore(jenkins): optimize Pulumi deployment workflow ([ad31591](https://github.com/aplazo/angular.control-tower-dashboard/commit/ad31591))
* chore(release): 1.0.0 ([61b478d](https://github.com/aplazo/angular.control-tower-dashboard/commit/61b478d))
* chore(release): mexp-336 configure semantic-release ([de0b386](https://github.com/aplazo/angular.control-tower-dashboard/commit/de0b386))
* chore(sonar): update SonarQube configuration to change from crlf to lf ([73f22ad](https://github.com/aplazo/angular.control-tower-dashboard/commit/73f22ad))
* refactor: add toastr styles and remove refresh login usecase ([b35accb](https://github.com/aplazo/angular.control-tower-dashboard/commit/b35accb))
* refactor: APM-2129 login usecase to handle valid roles ([a6e3c50](https://github.com/aplazo/angular.control-tower-dashboard/commit/a6e3c50))
* refactor: apm-2296 button to the end ([0aa8b46](https://github.com/aplazo/angular.control-tower-dashboard/commit/0aa8b46))
* refactor: apm-2296 ensure correct values to merchant creation ([e871afd](https://github.com/aplazo/angular.control-tower-dashboard/commit/e871afd))
* refactor: apm-2296 fix names business metrics component ([7247b46](https://github.com/aplazo/angular.control-tower-dashboard/commit/7247b46))
* refactor: apm-2296 reorder files and rename consts ([d4cf6a1](https://github.com/aplazo/angular.control-tower-dashboard/commit/d4cf6a1))
* refactor: apm-2296 replace local error usecase handler ([88bdc41](https://github.com/aplazo/angular.control-tower-dashboard/commit/88bdc41))
* refactor: apm-2296 take out messages and fix names ([f9dd863](https://github.com/aplazo/angular.control-tower-dashboard/commit/f9dd863))
* refactor: apm-2297 implement user email entity ([07e1e03](https://github.com/aplazo/angular.control-tower-dashboard/commit/07e1e03))
* refactor: apm-2297 improve and remove bind from login component ([3ac742a](https://github.com/aplazo/angular.control-tower-dashboard/commit/3ac742a))
* refactor: apm-2297 inject menu store as app service ([e16dab7](https://github.com/aplazo/angular.control-tower-dashboard/commit/e16dab7))
* refactor: apm-2297 move route names to domain ([f46ec95](https://github.com/aplazo/angular.control-tower-dashboard/commit/f46ec95))
* refactor: apm-2297 move route names to domain ([118b36e](https://github.com/aplazo/angular.control-tower-dashboard/commit/118b36e))
* refactor: apm-2297 prepare payments to use canmatch ([b7751a6](https://github.com/aplazo/angular.control-tower-dashboard/commit/b7751a6))
* refactor: apm-2297 reorganize files ([c9d2217](https://github.com/aplazo/angular.control-tower-dashboard/commit/c9d2217))
* refactor: apm-2297 usecases provide any ([ecd1f61](https://github.com/aplazo/angular.control-tower-dashboard/commit/ecd1f61))
* refactor: apm-2349 some improvements ([a7f7f5c](https://github.com/aplazo/angular.control-tower-dashboard/commit/a7f7f5c))
* refactor: apm-2572 retrieve account data ([62a8752](https://github.com/aplazo/angular.control-tower-dashboard/commit/62a8752))
* refactor: apm-2573 retrieve billing info ([201d7ca](https://github.com/aplazo/angular.control-tower-dashboard/commit/201d7ca))
* refactor: apm-2575 change get mdr contract and service ([a02ab98](https://github.com/aplazo/angular.control-tower-dashboard/commit/a02ab98))
* refactor: apm-2575 mdr component chores cause new contract ([8db1c80](https://github.com/aplazo/angular.control-tower-dashboard/commit/8db1c80))
* refactor: apm-2701 get users ([226235a](https://github.com/aplazo/angular.control-tower-dashboard/commit/226235a))
* refactor: apm-2718 add updateAt to show contacts ([7d34f5e](https://github.com/aplazo/angular.control-tower-dashboard/commit/7d34f5e))
* refactor: define login routes ([5d224aa](https://github.com/aplazo/angular.control-tower-dashboard/commit/5d224aa))
* refactor: define login routes ([18367d8](https://github.com/aplazo/angular.control-tower-dashboard/commit/18367d8))
* refactor: improve cohesion for login routes ([7bd9038](https://github.com/aplazo/angular.control-tower-dashboard/commit/7bd9038))
* refactor: improve cohesion for login routes ([fc28793](https://github.com/aplazo/angular.control-tower-dashboard/commit/fc28793))
* refactor: mac-135 customizable confirm report ([f9170bb](https://github.com/aplazo/angular.control-tower-dashboard/commit/f9170bb))
* refactor: mexp-205 campaigns layout ([b928b33](https://github.com/aplazo/angular.control-tower-dashboard/commit/b928b33))
* refactor: mexp-208 ading missing servicesm usecases and fox some lints ([cbf24a9](https://github.com/aplazo/angular.control-tower-dashboard/commit/cbf24a9))
* refactor: mexp-208 fix some lint problems ([20783de](https://github.com/aplazo/angular.control-tower-dashboard/commit/20783de))
* refactor: mexp-334 update environment configuration with const variables and type assertion ([1501bb1](https://github.com/aplazo/angular.control-tower-dashboard/commit/1501bb1))
* refactor: move user store as a root provider ([802e117](https://github.com/aplazo/angular.control-tower-dashboard/commit/802e117))
* refactor: move user store as a root provider ([670f2dc](https://github.com/aplazo/angular.control-tower-dashboard/commit/670f2dc))
* refactor: Rename terms_conditions to termsConditions in campaign-related files ([a715096](https://github.com/aplazo/angular.control-tower-dashboard/commit/a715096))
* refactor(admin): mexp-336 Improve type safety and code quality across multiple components ([8af15bd](https://github.com/aplazo/angular.control-tower-dashboard/commit/8af15bd))
* refactor(jenkins): mexp-336 enhance deployment pipeline ([82227ca](https://github.com/aplazo/angular.control-tower-dashboard/commit/82227ca))
* refactor(jenkins): mexp-336 Improve SonarQube version naming strategy ([eb0c9a8](https://github.com/aplazo/angular.control-tower-dashboard/commit/eb0c9a8))
* refactor(jenkins): mexp-336 optimize code validation and versioning stages ([b113e63](https://github.com/aplazo/angular.control-tower-dashboard/commit/b113e63))
* refactor(jenkins): mexp-336 Restructure master branch deployment stages ([37f27e6](https://github.com/aplazo/angular.control-tower-dashboard/commit/37f27e6))
* refactor(jenkins): mexp-336 Simplify Pulumi deployment with reusable function ([8c97610](https://github.com/aplazo/angular.control-tower-dashboard/commit/8c97610))
* refactor(jenkins): mexp-336 Standardize Slack notification colors in deployment stages ([1aa6fb3](https://github.com/aplazo/angular.control-tower-dashboard/commit/1aa6fb3))
* test: add auth guard ([f69cdcf](https://github.com/aplazo/angular.control-tower-dashboard/commit/f69cdcf))
* test: add login component ([f14ef9f](https://github.com/aplazo/angular.control-tower-dashboard/commit/f14ef9f))
* test: add login mapper tests ([02e37b2](https://github.com/aplazo/angular.control-tower-dashboard/commit/02e37b2))
* test: add login mapper tests ([85d29d5](https://github.com/aplazo/angular.control-tower-dashboard/commit/85d29d5))
* test: add user entity tests ([d4a6091](https://github.com/aplazo/angular.control-tower-dashboard/commit/d4a6091))
* test: add user entity tests ([b745a06](https://github.com/aplazo/angular.control-tower-dashboard/commit/b745a06))
* test: apm-2200 add board component ([4f019e3](https://github.com/aplazo/angular.control-tower-dashboard/commit/4f019e3))
* test: apm-2200 add confirm report component ([249ab6d](https://github.com/aplazo/angular.control-tower-dashboard/commit/249ab6d))
* test: apm-2200 add coverage to sona analysis ([1fafab7](https://github.com/aplazo/angular.control-tower-dashboard/commit/1fafab7))
* test: apm-2200 add error handler ([2845ff2](https://github.com/aplazo/angular.control-tower-dashboard/commit/2845ff2))
* test: apm-2200 add extra expect to board component ([20e39f0](https://github.com/aplazo/angular.control-tower-dashboard/commit/20e39f0))
* test: apm-2200 add extra test payments summary ([0122281](https://github.com/aplazo/angular.control-tower-dashboard/commit/0122281))
* test: apm-2200 add extra test to board comp ([25c7745](https://github.com/aplazo/angular.control-tower-dashboard/commit/25c7745))
* test: apm-2200 add get list usecase ([73c07ab](https://github.com/aplazo/angular.control-tower-dashboard/commit/73c07ab))
* test: apm-2200 add get payments summary ([d059e9d](https://github.com/aplazo/angular.control-tower-dashboard/commit/d059e9d))
* test: apm-2200 add login providers ([6292454](https://github.com/aplazo/angular.control-tower-dashboard/commit/6292454))
* test: apm-2200 add login repo infra and reorder files ([32e045b](https://github.com/aplazo/angular.control-tower-dashboard/commit/32e045b))
* test: apm-2200 add login usecase ([54b6ac5](https://github.com/aplazo/angular.control-tower-dashboard/commit/54b6ac5))
* test: apm-2200 add more payments get list usecase ([9166105](https://github.com/aplazo/angular.control-tower-dashboard/commit/9166105))
* test: apm-2200 add more to login mapper ([f186717](https://github.com/aplazo/angular.control-tower-dashboard/commit/f186717))
* test: apm-2200 add payments component ([3d6abad](https://github.com/aplazo/angular.control-tower-dashboard/commit/3d6abad))
* test: apm-2200 add payments criteria ([9de8e1f](https://github.com/aplazo/angular.control-tower-dashboard/commit/9de8e1f))
* test: apm-2200 add payments list repository ([7b88f7f](https://github.com/aplazo/angular.control-tower-dashboard/commit/7b88f7f))
* test: apm-2200 add payments providers ([855fe5a](https://github.com/aplazo/angular.control-tower-dashboard/commit/855fe5a))
* test: apm-2200 add payments send report repository ([c7158fc](https://github.com/aplazo/angular.control-tower-dashboard/commit/c7158fc))
* test: apm-2200 add payments summary repository ([79b8bb5](https://github.com/aplazo/angular.control-tower-dashboard/commit/79b8bb5))
* test: apm-2200 add send report usecase ([0d04c90](https://github.com/aplazo/angular.control-tower-dashboard/commit/0d04c90))
* test: apm-2200 add shared providers ([b055314](https://github.com/aplazo/angular.control-tower-dashboard/commit/b055314))
* test: apm-2200 add simple user store ([4b9b306](https://github.com/aplazo/angular.control-tower-dashboard/commit/4b9b306))
* test: apm-2200 add token interceptor ([81bae9c](https://github.com/aplazo/angular.control-tower-dashboard/commit/81bae9c))
* test: apm-2200 add transloco service ([c53530e](https://github.com/aplazo/angular.control-tower-dashboard/commit/c53530e))
* test: apm-2200 complete login component ([88c9b40](https://github.com/aplazo/angular.control-tower-dashboard/commit/88c9b40))
* test: apm-2200 fix login mapper test issues with Date ([cd05639](https://github.com/aplazo/angular.control-tower-dashboard/commit/cd05639))
* test: apm-2200 remove unused service ([eb38a17](https://github.com/aplazo/angular.control-tower-dashboard/commit/eb38a17))
* test: apm-2200 shield environment injection ([4f54a7d](https://github.com/aplazo/angular.control-tower-dashboard/commit/4f54a7d))
* test: apm-2296 add business metrics ([ced8313](https://github.com/aplazo/angular.control-tower-dashboard/commit/ced8313))
* test: apm-2296 add legal representative ([44ef93e](https://github.com/aplazo/angular.control-tower-dashboard/commit/44ef93e))
* test: apm-2297 add billing component ([18cb27c](https://github.com/aplazo/angular.control-tower-dashboard/commit/18cb27c))
* test: apm-2297 add creation component ([045c55d](https://github.com/aplazo/angular.control-tower-dashboard/commit/045c55d))
* test: apm-2297 add extra case ([f6cc2d3](https://github.com/aplazo/angular.control-tower-dashboard/commit/f6cc2d3))
* test: apm-2297 add info settings component ([e47714e](https://github.com/aplazo/angular.control-tower-dashboard/commit/e47714e))
* test: apm-2297 add match by menu ([fa183b5](https://github.com/aplazo/angular.control-tower-dashboard/commit/fa183b5))
* test: apm-2297 add mdr settings component ([02da6c3](https://github.com/aplazo/angular.control-tower-dashboard/commit/02da6c3))
* test: apm-2297 add simple menu store ([7c4e8b6](https://github.com/aplazo/angular.control-tower-dashboard/commit/7c4e8b6))
* test: apm-2297 add status settings component ([33d76c0](https://github.com/aplazo/angular.control-tower-dashboard/commit/33d76c0))
* test: apm-2297 add users settings component ([c02d7a3](https://github.com/aplazo/angular.control-tower-dashboard/commit/c02d7a3))
* test: apm-2297 fix broken tests for payment component ([a7dcdac](https://github.com/aplazo/angular.control-tower-dashboard/commit/a7dcdac))
* test: apm-2297 login usecase ([02a2cc8](https://github.com/aplazo/angular.control-tower-dashboard/commit/02a2cc8))
* test: apm-2297 remove forced tests statement ([13f71ab](https://github.com/aplazo/angular.control-tower-dashboard/commit/13f71ab))
* test: apm-2297 render by menu directive ([3ffd97a](https://github.com/aplazo/angular.control-tower-dashboard/commit/3ffd97a))
* test: apm-2349 add mock services ([fc22fa0](https://github.com/aplazo/angular.control-tower-dashboard/commit/fc22fa0))
* test: apm-2349 add tests ([d9870f0](https://github.com/aplazo/angular.control-tower-dashboard/commit/d9870f0))
* test: apm-2349 fix tests ([69daf27](https://github.com/aplazo/angular.control-tower-dashboard/commit/69daf27))
* test: apm-2359 incorrect params ([54466a5](https://github.com/aplazo/angular.control-tower-dashboard/commit/54466a5))
* test: apm-2360 info settings ([3c165e5](https://github.com/aplazo/angular.control-tower-dashboard/commit/3c165e5))
* test: apm-2361 adding more unit tests ([7f625c5](https://github.com/aplazo/angular.control-tower-dashboard/commit/7f625c5))
* test: apm-2363 add contact component ([8066969](https://github.com/aplazo/angular.control-tower-dashboard/commit/8066969))
* test: apm-2363 add contact http repository ([0d4bf69](https://github.com/aplazo/angular.control-tower-dashboard/commit/0d4bf69))
* test: apm-2363 add get contacts usecase ([90946d7](https://github.com/aplazo/angular.control-tower-dashboard/commit/90946d7))
* test: apm-2363 add mdr http repository ([b908774](https://github.com/aplazo/angular.control-tower-dashboard/commit/b908774))
* test: apm-2363 add mdr usecase ([5449515](https://github.com/aplazo/angular.control-tower-dashboard/commit/5449515))
* test: apm-2363 add merchant store tests ([44743de](https://github.com/aplazo/angular.control-tower-dashboard/commit/44743de))
* test: apm-2363 add more tests ([e4caf43](https://github.com/aplazo/angular.control-tower-dashboard/commit/e4caf43))
* test: apm-2364 ([3ba6ae6](https://github.com/aplazo/angular.control-tower-dashboard/commit/3ba6ae6))
* test: apm-2366 add crashing tests ([bfd78b2](https://github.com/aplazo/angular.control-tower-dashboard/commit/bfd78b2))
* test: apm-2518 broken tests ([5dde44f](https://github.com/aplazo/angular.control-tower-dashboard/commit/5dde44f))
* test: apm-2569 add some ones ([45c4b30](https://github.com/aplazo/angular.control-tower-dashboard/commit/45c4b30))
* test: apm-2569 receipts ([6d17afe](https://github.com/aplazo/angular.control-tower-dashboard/commit/6d17afe))
* test: apm-2572 account setting page ([db493ba](https://github.com/aplazo/angular.control-tower-dashboard/commit/db493ba))
* test: apm-2572 account update usecase ([9f1d6cc](https://github.com/aplazo/angular.control-tower-dashboard/commit/9f1d6cc))
* test: apm-2573 billing component & repository ([b31a36e](https://github.com/aplazo/angular.control-tower-dashboard/commit/b31a36e))
* test: apm-2573 refactor by new changes ([f6f448c](https://github.com/aplazo/angular.control-tower-dashboard/commit/f6f448c))
* test: apm-2573 update invoice ([3622849](https://github.com/aplazo/angular.control-tower-dashboard/commit/3622849))
* test: apm-2573 update invoice usecase ([972e437](https://github.com/aplazo/angular.control-tower-dashboard/commit/972e437))
* test: apm-2574 fix admin layout providers ([461e315](https://github.com/aplazo/angular.control-tower-dashboard/commit/461e315))
* test: apm-2574 remove date false negatives ([d48341a](https://github.com/aplazo/angular.control-tower-dashboard/commit/d48341a))
* test: apm-2574 upsert branch ([2a1e4ed](https://github.com/aplazo/angular.control-tower-dashboard/commit/2a1e4ed))
* test: apm-2574 upsert branch repository ([7e43bbb](https://github.com/aplazo/angular.control-tower-dashboard/commit/7e43bbb))
* test: apm-2574 upsert branch with dialog ([0b66db6](https://github.com/aplazo/angular.control-tower-dashboard/commit/0b66db6))
* test: apm-2575 get mdr tests ([3ed347b](https://github.com/aplazo/angular.control-tower-dashboard/commit/3ed347b))
* test: apm-2575 mdruidto fix contract ([fb7a04e](https://github.com/aplazo/angular.control-tower-dashboard/commit/fb7a04e))
* test: apm-2656 fix tests ([96d08f3](https://github.com/aplazo/angular.control-tower-dashboard/commit/96d08f3))
* test: apm-2701 ([c62654b](https://github.com/aplazo/angular.control-tower-dashboard/commit/c62654b))
* test: apm-2701 bedore edit users ([23810dc](https://github.com/aplazo/angular.control-tower-dashboard/commit/23810dc))
* test: apm-2701 create user feature ([1e58d40](https://github.com/aplazo/angular.control-tower-dashboard/commit/1e58d40))
* test: apm-2701 pass before create user feat ([6c4292c](https://github.com/aplazo/angular.control-tower-dashboard/commit/6c4292c))
* test: apm-2718 add some tests ([97dfc2a](https://github.com/aplazo/angular.control-tower-dashboard/commit/97dfc2a))
* test: apm-2718 create contact with dialog ([45aee12](https://github.com/aplazo/angular.control-tower-dashboard/commit/45aee12))
* test: apm-2718 fix tests ([6d98b25](https://github.com/aplazo/angular.control-tower-dashboard/commit/6d98b25))
* test: branch form ([2b6b8b6](https://github.com/aplazo/angular.control-tower-dashboard/commit/2b6b8b6))
* test: fix settings to execute tests ([851db5f](https://github.com/aplazo/angular.control-tower-dashboard/commit/851db5f))
* test: fix settings to execute tests ([c875ced](https://github.com/aplazo/angular.control-tower-dashboard/commit/c875ced))
* test: mac-135 only current tests ([b2fd831](https://github.com/aplazo/angular.control-tower-dashboard/commit/b2fd831))
* test: mac-140 replacing select component tests ([177d6d3](https://github.com/aplazo/angular.control-tower-dashboard/commit/177d6d3))
* test: mac-63 fix current tests ([721d09f](https://github.com/aplazo/angular.control-tower-dashboard/commit/721d09f))
* test: mac-78 fixing broken ([5330c3a](https://github.com/aplazo/angular.control-tower-dashboard/commit/5330c3a))
* test: mac-87 fix broken ([a984371](https://github.com/aplazo/angular.control-tower-dashboard/commit/a984371))
* test: mexp-208 aplazo-reawards-table component ([d1f13fa](https://github.com/aplazo/angular.control-tower-dashboard/commit/d1f13fa))
* test: mexp-208 campaign form component ([5995975](https://github.com/aplazo/angular.control-tower-dashboard/commit/5995975))
* test: mexp-208 createone campaing usecas and fixes in ui ([0cd8b7e](https://github.com/aplazo/angular.control-tower-dashboard/commit/0cd8b7e))
* test: mexp-208 edit campagin usecase ([5c85ca8](https://github.com/aplazo/angular.control-tower-dashboard/commit/5c85ca8))
* test: mexp-208 fetch premios aplazo usecase and fix for PremiosAplazoCampaign interface ([4f80675](https://github.com/aplazo/angular.control-tower-dashboard/commit/4f80675))
* test: mexp-313 update tests by adding missing property ([a6c1b5a](https://github.com/aplazo/angular.control-tower-dashboard/commit/a6c1b5a))
* test: remove unnecessary tests ([ed7361b](https://github.com/aplazo/angular.control-tower-dashboard/commit/ed7361b))
* feature: mexp-313 Checkbox in invoice to allow General public ([b4eebff](https://github.com/aplazo/angular.control-tower-dashboard/commit/b4eebff))
* feature: mexp-313 Checkbox in invoice to allow General public ([aa993e9](https://github.com/aplazo/angular.control-tower-dashboard/commit/aa993e9))
* feature: mexp-313 Checkbox in invoice to allow General public ([8da929f](https://github.com/aplazo/angular.control-tower-dashboard/commit/8da929f))
* feature: mexp-313 Checkbox in invoice to allow General public ([b523553](https://github.com/aplazo/angular.control-tower-dashboard/commit/b523553))
* feature: mexp-313 Checkbox in invoice to allow General public ([1f5c5c3](https://github.com/aplazo/angular.control-tower-dashboard/commit/1f5c5c3))
* style: apm-2465 mobile styles ([8dbd3d1](https://github.com/aplazo/angular.control-tower-dashboard/commit/8dbd3d1))
* style: change bg color ([bf2beaa](https://github.com/aplazo/angular.control-tower-dashboard/commit/bf2beaa))

# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## 1.0.0 (2025-02-25)


### Features

* add app loader ui component ([439813e](https://github.com/aplazo/angular.control-tower-dashboard/commit/439813ef5f64d4bb0b95cc5f9b696f5298d9cdee))
* add app loader ui component ([d6daee5](https://github.com/aplazo/angular.control-tower-dashboard/commit/d6daee53327bd4e60ce500baaa17049b56e8c7ce))
* add auth guards ([0de06bf](https://github.com/aplazo/angular.control-tower-dashboard/commit/0de06bfbcb14ba0e89b0b7ccb70897d207f5e2ad))
* add auth guards ([0b2c5bb](https://github.com/aplazo/angular.control-tower-dashboard/commit/0b2c5bbc9c333ced2c0347f66a2682878cb7fce3))
* add basics info component ([ba5259e](https://github.com/aplazo/angular.control-tower-dashboard/commit/ba5259e77b88994097825c57d5832a40f15d8349))
* add dockerfile and fix tailwind types ([8224394](https://github.com/aplazo/angular.control-tower-dashboard/commit/8224394f6169f83995d436c8521ae1e1145f4a93))
* add generals component ([bcbec7c](https://github.com/aplazo/angular.control-tower-dashboard/commit/bcbec7cb7537c1a5e4245a41873419db7036e527))
* add jenkinsfile ([146cfed](https://github.com/aplazo/angular.control-tower-dashboard/commit/146cfed6094cf0e574032ca7bc94c9f2c2ead359))
* add login dtos ([877d891](https://github.com/aplazo/angular.control-tower-dashboard/commit/877d891444addd8e5dacd9c60251aaea8c768d5f))
* add login dtos ([b082c53](https://github.com/aplazo/angular.control-tower-dashboard/commit/b082c530942f26c9bde7bb3c28a34d638a52a1b8))
* add login repository implementation ([f9ea8e2](https://github.com/aplazo/angular.control-tower-dashboard/commit/f9ea8e2595e9e4b28ac276ec1bd2fb343e0ee3fb))
* add login repository implementation ([493b520](https://github.com/aplazo/angular.control-tower-dashboard/commit/493b52021fd9ee508942a4a4bc48607969a2bd18))
* Add Mac ARM64 docker configuration and update dev server settings ([9b113f2](https://github.com/aplazo/angular.control-tower-dashboard/commit/9b113f2cc625bd11b16607d4ae7d9d4d76263b5d))
* add makefile to build/run docker img ([c002d44](https://github.com/aplazo/angular.control-tower-dashboard/commit/c002d44c9314d304d134891ff7a950c60a2a78bb))
* add new serializer method ([d2951aa](https://github.com/aplazo/angular.control-tower-dashboard/commit/d2951aac1201ccbeb877f06ecbec846821454137))
* add new serializer method ([bb6915a](https://github.com/aplazo/angular.control-tower-dashboard/commit/bb6915aaf3b948f943b91d5d7f098cbb83c8aac1))
* add persistence service ([a15f9d4](https://github.com/aplazo/angular.control-tower-dashboard/commit/a15f9d417b351c1170b065a01045476156695630))
* add persistence service ([a4aaa11](https://github.com/aplazo/angular.control-tower-dashboard/commit/a4aaa11cad2827efea9484538ca9e77f2250d335))
* add refresh login usecase ([ab56a81](https://github.com/aplazo/angular.control-tower-dashboard/commit/ab56a819cfed76238041139dcafc536e1b69fffc))
* add refresh login usecase ([7d47598](https://github.com/aplazo/angular.control-tower-dashboard/commit/7d47598c8db1be1c26d653e01fd096f93b33791c))
* add user entity ([38be528](https://github.com/aplazo/angular.control-tower-dashboard/commit/38be5288bd3c0766598303140f7c3c4515e84a0d))
* add user entity ([72c1467](https://github.com/aplazo/angular.control-tower-dashboard/commit/72c14673e5efc1d7caebea153b65bf9b31a5e393))
* add user store service ([cae6fdc](https://github.com/aplazo/angular.control-tower-dashboard/commit/cae6fdc4d1ab18546615be7c3008c4f248a560fa))
* add user store service ([02d894c](https://github.com/aplazo/angular.control-tower-dashboard/commit/02d894cc659d309d5af4a2728ed38e0b136d52b7))
* add valid roles ([2e277ca](https://github.com/aplazo/angular.control-tower-dashboard/commit/2e277ca6f7048958c62fcdc83014446050ecc37a))
* add valid roles ([b7eafb1](https://github.com/aplazo/angular.control-tower-dashboard/commit/b7eafb11f75d583a8549a3344d262d152cbc4648))
* APM-2129 add refresh login to app initializer factory ([ce6c961](https://github.com/aplazo/angular.control-tower-dashboard/commit/ce6c96127fb8d4a0c6b599d0b9ecc945de1d0b7b))
* APM-2129 add refresh login to app initializer factory ([2511629](https://github.com/aplazo/angular.control-tower-dashboard/commit/2511629dbee77616cd139106cf7b0e5f174a2d92))
* APM-2129 change routing strategy and render components ([74347a9](https://github.com/aplazo/angular.control-tower-dashboard/commit/74347a92e05a3a35674ab4086132a4549ce8c445))
* APM-2129 redirect in success login usecase ([16766e4](https://github.com/aplazo/angular.control-tower-dashboard/commit/16766e47b013a3a432c5c9e1afdc044e795b5f22))
* APM-2129 redirect in success login usecase ([792c01b](https://github.com/aplazo/angular.control-tower-dashboard/commit/792c01b0383fbd8728e2b28403daa8c54048dd79))
* APM-2129 refactor routes ([4d6b3db](https://github.com/aplazo/angular.control-tower-dashboard/commit/4d6b3db3fa8b591b47547cde79db87763d3ef4a3))
* APM-2130 add colors to adjustments in payments module ([8b794ec](https://github.com/aplazo/angular.control-tower-dashboard/commit/8b794ec30ca17ed4e7b959930c7f8bd260a9c62f))
* apm-2130 add send report usecase ([34969c1](https://github.com/aplazo/angular.control-tower-dashboard/commit/34969c196006173791f995031f8f3f3da67a9049))
* apm-2130 handle null values for summary ([75ab649](https://github.com/aplazo/angular.control-tower-dashboard/commit/75ab64941ae0b7fc06fa5fa63e47ce94192b2ff1))
* apm-2130 payments list ([1b26093](https://github.com/aplazo/angular.control-tower-dashboard/commit/1b2609356c624c400942039cddceb993229e8310))
* apm-2130 payments module ([f2a41b5](https://github.com/aplazo/angular.control-tower-dashboard/commit/f2a41b5e229d93e16abac31498c7c2b890acc2eb))
* APM-2130 set icon to payments sidenavlink ([85e95e9](https://github.com/aplazo/angular.control-tower-dashboard/commit/85e95e9ba9231eb0248765b21ac28afaa88a0e54))
* apm-2296 add admin mapper ([66c385a](https://github.com/aplazo/angular.control-tower-dashboard/commit/66c385ae325724c67a57e1e6eb97ab2c7b3ef888))
* apm-2296 add business metrics ([b529645](https://github.com/aplazo/angular.control-tower-dashboard/commit/b52964513542f4ee2031df8e7308cdfda38aa441))
* apm-2296 add components to creation page ([2aba0be](https://github.com/aplazo/angular.control-tower-dashboard/commit/2aba0be38f2a3ffdfb34b522ba1915834caf468d))
* apm-2296 add deactivated guard to merchant creation ([500fcb3](https://github.com/aplazo/angular.control-tower-dashboard/commit/500fcb3dd084e231c4f02ec5631dd33ab43fb997))
* apm-2296 add info merchant component ([9dfd857](https://github.com/aplazo/angular.control-tower-dashboard/commit/9dfd8572a19b04189dd8a3ec5498814c98d8f541))
* apm-2296 add legal representative component ([aff831d](https://github.com/aplazo/angular.control-tower-dashboard/commit/aff831d50cb032f18a8d93c96416289c8ff8c884))
* apm-2296 add merchant creation component ([1ec63a9](https://github.com/aplazo/angular.control-tower-dashboard/commit/1ec63a96ba899f08f73204b27c18170ac82d6172))
* apm-2296 add new merchant repository ([9b2179a](https://github.com/aplazo/angular.control-tower-dashboard/commit/9b2179a8db732b31359366256f219f4d8b05b914))
* apm-2296 add new merchant request dto ([f82de3c](https://github.com/aplazo/angular.control-tower-dashboard/commit/f82de3c0b6816273e5f7957c4596ed8518c48849))
* apm-2296 add new merchant response dto ([3390e35](https://github.com/aplazo/angular.control-tower-dashboard/commit/3390e35fc26166cccc96cbd96a52a0afd8ac3414))
* apm-2296 add new merchant usecase ([f002241](https://github.com/aplazo/angular.control-tower-dashboard/commit/f002241d826808d28cc1e9ee5d0c84374261336e))
* apm-2296 add webpage validation to mapper ([d79efa7](https://github.com/aplazo/angular.control-tower-dashboard/commit/d79efa7ce83e533988568f1fbf53f4bc7c0253c0))
* apm-2296 improve confirm prospect component ([43e7805](https://github.com/aplazo/angular.control-tower-dashboard/commit/43e7805c761be6f305e941c2fb6fd760a806b2b8))
* apm-2297 add admin feature module ([638aeb7](https://github.com/aplazo/angular.control-tower-dashboard/commit/638aeb7277b898f09676ee28b9b94a1d06074832))
* apm-2297 add extra getter to menu store ([cce4719](https://github.com/aplazo/angular.control-tower-dashboard/commit/cce4719dbe8055a50cbdb9182dbb3082c3b20cde))
* apm-2297 add match by menu ([d8cb09e](https://github.com/aplazo/angular.control-tower-dashboard/commit/d8cb09e0c18e5c45b1fee44c37dd350caf68632d))
* apm-2297 add menu store ([76b0945](https://github.com/aplazo/angular.control-tower-dashboard/commit/76b09451725d709cd7ade243ae1dc2302329c66e))
* apm-2297 add new valid role ([e91d1e8](https://github.com/aplazo/angular.control-tower-dashboard/commit/e91d1e8be3a1d920bacfa8fb0cecb93e7fd8ef4a))
* apm-2297 add sidenav links for admin module ([179a35e](https://github.com/aplazo/angular.control-tower-dashboard/commit/179a35e4b74fc1b754c0022dee32a66f75719c0a))
* apm-2297 add user email entity ([40e3804](https://github.com/aplazo/angular.control-tower-dashboard/commit/40e3804f2358ec1cbea14fc49efeab9000bf6673))
* apm-2297 hydrate menu store on login usecase ([bad4e8a](https://github.com/aplazo/angular.control-tower-dashboard/commit/bad4e8ac5552e1a9b3a4b59d44b9b5781ca1e395))
* apm-2297 implement canmatch by menu ([73109aa](https://github.com/aplazo/angular.control-tower-dashboard/commit/73109aae0d2823fd937391db362c4a6353533b3a))
* apm-2349 add new admin providers ([58c7541](https://github.com/aplazo/angular.control-tower-dashboard/commit/58c754173ae54c1993b06c8c97192e324482250b))
* apm-2349 add new entities/dtos ([6cf4da8](https://github.com/aplazo/angular.control-tower-dashboard/commit/6cf4da809c0f8c4c6381bd7e3651e8b1d6543d0d))
* apm-2349 add seach by usecase ([a169027](https://github.com/aplazo/angular.control-tower-dashboard/commit/a169027b546cb252d2bd6aaac5056c00e5672734))
* apm-2349 add search by repository ([7e64b93](https://github.com/aplazo/angular.control-tower-dashboard/commit/7e64b9309b4ee4d52c60a89065f23ea774b822c4))
* apm-2349 admin layout improvements ([c9bee28](https://github.com/aplazo/angular.control-tower-dashboard/commit/c9bee28887162a313837dfb6a706757d7252097f))
* apm-2349 change admin layout ([c0a4545](https://github.com/aplazo/angular.control-tower-dashboard/commit/c0a4545bcfb53a81122dba3321fcb7ff6aa79ef1))
* apm-2349 get basics info repository ([d4bb2e8](https://github.com/aplazo/angular.control-tower-dashboard/commit/d4bb2e8b147baa63e20f2c4a3632d622a3b0d30f))
* apm-2349 get generals info repository ([dfd368f](https://github.com/aplazo/angular.control-tower-dashboard/commit/dfd368ffe47085ff4fb68263300b860d56b90981))
* apm-2349 improve merchant store ([0d5dedd](https://github.com/aplazo/angular.control-tower-dashboard/commit/0d5deddf97ed206f9f38ed41a88c387916b0518c))
* apm-2361 add account component ([5feb7a8](https://github.com/aplazo/angular.control-tower-dashboard/commit/5feb7a8840b65464c5a740211817b31fd08a4acb))
* apm-2362 add invoice page ([c25a107](https://github.com/aplazo/angular.control-tower-dashboard/commit/c25a107a54eb442300edbb19e74a938020229593))
* apm-2363 add contacts ([46b1ff2](https://github.com/aplazo/angular.control-tower-dashboard/commit/46b1ff29545dba9bb88dd75b59886b88d9075875))
* apm-2364 add mdr ([fd8dcfe](https://github.com/aplazo/angular.control-tower-dashboard/commit/fd8dcfeb62bf5be07f93f0159dfa12adf5ec5bbb))
* apm-2365 add storefronts page ([bc7825f](https://github.com/aplazo/angular.control-tower-dashboard/commit/bc7825f58510047a7be6c198e71a87b412247e62))
* apm-2366 add users page ([adfc4fb](https://github.com/aplazo/angular.control-tower-dashboard/commit/adfc4fb9dca1682072eb67b640c48b558ecb7eed))
* apm-2465 update merchant status ([f58d760](https://github.com/aplazo/angular.control-tower-dashboard/commit/f58d7602a0ca59b834f9ef20c7fa9b5043623794))
* apm-2518 add receipts ([1dac7e9](https://github.com/aplazo/angular.control-tower-dashboard/commit/1dac7e9c521343d67df3aee901f639a7aa1ade23))
* apm-2518 menu improvements ([59a8fd1](https://github.com/aplazo/angular.control-tower-dashboard/commit/59a8fd134a7fecdd91ae474ebb00e518106b5d1a))
* apm-2518 uploading files ([c035190](https://github.com/aplazo/angular.control-tower-dashboard/commit/c03519036e2073d01d99ab2dd5eab9863447584b))
* apm-2528 sidebar click outside improvements ([11db5ad](https://github.com/aplazo/angular.control-tower-dashboard/commit/11db5ad5c8ad4486c15a0c6adfd03c07b95ced96))
* apm-2569 basics editing in component ([234842d](https://github.com/aplazo/angular.control-tower-dashboard/commit/234842de55f7f9f9b931570fd8f8700ee20679cb))
* apm-2569 new contract to basic info ([2bfc7f3](https://github.com/aplazo/angular.control-tower-dashboard/commit/2bfc7f3e7bc7acbe4936515b8e430a9ed8115afd))
* apm-2569 rename filename ([f59b94b](https://github.com/aplazo/angular.control-tower-dashboard/commit/f59b94bc51418989d7b0b6d8b2d33a1f9445f6ce))
* apm-2572 update account ([76ed997](https://github.com/aplazo/angular.control-tower-dashboard/commit/76ed99724acd317a8abaf3737b4246b1a9d422d9))
* apm-2573 improve invoice entities ([2e606ca](https://github.com/aplazo/angular.control-tower-dashboard/commit/2e606ca384ec5289c7974f553cefc9dc0d5339cf))
* apm-2574 add storefront edit/create ([ba62cc1](https://github.com/aplazo/angular.control-tower-dashboard/commit/ba62cc129a8b66eb824df8768fb849b81dfc4cda))
* apm-2575 update mdr usecase ([cc78457](https://github.com/aplazo/angular.control-tower-dashboard/commit/cc784578f1877699aaf56a74550881d133076221))
* apm-2656 remove page url validation ([35acbfd](https://github.com/aplazo/angular.control-tower-dashboard/commit/35acbfd08c4318dc62c2d985b4049a17d0aa77d6))
* apm-2656 remove page url validation ([b043b31](https://github.com/aplazo/angular.control-tower-dashboard/commit/b043b317764a1f3f18aa2d7206b9669b801241fa))
* apm-2656 remove unused status from onboarding ([bd2eafc](https://github.com/aplazo/angular.control-tower-dashboard/commit/bd2eafca2917c328f977871db26448aa69bbb7ca))
* apm-2701 create user feature ([7dae682](https://github.com/aplazo/angular.control-tower-dashboard/commit/7dae682a696be88349d17b515ce7a538c0b2316c))
* apm-2701 create user form component ([6de2368](https://github.com/aplazo/angular.control-tower-dashboard/commit/6de23684f77073f6ca3ded3be2e33990d4df71ae))
* apm-2701 edit user ([80f69db](https://github.com/aplazo/angular.control-tower-dashboard/commit/80f69dbcf769e2613e5b3889e754e430e534bb44))
* apm-2701 edit user issues ([7481224](https://github.com/aplazo/angular.control-tower-dashboard/commit/7481224dc2bc95cdec035c385fb8c36b3110efa0))
* apm-2701 edit users ([ed45ef2](https://github.com/aplazo/angular.control-tower-dashboard/commit/ed45ef251216b0dbb1bb05877991f4ff06069340))
* apm-2718 create contact ([2f9a509](https://github.com/aplazo/angular.control-tower-dashboard/commit/2f9a509228ec92dfa43a7ed1a352943a37c749b6))
* apm-2718 edit contact ([b381751](https://github.com/aplazo/angular.control-tower-dashboard/commit/b381751a93aa1e4d7bb5ea22afe243105caaca0c))
* apm-2811 invoice generator ([5f05ebc](https://github.com/aplazo/angular.control-tower-dashboard/commit/5f05ebc4a0259ac56e460ba6830a16e983322948))
* apm-2896 industries list changes ([2e06322](https://github.com/aplazo/angular.control-tower-dashboard/commit/2e0632294ea2e84375c23da4353e18a839550bf1))
* apm-2897 remove category field from generals ([729adf3](https://github.com/aplazo/angular.control-tower-dashboard/commit/729adf3088a35daa4882375aa2d3b57fe0a2ccc1))
* camapign fetcher pagination and basic form ([35a759b](https://github.com/aplazo/angular.control-tower-dashboard/commit/35a759b7fbec8ed6ebb50a9e32f773d2ee152a04))
* centralize mapping chores ([b4f8b1f](https://github.com/aplazo/angular.control-tower-dashboard/commit/b4f8b1f427b1222ff9c50617e2bd5ad012e4e82c))
* centralize mapping chores ([046c11a](https://github.com/aplazo/angular.control-tower-dashboard/commit/046c11a22add6bd0ae2d9694b512bbbb4b929761))
* define login repository ([906e1b7](https://github.com/aplazo/angular.control-tower-dashboard/commit/906e1b75bf3721e0146db95aba7c47cb015876ee))
* define login repository ([6c41177](https://github.com/aplazo/angular.control-tower-dashboard/commit/6c41177cdc2f8c754c3087b4b3f745ac04e31028))
* dx improvements ([93118ae](https://github.com/aplazo/angular.control-tower-dashboard/commit/93118ae739db9b09860f88871318b96aea80b7a6))
* edit generals ([3347532](https://github.com/aplazo/angular.control-tower-dashboard/commit/334753251ce87d3b2ef72ad7e93dba12b94dc167))
* google GSI with button in login page ([df5f4c8](https://github.com/aplazo/angular.control-tower-dashboard/commit/df5f4c826b14c70f6f380c4acdeaf800726a4eb6))
* inject and resolve root providers ([8075e75](https://github.com/aplazo/angular.control-tower-dashboard/commit/8075e7586aff5be218f36353badc6761aa3b44bd))
* inject and resolve root providers ([9d874ac](https://github.com/aplazo/angular.control-tower-dashboard/commit/9d874ac314ebf0eb409109c86c3e168e3c437229))
* mac-125 add phone to contact form ([0a5287e](https://github.com/aplazo/angular.control-tower-dashboard/commit/0a5287ef82255ba0f8812a6d4aa367e6d5ed4aa1))
* mac-135 invoices by id ([c26bb32](https://github.com/aplazo/angular.control-tower-dashboard/commit/c26bb32b81fcaa9a7aa4f0003d946a6b57277f03))
* mac-140 merchant status changes ([b9fcee0](https://github.com/aplazo/angular.control-tower-dashboard/commit/b9fcee0a8b4d714da7de371709b347f2b13009e5))
* mac-164 add excluded invoice status ([6f0eab7](https://github.com/aplazo/angular.control-tower-dashboard/commit/6f0eab74eb1642dd4711274c1a2ad55f0651b951))
* mac-204 show error msg from update basics ([21d4968](https://github.com/aplazo/angular.control-tower-dashboard/commit/21d49682697091e2b3e569787a8c50063c32e8a4))
* mac-63 changing logic to accept empty email and shows branches ([5228bb7](https://github.com/aplazo/angular.control-tower-dashboard/commit/5228bb724cd9490a2ecf42ddae802deb35f2f454))
* mac-63 hydrate branches on select merchant ([37a1941](https://github.com/aplazo/angular.control-tower-dashboard/commit/37a1941257b8b4e96046ae7839e5b95bb1929bb2))
* mac-77 retrieve invoice summary for payments ([e0b3644](https://github.com/aplazo/angular.control-tower-dashboard/commit/e0b3644dc5012cdf30facaf19ac8c9d4ff5848ff))
* mac-78 add invoice status to payments list ([9ed20e6](https://github.com/aplazo/angular.control-tower-dashboard/commit/9ed20e6faed12af7b8bdc21f378f762d24515a82))
* mexp-200 create one campaign ([c6db383](https://github.com/aplazo/angular.control-tower-dashboard/commit/c6db3838453215e2e5f2e222c266b085274d60a6))
* mexp-208 improve usecase ([df1f6ab](https://github.com/aplazo/angular.control-tower-dashboard/commit/df1f6ab730f0bbfec4eb3768cdcdc02eb6f0a413))
* mexp-209 edit campaign ([eaaf117](https://github.com/aplazo/angular.control-tower-dashboard/commit/eaaf117891021736ef5c9a8b3aee4f4ee7427dc6))
* mexp-315 replace old role with a new one ([84ab71c](https://github.com/aplazo/angular.control-tower-dashboard/commit/84ab71c3cc4a07ee7ff99b88be5614dc55c4399a))
* mexp-317 add tyc link to campaign form ([3af0306](https://github.com/aplazo/angular.control-tower-dashboard/commit/3af03069663170904c10eb64707e3bae0d900314))
* mexp-334 Enhance Jenkins pipeline with improved Slack notifications and secret management ([75530e4](https://github.com/aplazo/angular.control-tower-dashboard/commit/75530e490d04c8e7c2464200151117d5e7923f8d))
* mexp-334 migrate to @ngx-env/builder for environment configuration ([52360c3](https://github.com/aplazo/angular.control-tower-dashboard/commit/52360c389a9fd01dae7fbf033757187b5f04f304))
* mexp-77 form datepicker ([2f3f37f](https://github.com/aplazo/angular.control-tower-dashboard/commit/2f3f37f3b79263ee41002adc032046e1d1e1b88e))
* **shared-ui:** apm-2518 all logic completed ([a4ee980](https://github.com/aplazo/angular.control-tower-dashboard/commit/a4ee9801e07a429bb56d52147056b9686f9dc7ff))
* Update Docker and Jenkins configuration for multi-image build and deployment ([9d7241c](https://github.com/aplazo/angular.control-tower-dashboard/commit/9d7241cfb3871fda9cc2d5ac2c4904957ea5cc67))


### Bug Fixes

* apm-2130 remove unused dynamic pipe ([811d542](https://github.com/aplazo/angular.control-tower-dashboard/commit/811d5426bf02cbecea95e3a8147dbe07aafc4197))
* apm-2296 handle already registered email error ([7bd6be1](https://github.com/aplazo/angular.control-tower-dashboard/commit/7bd6be1407f16efa95d7fb2e9e591f913ac6afb5))
* apm-2296 handle reset at the end of merchant creation ([bd8aaa7](https://github.com/aplazo/angular.control-tower-dashboard/commit/bd8aaa7c4f603243306ce110763fb7ce4ca38b41))
* apm-2296 merchant request contract ([51a1e75](https://github.com/aplazo/angular.control-tower-dashboard/commit/51a1e750696732d91d7c384efac93296d45b4b96))
* apm-2297 add providers to correct layer ([424cd54](https://github.com/aplazo/angular.control-tower-dashboard/commit/424cd54b36f4ae5b8ff183520ee139f185e11db9))
* apm-2297 add selectors to prevent collitions ([8b77485](https://github.com/aplazo/angular.control-tower-dashboard/commit/8b77485469c74f9636c968653f3c6f7f39193b09))
* apm-2465 select instead radio for refund new status ([17946d3](https://github.com/aplazo/angular.control-tower-dashboard/commit/17946d3f991724a2aa328148a23cc92600f0e16d))
* apm-2518 dynamic multifiles ([910a459](https://github.com/aplazo/angular.control-tower-dashboard/commit/910a45941d9447220ff8d381ea43f71e998bbf26))
* apm-2518 fix routing ([e8d63a1](https://github.com/aplazo/angular.control-tower-dashboard/commit/e8d63a1fe1a06ae1ff54647711883e51ecf32728))
* apm-2569 empty edited fields ([6d359d8](https://github.com/aplazo/angular.control-tower-dashboard/commit/6d359d88fdd3d779758af48ec24abbfbcef5bf77))
* apm-2571 generals edition labels ([184dc05](https://github.com/aplazo/angular.control-tower-dashboard/commit/184dc053b82102b4c4f3ea6a32a7f6314ec3faf6))
* apm-2572 validate clabe 18 digits ([b31485b](https://github.com/aplazo/angular.control-tower-dashboard/commit/b31485b13eb3e33b35081d893e64a56c88615763))
* apm-2573 cfdi parsing values ([00193b6](https://github.com/aplazo/angular.control-tower-dashboard/commit/00193b6274d21a550facf1f2f5bedbe585923682))
* apm-2574 hide component on changes ([3639a94](https://github.com/aplazo/angular.control-tower-dashboard/commit/3639a94585ca6fbe6a00ae3cd91a082aa8cfadb9))
* apm-2574 update branches from store ([9688b75](https://github.com/aplazo/angular.control-tower-dashboard/commit/9688b75ffee868645741ac92be2baa68aae14468))
* apm-2574 update label ([0ff0c37](https://github.com/aplazo/angular.control-tower-dashboard/commit/0ff0c37a45fd2db2083bf35eddd2d6fc7bf4d73c))
* apm-2575 mdr incorrect comparation ([3117b94](https://github.com/aplazo/angular.control-tower-dashboard/commit/3117b94f8259e6f7ab8c7e581f6725e7463d49e2))
* apm-2701 only allowed roles ([7b9193c](https://github.com/aplazo/angular.control-tower-dashboard/commit/7b9193cac5eb1ab4d69787179079b9d34c8735f0))
* apm-2811 fix roles to invoice generation ([0cec469](https://github.com/aplazo/angular.control-tower-dashboard/commit/0cec469770bf8b078f3dae31ada464396dcbb721))
* apm-2811 generate invoice icon ([a3bd931](https://github.com/aplazo/angular.control-tower-dashboard/commit/a3bd93163deba591a7bd6fb85f3ba147096811c9))
* apm-2892 billing save store bug ([b57dce9](https://github.com/aplazo/angular.control-tower-dashboard/commit/b57dce9505b0a10aac7f781e6814f2424766a8b5))
* apm-2892 payment form fields ([ab773e3](https://github.com/aplazo/angular.control-tower-dashboard/commit/ab773e37ebe0144d5ac2a7171fc852cd66c83e7a))
* apm-2892 payment form fields ([055c2b2](https://github.com/aplazo/angular.control-tower-dashboard/commit/055c2b26804de681df95da02f6ebd82308199d99))
* apm-2971 create user ([343228f](https://github.com/aplazo/angular.control-tower-dashboard/commit/343228fb030e65814ad3e4d7cf742bf46be9d68b))
* client id environment ([1cce72a](https://github.com/aplazo/angular.control-tower-dashboard/commit/1cce72a596ab1cbea211dc006b8b0a554f8e1d5d))
* environment from inject ([a402e5f](https://github.com/aplazo/angular.control-tower-dashboard/commit/a402e5fa2dc7cd8c45a87885502edb8ec06a6588))
* Initialize tycControl with existing termsConditions data ([5eefe2c](https://github.com/aplazo/angular.control-tower-dashboard/commit/5eefe2c1776ffb38507ab4e7f835896b08a2841e))
* jenkinks node version ([65f5949](https://github.com/aplazo/angular.control-tower-dashboard/commit/65f594974971a0e7104ca32d4e54768dd7686325))
* loader aftercontentcheck error ([5940484](https://github.com/aplazo/angular.control-tower-dashboard/commit/594048437477b475b1437ba205cc23dd2de85d9e))
* mac-140 label for basic merchant staus label ([0430ad6](https://github.com/aplazo/angular.control-tower-dashboard/commit/0430ad6ae1d23e2be60935a8e7272211f4ad9a6e))
* mac-256 remove cache for account and billing ([ad239ab](https://github.com/aplazo/angular.control-tower-dashboard/commit/ad239ab246865900d38fbef1bd4b61de3c31c810))
* mac-63 dependencies ([57e4c40](https://github.com/aplazo/angular.control-tower-dashboard/commit/57e4c4015af44ae0387e508123139cedbe595eb7))
* mac-63 dependencies ([e0f9f83](https://github.com/aplazo/angular.control-tower-dashboard/commit/e0f9f83483f209f3bf5f9b9127e18f5a4b08d977))
* mac-63 option select for empty branches ([9f466e0](https://github.com/aplazo/angular.control-tower-dashboard/commit/9f466e00ec7dc9d6df633bfa21c556d18e70a3b6))
* mac-63 optional email ([7d3ff2c](https://github.com/aplazo/angular.control-tower-dashboard/commit/7d3ff2c0f7da6e7f9477aef762c8a78634c2ab5e))
* mac-88 active/deactive operator ([bc149bb](https://github.com/aplazo/angular.control-tower-dashboard/commit/bc149bb5b88584ab13e3c74cf3317edda5d8fa5e))
* mdr disable edition on success ([df9c8ea](https://github.com/aplazo/angular.control-tower-dashboard/commit/df9c8eac493ba0803146c494ec56a6e1751c7d2b))
* mexp-188 datepicker border overlap ([3ebbb3b](https://github.com/aplazo/angular.control-tower-dashboard/commit/3ebbb3ba45e61350f0d30e5312c77a6717b6b09b))
* mexp-201 Update campaign form TyC URL example to use .mx domain ([da982dd](https://github.com/aplazo/angular.control-tower-dashboard/commit/da982dda69fbd22b9b820de171a599921aa4236e))
* mexp-208 first page ([c6c1cd2](https://github.com/aplazo/angular.control-tower-dashboard/commit/c6c1cd2f789d27290a6eef2d330e59f5072c9a0c))
* mexp-208 fixes for pr approval ([03dc76e](https://github.com/aplazo/angular.control-tower-dashboard/commit/03dc76eb9e86787471feb70b1c00ae5b184c7549))
* mexp-209 listing campaigns default date ([5a4fb83](https://github.com/aplazo/angular.control-tower-dashboard/commit/5a4fb834c7eb5df722120f888cdf62f9df62d625))
* mexp-317 update campaign args ([be43947](https://github.com/aplazo/angular.control-tower-dashboard/commit/be439478dc2a3a3a9e8d3f123f4d3d9385e133f9))
* mexp-334 Enhance Jenkins pipeline environment variable handling ([0e39c0e](https://github.com/aplazo/angular.control-tower-dashboard/commit/0e39c0ed0deac1c546ca09ca1b96d770c86dec91))
* mexp-334 Modify Docker Compose test execution in Jenkins pipeline ([d1d7b79](https://github.com/aplazo/angular.control-tower-dashboard/commit/d1d7b792476f4918b33582383400ffb76a4a021c))
* mexp-334 optimize Jenkins pipeline secret retrieval ([d56cbad](https://github.com/aplazo/angular.control-tower-dashboard/commit/d56cbadeba00e5fe68d5c528b746e60dfda76434))
* mexp-334 Refactor E2E test execution in Jenkins pipeline ([1acdf95](https://github.com/aplazo/angular.control-tower-dashboard/commit/1acdf9524a9b3fa44986e0cb43d5e839a492dbbe))
* mexp-334 Refactor test configuration and Docker setup ([5fa4ac3](https://github.com/aplazo/angular.control-tower-dashboard/commit/5fa4ac3cbc015c0153b054f8785b54a27c3868db))
* mexp-334 Simplify Playwright browser installation ([cc1b271](https://github.com/aplazo/angular.control-tower-dashboard/commit/cc1b2718753c3141e3712d189c7a87aac2eded19))
* mexp-334 Simplify Playwright installation in Jenkins pipeline ([aec331d](https://github.com/aplazo/angular.control-tower-dashboard/commit/aec331d9631646a8ccd76932e124ed56d932eeed))
* mexp-334 Update Docker Compose command syntax in Jenkins pipeline ([1bdd305](https://github.com/aplazo/angular.control-tower-dashboard/commit/1bdd3055c2045d70a18f20ab6cf7f37d5b2e5d7b))
* mexp-334 Update Jenkins pipeline test command ([af4a662](https://github.com/aplazo/angular.control-tower-dashboard/commit/af4a662a72e81563c762a5fd6fc721c8685d0868))
* mexp-334 Update Karma and Jenkins configurations for improved test environment ([51fd48a](https://github.com/aplazo/angular.control-tower-dashboard/commit/51fd48acf83c59dbb4c89cb501fdbea5b9935495))
* mexp-334 Update Playwright installation command in Jenkins pipeline ([d33876f](https://github.com/aplazo/angular.control-tower-dashboard/commit/d33876fda9f69f7ef18e5fa096f1dcbe7d193fa0))
* mexp-77 datepicker input key ([4225969](https://github.com/aplazo/angular.control-tower-dashboard/commit/42259691011b3531e250594d8825ba3205d74f17))
* mexp-77 form datepicker styles and click outside ([2622b6f](https://github.com/aplazo/angular.control-tower-dashboard/commit/2622b6fdc046d8c110f7f0c8e045418d0878e616))
* mexp-77 prevent default ([1aa7b95](https://github.com/aplazo/angular.control-tower-dashboard/commit/1aa7b959de26079668d3eeb105c277598078cc92))
* remove comments ([59c7187](https://github.com/aplazo/angular.control-tower-dashboard/commit/59c7187907f163d6707f9836b790fdfda30bdf18))
* response headers ([0611c27](https://github.com/aplazo/angular.control-tower-dashboard/commit/0611c27074f1e064c555b6310b23df072401c83a))
* set public endpoints to dev env ([641dd15](https://github.com/aplazo/angular.control-tower-dashboard/commit/641dd154db3d5993fb8fdf7e469cc894b0bbdb12))
* **shared-ui:** apm-2575 mdr handling ui errrors ([7a02c6e](https://github.com/aplazo/angular.control-tower-dashboard/commit/7a02c6e180e4ef0286af3ab2804aaad63bbbb655))
* **shared-ui:** mac-63 multiple branches ([85706c8](https://github.com/aplazo/angular.control-tower-dashboard/commit/85706c8bab06d4e298fbcabf3bb223a2b675b3a9))
* styles and dependencies ([92dcdd1](https://github.com/aplazo/angular.control-tower-dashboard/commit/92dcdd1d88eba122a59d59dab0eb8ea481916725))
* unhandled null pointer ([8ced14d](https://github.com/aplazo/angular.control-tower-dashboard/commit/8ced14dfddc36b35d12b5170936d844469adac1c))
* unhandled null pointer ([9434ebe](https://github.com/aplazo/angular.control-tower-dashboard/commit/9434ebe24d0599ff9bb38f7c28394fdf230ee0b2))


### Code Refactoring

* add toastr styles and remove refresh login usecase ([b35accb](https://github.com/aplazo/angular.control-tower-dashboard/commit/b35accb370ba9834f594d59fd4776526092cd1f6))
* APM-2129 login usecase to handle valid roles ([a6e3c50](https://github.com/aplazo/angular.control-tower-dashboard/commit/a6e3c50d1ad7b7249b6433e598c56c4ce0e259d3))
* apm-2296 button to the end ([0aa8b46](https://github.com/aplazo/angular.control-tower-dashboard/commit/0aa8b46f894d12319eb7478381b624ec2f7e5a6c))
* apm-2296 ensure correct values to merchant creation ([e871afd](https://github.com/aplazo/angular.control-tower-dashboard/commit/e871afd8dbd0c78723c1c960e59cbfb4de199f59))
* apm-2296 fix names business metrics component ([7247b46](https://github.com/aplazo/angular.control-tower-dashboard/commit/7247b467b37d97bd8787aa58e77ffaab34634dba))
* apm-2296 reorder files and rename consts ([d4cf6a1](https://github.com/aplazo/angular.control-tower-dashboard/commit/d4cf6a103de5deb78a4f567df4cbd360855b9994))
* apm-2296 replace local error usecase handler ([88bdc41](https://github.com/aplazo/angular.control-tower-dashboard/commit/88bdc41fd022c7b3c60e896bc142d7c36b5748fd))
* apm-2296 take out messages and fix names ([f9dd863](https://github.com/aplazo/angular.control-tower-dashboard/commit/f9dd8631b4fe9b25e21f3a1f7c8d1417092f5888))
* apm-2297 implement user email entity ([07e1e03](https://github.com/aplazo/angular.control-tower-dashboard/commit/07e1e03b82a0c29cd679ed898c23b3d91e4c5643))
* apm-2297 improve and remove bind from login component ([3ac742a](https://github.com/aplazo/angular.control-tower-dashboard/commit/3ac742addbf78295854462e14583112b0d935f1c))
* apm-2297 inject menu store as app service ([e16dab7](https://github.com/aplazo/angular.control-tower-dashboard/commit/e16dab7b38bf06cda1b207395fc335656999fea8))
* apm-2297 move route names to domain ([f46ec95](https://github.com/aplazo/angular.control-tower-dashboard/commit/f46ec95f04a5e1ca65c8ec701c40cbebe5583593))
* apm-2297 move route names to domain ([118b36e](https://github.com/aplazo/angular.control-tower-dashboard/commit/118b36ebf3be35f7108cd97820d487088ab276ed))
* apm-2297 prepare payments to use canmatch ([b7751a6](https://github.com/aplazo/angular.control-tower-dashboard/commit/b7751a69bf0f11aa67d97cd298b91d2c9a0e3d42))
* apm-2297 reorganize files ([c9d2217](https://github.com/aplazo/angular.control-tower-dashboard/commit/c9d22175f251a6b318f1a21f530e904bc0bf096e))
* apm-2297 usecases provide any ([ecd1f61](https://github.com/aplazo/angular.control-tower-dashboard/commit/ecd1f61da0f8dea73f045a68eed75f90e68fb3e2))
* apm-2349 some improvements ([a7f7f5c](https://github.com/aplazo/angular.control-tower-dashboard/commit/a7f7f5cd6e0432c84e399fe24d7977b8cbcb7693))
* apm-2572 retrieve account data ([62a8752](https://github.com/aplazo/angular.control-tower-dashboard/commit/62a875253a9564ab8fbcf3ad10ed9fde21b8e40d))
* apm-2573 retrieve billing info ([201d7ca](https://github.com/aplazo/angular.control-tower-dashboard/commit/201d7cae6a68dea2ba528bf1d8e0ba2b7b77e3cb))
* apm-2575 change get mdr contract and service ([a02ab98](https://github.com/aplazo/angular.control-tower-dashboard/commit/a02ab98a691ae2848d4bce23b3108e9548dc4920))
* apm-2575 mdr component chores cause new contract ([8db1c80](https://github.com/aplazo/angular.control-tower-dashboard/commit/8db1c80abb516d9bbb7c158d6be06241e3d4d0a9))
* apm-2701 get users ([226235a](https://github.com/aplazo/angular.control-tower-dashboard/commit/226235a4f420f3fe2e341b6eb4e65ec9f7d26e0f))
* apm-2718 add updateAt to show contacts ([7d34f5e](https://github.com/aplazo/angular.control-tower-dashboard/commit/7d34f5eea097dcd4c6aa5010b3f032f831683df1))
* define login routes ([5d224aa](https://github.com/aplazo/angular.control-tower-dashboard/commit/5d224aaf642608797d104837a2059476c1a3a430))
* define login routes ([18367d8](https://github.com/aplazo/angular.control-tower-dashboard/commit/18367d870bb06c715428fd062b0458d38890c4b0))
* improve cohesion for login routes ([7bd9038](https://github.com/aplazo/angular.control-tower-dashboard/commit/7bd9038773f3e810b6a319d4c9dfe7f8db301a7b))
* improve cohesion for login routes ([fc28793](https://github.com/aplazo/angular.control-tower-dashboard/commit/fc28793aaefe4e41d99f426403c7147893ed5ba8))
* mac-135 customizable confirm report ([f9170bb](https://github.com/aplazo/angular.control-tower-dashboard/commit/f9170bbabed7185cd492707570174ec12cb1cf98))
* mexp-205 campaigns layout ([b928b33](https://github.com/aplazo/angular.control-tower-dashboard/commit/b928b33d8cce8e3f5ac76b26e143b2f621605bdd))
* mexp-208 ading missing servicesm usecases and fox some lints ([cbf24a9](https://github.com/aplazo/angular.control-tower-dashboard/commit/cbf24a98f459abccf175663c169d9f19e189dd22))
* mexp-208 fix some lint problems ([20783de](https://github.com/aplazo/angular.control-tower-dashboard/commit/20783de643aee74992955ec57189797adbea4ace))
* mexp-334 update environment configuration with const variables and type assertion ([1501bb1](https://github.com/aplazo/angular.control-tower-dashboard/commit/1501bb1c1a7e7ad0682726851dac391ea7e9b787))
* move user store as a root provider ([802e117](https://github.com/aplazo/angular.control-tower-dashboard/commit/802e117a64df442b89290d572576da7ed33c7356))
* move user store as a root provider ([670f2dc](https://github.com/aplazo/angular.control-tower-dashboard/commit/670f2dc5438d47e5e30ac29568fb0b37daf75000))
* Rename terms_conditions to termsConditions in campaign-related files ([a715096](https://github.com/aplazo/angular.control-tower-dashboard/commit/a7150966b14c7c558b50e5d1ee08cfda6f707b83))
