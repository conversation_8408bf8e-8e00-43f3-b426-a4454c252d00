{"[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": false}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true}, "[html][typescript]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}}