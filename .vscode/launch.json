{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "ng serve & launch Chrome",
      "type": "chrome", // or "msedge" if you prefer Edge
      "request": "launch",
      "preLaunchTask": "npm: dev", // Assumes your start script runs "ng serve"
      "url": "http://localhost:4200/", // Default Angular port
      "webRoot": "${workspaceFolder}",
      "sourceMaps": true,
      "skipFiles": [
        "${workspaceFolder}/node_modules/**/*.js",
        "<node_internals>/**/*.js"
      ]
    },
    {
      "name": "ng test & launch Chrome",
      "type": "chrome",
      "request": "launch",
      "preLaunchTask": "npm: test", // Assumes your test script runs "ng test"
      "url": "http://localhost:9876/debug.html", // Default Karma port
      "webRoot": "${workspaceFolder}",
      "sourceMaps": true,
      "skipFiles": [
        "${workspaceFolder}/node_modules/**/*.js",
        "<node_internals>/**/*.js"
      ]
    }
  ]
}
