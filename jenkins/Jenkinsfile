def slackResponse = slackSend(message: "Setting up Jenkins-<PERSON>...")
def VERSION_CHANGED = false
def RELEASE_VERSION = ""
def STG_IMAGE_TAG = ""
def PROD_IMAGE_TAG = ""

// Simplified deployment function that only handles the stack selection and update
def deployWithPulumi(projectName, environment, imageTag) {
  sh "pulumi stack select ${projectName}.${environment} --cwd services/${projectName}/"
  sh "pulumi up -f -y -c ECR_TAG=${imageTag} -C services/${projectName}/"
}

// Function to convert JSON content to .env format without exposing sensitive data to logs
def convertJsonToEnv(jsonFilePath, envFilePath = '.env') {
  // Use a separate script file to avoid command line exposure in logs
  writeFile file: 'convert_json_to_env.sh', text: '''#!/bin/bash
set -e
# Read JSON without printing to console
JSON_FILE="$1"
ENV_FILE="$2"

# Ensure the output file doesn't exist yet
rm -f "$ENV_FILE"

# Use jq to convert JSON to KEY=VALUE format without exposing values in logs
jq -r 'to_entries | map("\\(.key)=\\(.value)") | .[]' "$JSON_FILE" > "$ENV_FILE"

# Remove the JSON file to avoid leaving sensitive data
rm -f "$JSON_FILE"
'''

  // Make script executable and run it
  sh "chmod +x convert_json_to_env.sh"
  sh "./convert_json_to_env.sh '${jsonFilePath}' '${envFilePath}'"

  // Remove the script file
  sh "rm -f convert_json_to_env.sh"
}

pipeline {
  agent {
    node {
      label 'Jenkins-Slave-M'
    }
  }
  tools {
    nodejs 'node 22.13.1'
  }
  stages {
    stage('Prepare Environment') {
      steps {
        echo 'Getting secrets...'
        script {
          sh "curl -fsSL https://get.pulumi.com | sh"
          sh "$HOME/.pulumi/bin/pulumi version"
          PROJECT_NAME = env.JOB_NAME.tokenize("/").first()
          def userIds = slackUserIdsFromCommitters()
          def userIdsString = userIds.collect {
            "<@$it>"
          }.join(' ')
          def blocks = [
            ["type": "section", "text": ["type": "mrkdwn", "text": "*Project:* <$GIT_URL|$PROJECT_NAME>\n*Branch:* $GIT_BRANCH\n*Commit:* ${GIT_COMMIT.substring(0,7)}\n*Changes by* $userIdsString"]],
            ["type": "divider"]
          ]
          slackSend(channel: slackResponse.channelId, blocks: blocks, timestamp: slackResponse.ts)
          slackSend(channel: slackResponse.channelId, message: "Prepare Environment...", timestamp: slackResponse.ts)
        }
        sh "aws secretsmanager get-secret-value --region us-west-1 --secret-id 'Jenkins/$PROJECT_NAME/stg' --query SecretString --output text > envs.json"
      }
    }

    stage('Code Validation and Versioning') {
      when {
        not {
          changeRequest()
        }
      }
      stages {
        stage('Unit Tests') {
          steps {
            script {
              props = readJSON(file: "envs.json")
              slackSend(channel: slackResponse.channelId, color: "#24B0D5", message: "Running unit tests...", timestamp: slackResponse.ts)
              sh "aws secretsmanager get-secret-value --region us-west-1 --secret-id 'Service/$PROJECT_NAME/stg' --query SecretString --output text > secrets.json"
              // Convert JSON secrets.json file to proper KEY=VALUE format
              convertJsonToEnv('secrets.json')

              // Clear existing coverage directory
              sh 'rm -rf coverage || true'
              sh 'mkdir -p coverage'
            }

            echo "Running unit tests..."
            sh "docker-compose down shield_local_test || true"
            sh "docker-compose build --no-cache shield_local_test"
            sh "docker-compose run --rm shield_local_test"

            // Extract coverage data from the Docker volume
            sh '''
              # Get the volume name - use a more robust approach that doesn't rely on JOB_BASE_NAME
              # The volume name format is typically: shield-front_coverage_vol
              VOLUME_NAME=$(docker volume ls --filter name=coverage_vol -q | grep "${JOB_NAME}" | head -n 1 || docker volume ls --filter name=coverage_vol -q | grep "${PROJECT_NAME}" | head -n 1)

              # If still not found, try with a more generic approach
              if [ -z "$VOLUME_NAME" ]; then
                VOLUME_NAME=$(docker volume ls --filter name=coverage_vol -q | head -n 1)
              fi

              if [ -z "$VOLUME_NAME" ]; then
                echo "No matching coverage volume found"
                exit 1
              fi

              echo "Using coverage volume: $VOLUME_NAME"

              # Create a temporary container that mounts the existing volume
              TEMP_CONTAINER=$(docker create -v ${VOLUME_NAME}:/coverage_data alpine:latest sh)

              # Start the container
              docker start ${TEMP_CONTAINER}

              # Copy data from the volume to the Jenkins workspace
              docker cp ${TEMP_CONTAINER}:/coverage_data/. ./coverage/

              # Stop and remove the temporary container
              docker stop ${TEMP_CONTAINER}
              docker rm ${TEMP_CONTAINER}
            '''

            // List coverage files to verify extraction
            sh "ls -la ./coverage"

            // Don't remove the volume until after extraction is confirmed
            sh "docker-compose down shield_local_test"
            sh "docker volume rm \$(docker volume ls --filter name=coverage_vol -q | grep \"${PROJECT_NAME}\" | head -n 1 || docker volume ls --filter name=coverage_vol -q | head -n 1) || true"
          }
        }
        stage('SonarQube Analysis') {
          steps {
            script {
              slackSend(channel: slackResponse.channelId, color: "#E89E2E", message: "Running static analysis... ", timestamp: slackResponse.ts)

              // Get package.json version using Node.js
              def packageVersion = sh(script: 'node -e "console.log(require(\'./package.json\').version)"', returnStdout: true).trim()

              // Get Git branch name
              def gitBranch = sh(script: 'git name-rev --name-only HEAD', returnStdout: true).trim()
              def sonarVersion = gitBranch == 'master' ? "master-${packageVersion}" : "development-${packageVersion}"

              def scannerHome = tool 'SonarScanner';

              withSonarQubeEnv() {
                sh "${scannerHome}/bin/sonar-scanner -Dsonar.projectVersion=${sonarVersion}"
              }
            }
          }
        }
        stage('Quality Gate Check') {
          steps {
            sleep(15)
            timeout(time: 2, unit: 'MINUTES') {
              waitForQualityGate abortPipeline: true
            }
          }
        }

        stage('Version Management') {
          when {
            branch 'master'
          }
          steps {
            script {
              slackSend(channel: slackResponse.channelId, color: "#24B0D5", message: "📝 Starting version management process...", timestamp: slackResponse.ts)
              // Retrieve AWS secrets for GitHub token
              echo 'Retrieving GitHub token from AWS Secrets Manager'
              sh "aws secretsmanager get-secret-value --region us-west-1 --secret-id 'Service/$PROJECT_NAME/prod' --query SecretString --output text > secrets.json"

              // Extract specific variables needed for tools that require direct env vars
              def secretsMap = readJSON file: 'secrets.json'

              // Convert JSON secrets file to .env format for tools that can use it
              convertJsonToEnv('secrets.json')

              // Get current version before release
              def currentVersion = sh(script: 'node -e "console.log(require(\'./package.json\').version)"', returnStdout: true).trim()

              // Execute release command with both .env file and explicit environment variables
              withEnv(["GH_TOKEN=${secretsMap.FRONT_GH_TOKEN}", "AUTH_TOKEN=${secretsMap.AUTH_TOKEN}"]) {
                sh "npm ci --no-progress --loglevel=error --ignore-scripts && npm run release"
              }

              // Get new version after release
              def newVersion = sh(script: 'node -e "console.log(require(\'./package.json\').version)"', returnStdout: true).trim()

              // Check if version changed and create a flag file
              def versionChanged = currentVersion != newVersion

              if (versionChanged) {
                echo "Version changed from ${currentVersion} to ${newVersion}"
                // Create a flag file that will be stashed
                writeFile file: 'version_info.txt', text: "VERSION_CHANGED=true\nRELEASE_VERSION=${newVersion}"
                // Also set the global variables directly
                VERSION_CHANGED = true
                RELEASE_VERSION = newVersion
              } else {
                echo "Version remained the same: ${currentVersion}"
                writeFile file: 'version_info.txt', text: "VERSION_CHANGED=false\nRELEASE_VERSION=${currentVersion}"
                // Also set the global variables directly
                VERSION_CHANGED = false
                RELEASE_VERSION = currentVersion
              }

              // Stash the version info file for use in later stages
              stash name: 'version-info', includes: 'version_info.txt'

              slackSend(channel: slackResponse.channelId, color: "#24B0D5", message: "✅ Version management completed. Version changed: ${versionChanged}, New version: ${newVersion}", timestamp: slackResponse.ts)
            }
          }
        }
      }
    }

    stage('Check Version Change') {
      when {
        branch 'master'
      }
      steps {
        script {
          // Unstash the version info
          unstash 'version-info'

          // Read the version info file
          def versionInfo = readFile('version_info.txt').trim()
          echo "Version info: ${versionInfo}"
          echo "Version info contains 'VERSION_CHANGED=true': ${versionInfo.contains('VERSION_CHANGED=true')}"

          // Parse the version info - use a simpler, more direct approach
          if (versionInfo.contains('VERSION_CHANGED=true')) {
            VERSION_CHANGED = true
            echo "Setting VERSION_CHANGED to true because file contains VERSION_CHANGED=true"
          } else {
            VERSION_CHANGED = false
            echo "Setting VERSION_CHANGED to false because file does not contain VERSION_CHANGED=true"
          }

          // Extract release version - use a simpler approach
          def lines = versionInfo.split('\n')
          echo "Split lines: ${lines.size()} lines"
          for (int i = 0; i < lines.size(); i++) {
            echo "Line ${i}: ${lines[i]}"
            if (lines[i].startsWith('RELEASE_VERSION=')) {
              RELEASE_VERSION = lines[i].substring('RELEASE_VERSION='.length())
              echo "Found RELEASE_VERSION at line ${i}: ${RELEASE_VERSION}"
              break
            }
          }

          echo "Final VERSION_CHANGED: ${VERSION_CHANGED}"
          echo "Final RELEASE_VERSION: ${RELEASE_VERSION}"
        }
      }
    }

    stage('Deployment Pipeline') {
      when {
        allOf {
          branch 'master'
          expression {
            echo "Checking VERSION_CHANGED condition: current value is [${VERSION_CHANGED}]"
            echo "VERSION_CHANGED type: ${VERSION_CHANGED.getClass().getName()}"
            echo "VERSION_CHANGED equals true: ${VERSION_CHANGED == true}"
            return VERSION_CHANGED == true
          }
          not { changeRequest() }
        }
      }
      stages {
        stage('Build and Push Docker Images') {
          steps {
            script {
              echo "VERSION_CHANGED: ${VERSION_CHANGED}"
              echo "RELEASE_VERSION: ${RELEASE_VERSION}"

              // Use the version from the package.json
              def version = RELEASE_VERSION ?: "latest"
              echo "Using version: ${version}"

              def timestamp = sh(script: "date '+%d%m%y'", returnStdout: true).trim()

              // Login to ECR once
              echo 'Login to ECR'
              slackSend(channel: slackResponse.channelId, color: "#24B0D5", message: "🔑 Authenticating with ECR registry...", timestamp: slackResponse.ts)
              sh 'aws ecr get-login-password --region us-west-1 | docker login --username AWS --password-stdin 159200192518.dkr.ecr.us-west-1.amazonaws.com'

              // Build and push for staging
              def stgImageTag = "${version}-${timestamp}-${BUILD_NUMBER}-stg"
              slackSend(channel: slackResponse.channelId, color: "#24B0D5", message: "🏗️ Building and pushing images for staging environment...", timestamp: slackResponse.ts)

              echo "Retrieving secrets.json file for staging"
              sh "rm -f secrets.json && aws secretsmanager get-secret-value --region us-west-1 --secret-id 'Service/$PROJECT_NAME/stg' --query SecretString --output text > secrets.json"

              // Get staging environment configuration
              sh "aws secretsmanager get-secret-value --region us-west-1 --secret-id 'Jenkins/$PROJECT_NAME/stg' --query SecretString --output text > stg_envs.json"
              def stgProps = readJSON(file: "stg_envs.json")

              // Convert JSON secrets.json file to proper KEY=VALUE format
              convertJsonToEnv('secrets.json')

              echo "Building images for staging"
              sh "TAG_1=${stgProps.ECR_URL}:stg TAG_2=${stgProps.ECR_URL}:${stgImageTag} docker-compose build tagged1 tagged2"

              echo "Pushing images for staging"
              sh """docker push ${stgProps.ECR_URL}:stg
              docker push ${stgProps.ECR_URL}:${stgImageTag}"""

              // Store the staging image tag for later use in deployment
              env.STG_IMAGE_TAG = stgImageTag

              // Build and push for production
              def prodImageTag = "${version}-${timestamp}-${BUILD_NUMBER}-prod"
              slackSend(channel: slackResponse.channelId, color: "#24B0D5", message: "🏗️ Building and pushing images for production environment...", timestamp: slackResponse.ts)

              echo "Retrieving secrets.json file for production"
              sh "rm -f secrets.json && aws secretsmanager get-secret-value --region us-west-1 --secret-id 'Service/$PROJECT_NAME/prod' --query SecretString --output text > secrets.json"

              // Get production environment configuration
              sh "aws secretsmanager get-secret-value --region us-west-1 --secret-id 'Jenkins/$PROJECT_NAME/prod' --query SecretString --output text > prod_envs.json"
              def prodProps = readJSON(file: "prod_envs.json")

              // Convert JSON secrets.json file to proper KEY=VALUE format
              convertJsonToEnv('secrets.json')

              echo "Building images for production"
              sh "TAG_1=${prodProps.ECR_URL}:prod TAG_2=${prodProps.ECR_URL}:${prodImageTag} docker-compose build tagged1 tagged2"

              echo "Pushing images for production"
              sh """docker push ${prodProps.ECR_URL}:prod
              docker push ${prodProps.ECR_URL}:${prodImageTag}"""

              // Store the production image tag for later use in deployment
              env.PROD_IMAGE_TAG = prodImageTag

              // Also store in global variables for better persistence
              STG_IMAGE_TAG = stgImageTag
              PROD_IMAGE_TAG = prodImageTag
            }
          }
        }

        stage('Prepare Infrastructure') {
          environment {
            AWS_REGION = 'us-west-1'
            PULUMI_CONFIG_PASSPHRASE_FILE = 'passphrase'
          }
          steps {
            script {
              slackSend(channel: slackResponse.channelId, color: "#24B0D5", message: "🔧 Preparing infrastructure for deployment...", timestamp: slackResponse.ts)
            }
            git(url: 'https://github.com/aplazo/node.pulumi-infrastructure.git', branch: 'master', credentialsId: 'github')

            withEnv(["PATH+PULUMI=$HOME/.pulumi/bin"]) {
              // Do the npm install and touch passphrase once
              sh "cd services/$PROJECT_NAME && npm ci --also-dev && touch passphrase"
              // Login to Pulumi once
              sh "pulumi login s3://pulumi-apz-infra"
            }
          }
        }

        stage('Deploy to Staging') {
          environment {
            AWS_REGION = 'us-west-1'
            PULUMI_CONFIG_PASSPHRASE_FILE = 'passphrase'
          }
          steps {
            script {
              slackSend(channel: slackResponse.channelId, color: "#24B0D5", message: "🚀 Deploying to staging environment...", timestamp: slackResponse.ts)
              echo "Using staging image tag: ${STG_IMAGE_TAG ?: env.STG_IMAGE_TAG}"
            }
            withEnv(["PATH+PULUMI=$HOME/.pulumi/bin"]) {
              deployWithPulumi(PROJECT_NAME, "stg", STG_IMAGE_TAG ?: env.STG_IMAGE_TAG)
            }
          }
        }

        stage('Approve Production Deployment') {
          steps {
            script {
              slackSend(color: "#24B0D5", channel: slackResponse.channelId, message: "⏳ <$RUN_DISPLAY_URL|Waiting for production deployment approval>", timestamp: slackResponse.ts)
            }
            input(message: 'Approve production deployment?', ok: 'Yes, deploy to production')
          }
        }

        stage('Deploy to Production') {
          environment {
            AWS_REGION = 'us-west-1'
            PULUMI_CONFIG_PASSPHRASE_FILE = 'passphrase'
          }
          steps {
            script {
              slackSend(channel: slackResponse.channelId, color: "#24B0D5", message: "🚀 Deploying to production environment...", timestamp: slackResponse.ts)
              echo "Using production image tag: ${PROD_IMAGE_TAG ?: env.PROD_IMAGE_TAG}"
            }
            withEnv(["PATH+PULUMI=$HOME/.pulumi/bin"]) {
              deployWithPulumi(PROJECT_NAME, "prod", PROD_IMAGE_TAG ?: env.PROD_IMAGE_TAG)
            }
          }
        }
      }
    }
  }
  post {
    always {
      script {
        COLOR_MAP = ['SUCCESS': 'good', 'FAILURE': 'danger', 'UNSTABLE': 'danger', 'ABORTED': 'danger']
        slackSend(channel: slackResponse.channelId, color: COLOR_MAP[currentBuild.currentResult], message: "*${currentBuild.currentResult}:* <$RUN_DISPLAY_URL|Click here> for more info.", timestamp: slackResponse.ts)
      }
    }
  }
}
