// Define the type of the environment variables.
declare interface Env {
  readonly NG_APP_GOOGLE_CLIENT_ID: string;
  readonly NG_APP_API_BASE_URL: string;
  readonly NG_APP_BIFROST_URL: string;
  readonly NG_APP_I18N_URL: string;
  readonly NG_APP_LANDING_URL: string;
  readonly NG_APP_GATEWAY_URL: string;
  readonly NG_APP_PCUSTOM_CARD_URL: string;
  readonly NG_APP_FEATURE_FLAGS_API_KEY: string;
  readonly NG_APP_FEATURE_FLAGS_ENV: string;
}

// 1. Use import.meta.env.YOUR_ENV_VAR in your code. (conventional)
declare interface ImportMeta {
  readonly env: Env;
}
