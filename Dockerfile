### STAGE 1: Build ###
FROM node:22.13.1-slim AS build

ARG AUTH_TOKEN

ENV AUTH_TOKEN=$AUTH_TOKEN

WORKDIR /usr/src/app

RUN npm cache clean --force

COPY package*.json ./
COPY .npmrc ./
COPY .env ./

RUN npm ci --no-progress --loglevel=error --ignore-scripts

COPY . .

RUN node --run build

### STAGE 2: Run ###
FROM nginx:1.26.2-alpine3.20-slim
COPY ./nginx-custom.conf /etc/nginx/conf.d/default.conf
COPY ./nginx.conf /etc/nginx
COPY ./nginx-security-headers.conf /etc/nginx/security-headers.conf
COPY --from=build /usr/src/app/dist/helicarrier/browser/ /usr/share/nginx/html
EXPOSE 80
